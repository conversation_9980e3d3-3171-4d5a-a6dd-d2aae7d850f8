# 📡 PHÂN TÍCH API & ENDPOINTS - "ÔNG BA DẠY HÓA"

## 📋 TỔNG QUAN API SYSTEM

Dự án "Ông Ba Dạy Hóa" sử dụng kiến trúc **REST API** với Strapi làm backend và Axios làm HTTP client. Hệ thống API được chia thành các module chức năng chính:

```mermaid
graph TD
    A[📡 API Ecosystem] --> B[🖥️ Strapi Backend]
    A --> C[🌐 Frontend Client]
    A --> D[🔗 External Services]

    B --> E[📊 RESTful Endpoints]
    B --> F[🔐 Authentication]
    B --> G[📝 Content Management]

    C --> H[📱 Axios HTTP Client]
    C --> I[🍪 Cookie Management]
    C --> J[🔄 Request Interceptors]

    D --> K[💳 PayOS Payment]
    D --> L[📧 Brevo Email]
    D --> M[☁️ AWS S3 Storage]

    N[🔧 API Features] --> O[📋 Auto-generated APIs]
    N --> P[🎯 Custom Endpoints]
    N --> Q[📊 Query Parameters]
    N --> R[🔄 Population & Filtering]
```

---

## 🏗️ API ARCHITECTURE

### Strapi API Structure

```mermaid
flowchart TD
    A[🌐 HTTP Request] --> B[🛡️ CORS Middleware]
    B --> C[🔐 Authentication]
    C --> D[📝 Content Type Routes]
    D --> E[🎯 Controller Logic]
    E --> F[⚙️ Service Layer]
    F --> G[🗄️ Database Queries]
    G --> H[📤 JSON Response]

    I[🔧 Custom Logic] --> D
    J[📧 Email Integration] --> F
    K[💳 Payment Processing] --> F
    L[☁️ File Storage] --> F

    M[📊 API Features] --> N[📋 Filtering]
    M --> O[🔄 Sorting]
    M --> P[📄 Pagination]
    M --> Q[🌐 Population]
```

### API Categories Overview

```mermaid
mindmap
  root((📡 API Categories))
    Authentication
      Local Login
      Google OAuth
      User Registration
      OTP Verification
      Password Reset
    Content Management
      Courses & Chapters
      Knowledge Sections
      Exercises & Quizzes
      Blog Posts
      Video Features
    E-commerce
      Orders & Payments
      Voucher System
      Payment Verification
      Transaction History
    Learning Features
      Streak System
      Progress Tracking
      Quiz Results
      Student Rankings
    Communication
      Email Templates
      OTP System
      Notifications
      Form Submissions
    Media & Assets
      File Upload
      Image Processing
      AWS S3 Integration
      Asset Management
```

---

## 🔐 AUTHENTICATION APIs

### Authentication Flow

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 User
    participant F as 🌐 Frontend
    participant B as 🖥️ Backend API
    participant D as 🗄️ Database

    U->>F: Submit Login Form
    F->>B: POST /api/auth/local
    B->>D: Find User by Email
    D-->>B: User Data
    B->>B: Validate Password
    B->>B: Generate JWT Token
    B-->>F: JWT + User Data
    F->>F: Store in Cookie
    F-->>U: Redirect to Dashboard
```

### Authentication Endpoints

| Endpoint | Method | Purpose | Request | Response |
|----------|--------|---------|---------|----------|
| `/auth/local` | POST | Local login | `{email, password}` | `{jwt, user, authprovider}` |
| `/auth/signup` | POST | User registration | `{email, password, fullname, ...}` | `{jwt, user, authprovider}` |
| `/auth/login` | POST | Google OAuth login | `{email, provider, googleToken}` | `{jwt, user, authprovider}` |
| `/auth/reset-password` | POST | Password reset | `{email, code, password}` | `{message}` |
| `/auth/profile/{id}` | PUT | Update profile | `{fullname, phone, ...}` | `{user}` |
| `/auth/find-by-email` | GET | Find user by email | `{email}` | `{user}` |
| `/auth/update-password` | PUT | Update password | `{email, password}` | `{message}` |

### Custom Authentication Controller

```typescript
// src/api/auth/controllers/auth.ts
export default {
  async login(ctx) {
    const { provider, email, password, provider_id, googleToken } = ctx.request.body;

    if (provider === "local") {
      const user = await strapi.query('plugin::users-permissions.user').findOne({ where: { email } });
      const validPassword = await strapi.plugins['users-permissions'].services.user.validatePassword(password, user.password);

      if (!validPassword) {
        return ctx.badRequest('Invalid credentials');
      }
    }

    if (provider === "google") {
      let user = await strapi.query('plugin::users-permissions.user').findOne({ where: { email } });

      if (!user) {
        // Create new Google user
        user = await strapi.plugins["users-permissions"].services.user.add({
          email,
          username: email,
          provider: "external",
          confirmed: true,
          googleToken,
          googleId: provider_id
        });
      }
    }

    const token = strapi.plugins["users-permissions"].services.jwt.issue({ id: user.id });
    const sanitizedUser = await strapi.plugins["users-permissions"].services.user.fetch(user.id);

    return ctx.send({
      jwt: token,
      user: sanitizedUser,
      authprovider: authProvider
    });
  }
};
```

---

## 📚 COURSE MANAGEMENT APIs

### Course Data Flow

```mermaid
flowchart TD
    A[📚 Course Request] --> B[🏫 Course Controller]
    B --> C[📖 Get Course Data]
    C --> D[📚 Populate Chapters]
    D --> E[📝 Populate Exercises]
    E --> F[🖼️ Populate Images]
    F --> G[📤 Return Course Data]

    H[🔄 Related Data] --> I[👥 Course Tiers]
    H --> J[🏷️ Categories]
    H --> K[📊 Course Features]

    L[📊 Filtering] --> M[🎯 By Grade]
    L --> N[📅 By Status]
    L --> O[🔍 By Search Term]
```

### Course Endpoints

| Endpoint | Method | Purpose | Parameters | Population |
|----------|--------|---------|------------|------------|
| `/courses` | GET | Get all courses | `filters`, `populate`, `sort` | `image`, `course_tiers`, `chapters` |
| `/courses/{id}` | GET | Get course by ID | `populate` | `chapters`, `exercises`, `features` |
| `/grades` | GET | Get courses by grade | `gradeId` | `course.chapters`, `course.image` |

### Course API Implementation

```javascript
// Frontend - app/api/strapi.js
const courses = {
  getCourseBySlug: async (slug, gradeId = null, token = null) => {
    let filters = {
      slug: { $eq: slug },
    };

    if (gradeId) {
      filters.gradeId = { $eq: gradeId };
    }

    const response = await instance.get("/courses", {
      params: {
        filters,
        populate: {
          image: true,
          course_tiers: true,
          faqs: true,
          features: true,
          class_schedules: true,
          chapters: {
            populate: ["knowledgeSections", "exercises"],
            sort: "order:asc",
          },
        },
      },
    });

    return response.data.data[0];
  }
};
```

---

## 💳 PAYMENT & ORDER APIs

### Payment Flow

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 User
    participant F as 🌐 Frontend
    participant B as 🖥️ Backend
    participant P as 💳 PayOS
    participant D as 🗄️ Database

    U->>F: Click Buy Course
    F->>B: POST /orders
    B->>D: Create Order
    D-->>B: Order ID
    B->>P: Create Payment Link
    P-->>B: Payment URL
    B-->>F: Redirect to PayOS
    F-->>U: PayOS Payment Page

    U->>P: Complete Payment
    P->>B: Webhook Notification
    B->>D: Update Order Status
    B->>B: Send Confirmation Email
    B-->>U: Success Page
```

### Payment Endpoints

| Endpoint | Method | Purpose | Request | Response |
|----------|--------|---------|---------|----------|
| `/orders` | POST | Create order | `{courseId, amount, userId}` | `{order, paymentUrl}` |
| `/payments/create-payment-link` | POST | Create PayOS link | `{orderId, amount, description}` | `{paymentUrl}` |
| `/payments/verify` | POST | Verify payment | `{orderCode}` | `{status, order}` |
| `/payments/complete` | POST | Complete payment | `{orderCode}` | `{success}` |
| `/vouchers/validate` | POST | Validate voucher | `{code, courseId}` | `{valid, discount}` |

### Payment Integration

```javascript
// Payment API Implementation
const payment = {
  createPaymentLink: async (paymentData) => {
    const amount = Math.max(2000, Math.round(Number(paymentData.amount)));

    const requestData = {
      orderId: paymentData.orderId.toString(),
      amount: amount,
      description: paymentData.description || `Thanh toán đơn hàng #${paymentData.orderId}`,
    };

    const response = await instance.post("/payments/create-payment-link", requestData);

    const paymentUrl = response.data.paymentUrl || response.data.checkoutUrl;
    if (!paymentUrl) {
      throw new Error("Missing payment URL in response");
    }

    return {
      paymentUrl: paymentUrl,
      checkoutUrl: paymentUrl,
    };
  },

  verifyPayment: async (orderCode) => {
    const response = await instance.post("/payments/verify", { orderCode });
    return response.data;
  }
};
```

---

## 📧 COMMUNICATION APIs

### OTP System Flow

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 User
    participant F as 🌐 Frontend
    participant B as 🖥️ Backend
    participant E as 📧 Email Service

    U->>F: Request OTP
    F->>B: POST /send-otp
    B->>B: Generate OTP Code
    B->>E: Send Email with OTP
    E-->>U: OTP Email
    U->>F: Enter OTP
    F->>B: POST /verify-otp
    B->>B: Validate OTP
    B-->>F: Verification Result
    F-->>U: Success/Error Message
```

### Communication Endpoints

| Endpoint | Method | Purpose | Request | Response |
|----------|--------|---------|---------|----------|
| `/send-otp` | POST | Send OTP email | `{email}` | `{success}` |
| `/verify-otp` | POST | Verify OTP code | `{email, code}` | `{valid}` |
| `/offline-forms` | POST | Submit contact form | `{name, email, message}` | `{success}` |
| `/send-payment-confirmation` | POST | Send payment email | `{emailDetails}` | `{success}` |

### OTP Implementation

```javascript
// OTP API Implementation
const sendOTP = {
  send: async (email) => {
    const instance = createStrapiClient();
    const response = await instance.post("/send-otp", { email });
    return response.data;
  },

  verify: async (email, code) => {
    const instance = createStrapiClient();
    const response = await instance.post("/verify-otp", {
      email,
      code: code.toString(),
    });
    return response.data;
  },
};
```

---

## 🎯 LEARNING FEATURES APIs

### Streak System Flow

```mermaid
flowchart TD
    A[🎯 Start Streak] --> B[⚙️ Streak Controller]
    B --> C[📊 Get Questions]
    C --> D[⏰ Start Timer]
    D --> E[📝 Process Answers]
    E --> F[📊 Calculate Score]
    F --> G[💾 Update Progress]
    G --> H[🏆 Update Rankings]
    H --> I[📧 Send Notifications]

    J[📊 Data Sources] --> K[❓ Streak Questions]
    J --> L[📝 Questions Answers]
    J --> M[🏆 User Rankings]
```

### Learning Endpoints

| Endpoint | Method | Purpose | Parameters | Features |
|----------|--------|---------|------------|----------|
| `/streaks` | POST | Create streak session | `{userId, streakQuestionId}` | Progress tracking |
| `/questions-answers` | POST | Submit answer | `{userId, questionId, answer}` | Answer validation |
| `/streak-questions` | GET | Get questions | `{courseId, date}` | Daily questions |
| `/reports` | POST | Submit report | `{userId, streakId, report}` | Issue reporting |

### Streak API Implementation

```javascript
// Streak API Implementation
const streak = {
  getDataByUser: async (data) => {
    const instance = createStrapiClient();
    return await instance.post(`/streaks/get-streak-by-user`, data);
  },

  createStreak: async (data) => {
    const instance = createStrapiClient();
    return await instance.post(`/streaks`, { data: data });
  },

  getQuestionByStreak: async (streakId) => {
    const instance = createStrapiClient();
    return await instance.get(`/questions`, {
      params: {
        filters: {
          streak_question: { id: { $eq: streakId } },
          question_status: { $eq: 'approval' }
        },
        sort: ['exercise_type.point:asc'],
        populate: {
          exercise_type: { fields: ['point'] }
        }
      }
    });
  }
};
```

---

## 📝 CONTENT MANAGEMENT APIs

### Blog System Flow

```mermaid
flowchart TD
    A[📝 Blog Request] --> B[📋 Blog Controller]
    B --> C[📚 Get Blog Posts]
    C --> D[🖼️ Populate Images]
    D --> E[📊 Populate Sections]
    E --> F[🏷️ Populate Categories]
    F --> G[📤 Return Blog Data]

    H[🔍 Search & Filter] --> I[📅 By Date]
    H --> J[🏷️ By Category]
    H --> K[🔍 By Keyword]

    L[📊 Pagination] --> M[📄 Page Size]
    L --> N[📍 Current Page]
    L --> O[📊 Total Count]
```

### Content Endpoints

| Endpoint | Method | Purpose | Parameters | Population |
|----------|--------|---------|------------|------------|
| `/blog-posts` | GET | Get all posts | `populate`, `sort`, `pagination` | `featuredImage`, `sections` |
| `/blog-posts/{id}` | GET | Get post by ID | `populate` | `sections.image`, `categories` |
| `/video-features` | GET | Get video features | `populate`, `filters` | `courses`, `video_data` |

### Content API Implementation

```javascript
// Blog API Implementation
const blog = {
  getAllPosts: async (token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/blog-posts", {
      params: {
        populate: "featuredImage",
        sort: "date:desc",
      },
    });
    return response.data;
  },

  getPostBySlug: async (slug, token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/blog-posts", {
      params: {
        filters: {
          slug: { $eq: slug },
        },
        populate: {
          featuredImage: true,
          sections: {
            populate: ["image"],
            sort: "order:asc",
          },
        },
      },
    });

    return response.data.data[0];
  }
};
```

---

## 📊 API QUERY FEATURES

### Query Parameters

```mermaid
mindmap
  root((🔍 Query Parameters))
    Filtering
      Exact Match
      Range Queries
      Text Search
      Relationship Filters
    Sorting
      Single Field
      Multiple Fields
      Ascending/Descending
      Custom Sort
    Pagination
      Page Size
      Current Page
      Start Index
      Total Count
    Population
      Single Relations
      Multiple Relations
      Nested Population
      Field Selection
    Search
      Full-text Search
      Fuzzy Matching
      Highlighting
      Relevance Scoring
```

### Advanced Query Examples

```javascript
// Complex filtering and population
const response = await instance.get("/courses", {
  params: {
    // Filtering
    filters: {
      gradeId: { $eq: "10" },
      price: { $lt: 500000 },
      title: { $contains: "hóa" }
    },

    // Sorting
    sort: ["createdAt:desc", "price:asc"],

    // Pagination
    pagination: {
      page: 1,
      pageSize: 10,
      withCount: true
    },

    // Population with nested fields
    populate: {
      image: true,
      course_tiers: true,
      chapters: {
        populate: {
          knowledgeSections: true,
          exercises: true
        },
        sort: "order:asc"
      }
    },

    // Field selection
    fields: ["title", "slug", "description", "price"]
  }
});
```

---

## 🔧 API ERROR HANDLING

### Error Response Structure

```mermaid
flowchart TD
    A[🚨 API Error] --> B{Error Type}
    B -->|Validation| C[📝 400 Bad Request]
    B -->|Authentication| D[🔐 401 Unauthorized]
    B -->|Authorization| E[🚫 403 Forbidden]
    B -->|Not Found| F[🔍 404 Not Found]
    B -->|Server Error| G[⚙️ 500 Internal Server]

    C --> H[📋 Field Validation]
    D --> I[🔑 Invalid Token]
    E --> J[👤 Insufficient Permissions]
    F --> K[📄 Resource Not Found]
    G --> L[🔧 System Error]

    M[📊 Error Response] --> N[⚠️ Error Message]
    M --> O[🔍 Error Details]
    M --> P[📋 Validation Errors]
    M --> Q[🆔 Error Code]
```

### Error Handling Implementation

```javascript
// Axios response interceptor
instance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;

      switch (status) {
        case 400:
          console.error('Validation Error:', data.error.details);
          break;
        case 401:
          // Handle authentication error
          logout();
          redirectToLogin();
          break;
        case 403:
          console.error('Permission Denied:', data.error.message);
          break;
        case 404:
          console.error('Resource Not Found:', data.error.message);
          break;
        case 500:
          console.error('Server Error:', data.error.message);
          break;
        default:
          console.error('API Error:', data.error.message);
      }
    } else if (error.request) {
      // Network error
      console.error('Network Error:', error.message);
    } else {
      // Other error
      console.error('Request Error:', error.message);
    }

    return Promise.reject(error);
  }
);
```

---

## 📈 API PERFORMANCE & OPTIMIZATION

### Performance Optimization

```mermaid
mindmap
  root((⚡ API Performance))
    Database Optimization
      Query Indexing
      Connection Pooling
      Query Caching
      Efficient Joins
    Response Optimization
      Data Compression
      Selective Fields
      Pagination
      HTTP Caching
    Load Balancing
      Request Distribution
      Auto Scaling
      Health Checks
      Failover
    Monitoring
      Response Times
      Error Rates
      Throughput
      Resource Usage
```

### Caching Strategy

```mermaid
flowchart TD
    A[📡 API Request] --> B{Is Cached?}
    B -->|Yes| C[📦 Return Cached Data]
    B -->|No| D[🔄 Process Request]
    D --> E[📊 Query Database]
    E --> F[📝 Process Data]
    F --> G[💾 Cache Result]
    G --> H[📤 Return Data]

    I[📋 Cache Types] --> J[⚡ Response Cache]
    I --> K[🗄️ Database Query Cache]
    I --> L[📊 Static Data Cache]

    M[⏰ Cache Expiry] --> N[📅 Time-based]
    M --> O[🔄 Event-based]
    M --> P[📏 Size-based]
```

---

## 🔒 API SECURITY

### Security Layers

```mermaid
flowchart TD
    A[🌐 API Security] --> B[🛡️ CORS Policy]
    A --> C[🔐 Authentication]
    A --> D[📝 Input Validation]
    A --> E[🔒 Rate Limiting]
    A --> F[📊 Logging]

    B --> G[🌐 Allowed Origins]
    B --> H[📋 Allowed Headers]
    B --> I[🔄 Preflight Requests]

    C --> J[JWT Tokens]
    C --> K[🔑 API Keys]
    C --> L[🔐 OAuth 2.0]

    D --> M[📋 Schema Validation]
    D --> N[🧹 Data Sanitization]
    D --> O[📏 Length Limits]

    E --> P[⏱️ Request Limits]
    E --> Q[👥 User Limits]
    E --> R[🌐 IP Limits]
```

### Security Implementation

```typescript
// Rate limiting configuration
{
  name: 'strapi::rateLimit',
  config: {
    routes: {
      '/api/auth/*': {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 5, // limit each IP to 5 requests per windowMs
        message: 'Too many authentication attempts, please try again later.'
      },
      '/api/*': {
        windowMs: 15 * 60 * 1000,
        max: 100,
        message: 'Too many requests, please try again later.'
      }
    }
  }
}
```

---

## 📊 API MONITORING & ANALYTICS

### API Monitoring Dashboard

```mermaid
flowchart TD
    A[📊 API Monitoring] --> B[📈 Request Metrics]
    A --> C[⚠️ Error Tracking]
    A --> D[⏱️ Performance]
    A --> E[👥 Usage Analytics]

    B --> F[📊 Requests/Second]
    B --> G[📈 Success Rate]
    B --> H[🌐 Geographic Distribution]

    C --> I[📋 Error Types]
    C --> J[🔍 Error Frequency]
    C --> K[🚨 Alert System]

    D --> L[⏱️ Response Times]
    D --> M[📦 Payload Sizes]
    D --> N[🖥️ Server Resources]

    E --> O[👤 Active Users]
    E --> P[📡 Popular Endpoints]
    E --> Q[📊 Usage Patterns]
```

---

## 🎯 CONCLUSION

API system của dự án "Ông Ba Dạy Hóa" được thiết kế với:

- **🏗️ Kiến trúc RESTful**: Strapi auto-generated + Custom endpoints
- **🔐 Security mạnh**: JWT, CORS, Rate limiting, Input validation
- **⚡ Performance tối ưu**: Query optimization, Caching, Pagination
- **📊 Monitoring đầy đủ**: Error tracking, Performance metrics
- **🔄 Flexibility**: Advanced filtering, Population, Custom logic
- **📱 Mobile-ready**: Optimized for mobile app integration

**API system cung cấp nền tảng vững chắc cho toàn bộ ứng dụng! 🚀**
