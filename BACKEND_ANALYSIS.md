# 🖥️ PHÂN TÍCH BACKEND (STRAPI) - "ÔNG BA DẠY HÓA"

## 📋 TỔNG QUAN BACKEND

Backend của dự án "Ông Ba Dạy Hóa" được xây dựng trên **Strapi 5** - một headless CMS mạnh mẽ với kiến trúc API-first. Strapi cung cấp:

```mermaid
graph TD
    A[🖥️ Strapi Backend] --> B[📡 REST API]
    A --> C[🔐 Authentication]
    A --> D[📊 Content Management]
    A --> E[🔧 Custom Logic]
    A --> F[📧 External Services]

    B --> G[🏫 Course API]
    B --> H[👥 User API]
    B --> I[💳 Payment API]
    B --> J[📝 Blog API]

    C --> K[JWT Tokens]
    C --> L[Role-Based Access]
    C --> M[Google OAuth]

    D --> N[📝 Content Types]
    D --> O[📋 Admin Panel]
    D --> P[🔄 Auto-generated APIs]

    E --> Q[🎯 Custom Controllers]
    E --> R[⚙️ Business Services]
    E --> S[📊 Data Processing]

    F --> T[💰 PayOS Integration]
    F --> U[📧 Brevo Email]
    F --> V[☁️ AWS S3 Storage]
```

---

## 🏗️ KIẾN TRÚC STRAPI

### Strapi Architecture Overview

```mermaid
flowchart TD
    A[🌐 HTTP Request] --> B[🔐 Authentication Middleware]
    B --> C{Is Authenticated?}
    C -->|Yes| D[📝 Content Type Router]
    C -->|No| E[🚫 Access Denied]

    D --> F[🎯 Controller]
    F --> G[⚙️ Service]
    G --> H[🗄️ Database Model]
    H --> I[📤 JSON Response]

    J[🎨 Admin Panel] --> K[📊 Content Manager]
    K --> L[👥 User Manager]
    L --> M[🔐 Permissions Manager]

    N[🔧 Custom Code] --> F
    O[📧 Email Service] --> G
    P[💳 Payment API] --> G
```

### Strapi Layer Structure

```mermaid
graph TD
    subgraph "Presentation Layer"
        A[📊 Admin Panel] --> B[🎨 Content Manager UI]
        B --> C[👥 User Management UI]
    end

    subgraph "API Layer"
        D[📡 REST API Endpoints] --> E[🔐 Authentication]
        E --> F[📝 Content Type Routes]
        F --> G[🎯 Custom Routes]
    end

    subgraph "Business Logic Layer"
        H[🎯 Controllers] --> I[⚙️ Services]
        I --> J[🔄 Business Rules]
    end

    subgraph "Data Layer"
        K[🗄️ Database Models] --> L[📊 ORM Queries]
        L --> M[💾 MySQL Database]
    end

    subgraph "External Integration"
        N[📧 Email Service] --> I
        O[💳 Payment Gateway] --> I
        P[☁️ File Storage] --> I
    end
```

---

## 📂 CẤU TRÚC THƯ MỤC BACKEND

### Core Structure

```mermaid
graph TD
    A[backend/] --> B[📦 package.json]
    A --> C[⚙️ src/]
    A --> D[🔧 config/]
    A --> E[🗄️ database/]
    A --> F[📧 email-templates/]
    A --> G[🌐 public/]

    B --> H[📚 Dependencies]
    B --> I[🔄 Scripts]

    C --> J[📡 api/]
    C --> K[🔧 middlewares/]
    C --> L[📊 types/]
    C --> M[🚀 index.ts]

    D --> N[🗄️ database.ts]
    D --> O[🔐 middlewares.ts]
    D --> P[📡 server.ts]
    D --> Q[👥 admin.ts]
```

### API Structure Detail

```mermaid
mindmap
  root((📡 API Structure))
    Authentication
      JWT Token System
      Google OAuth
      User Registration
    Content Management
      Course Management
      Blog Posts
      Chapters & Lessons
      Exercises & Quizzes
    E-commerce
      Order Processing
      Payment Integration
      Voucher System
    Learning Features
      Streak System
      Progress Tracking
      Quiz Questions
      Student Rankings
    Communication
      OTP System
      Email Templates
      Notifications
    Media & Assets
      File Upload
      AWS S3 Integration
      Image Processing
```

---

## 🔧 CẤU HÌNH HỆ THỐNG

### Database Configuration

```typescript
// config/database.ts
export default ({ env }) => ({
  connection: {
    client: 'mysql',
    connection: {
      host: env('DATABASE_HOST'),
      port: env.int('DATABASE_PORT'),
      database: env('DATABASE_NAME'),
      user: env('DATABASE_USERNAME'),
      password: env('DATABASE_PASSWORD'),
      ssl: env.bool('DATABASE_SSL'),
      connectTimeout: 10000,
    },
    options: {
      useNullAsDefault: true, // Custom primary key support
    },
  },
});
```

### Server Configuration

```typescript
// config/server.ts
export default ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  app: {
    keys: env.array('APP_KEYS'),
  },
});
```

### Security & CORS Configuration

```typescript
// config/middlewares.ts
export default [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'https:'],
          'img-src': ["'self'", 'data:', 'blob:', 'https:'],
          'media-src': ["'self'", 'data:', 'blob:', 'https:'],
          'frame-src': ["'self'", 'https://accounts.google.com'],
          upgradeInsecureRequests: null,
        },
      },
      // CORS settings
      crossOriginEmbedderPolicy: false,
      crossOriginOpenerPolicy: false,
      crossOriginResourcePolicy: false,
    },
  },
  {
    name: 'strapi::cors',
    config: {
      origin: process.env.CORS_ORIGINS ?
        process.env.CORS_ORIGINS.split(',') :
        ['http://localhost:3000', 'https://ongbadayhoa.com'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
      headers: ['Content-Type', 'Authorization', 'Origin', 'Accept', 'X-Requested-With'],
      maxAge: 86400,
    },
  },
  // File upload limits
  {
    name: 'strapi::body',
    config: {
      multipart: true,
      maxFileSize: ********, // 50MB
    },
  },
];
```

---

## 🔐 HỆ THỐNG XÁC THỰC

### Authentication Flow

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 User
    participant F as 🌐 Frontend
    participant B as 🖥️ Backend API
    participant D as 🗄️ Database

    U->>F: Login Request
    F->>B: POST /api/auth/local
    B->>B: Validate Credentials
    B->>D: Query User Data
    D-->>B: User Found
    B->>B: Generate JWT Token
    B-->>F: JWT + User Data
    F->>F: Store in Cookie
    F-->>U: Login Success
```

### Custom Authentication Controller

```typescript
// src/api/auth/controllers/auth.ts
export default {
  async login(ctx) {
    const { provider, email, password, provider_id, googleToken } = ctx.request.body;

    if (provider === "local") {
      // Local authentication logic
      const user = await strapi.query('plugin::users-permissions.user').findOne({ where: { email } });
      const validPassword = await strapi.plugins['users-permissions'].services.user.validatePassword(password, user.password);

      if (!validPassword) {
        return ctx.badRequest('Invalid credentials');
      }
    } else if (provider === "google") {
      // Google OAuth logic
      if (!user) {
        // Create new user for Google login
        user = await strapi.plugins["users-permissions"].services.user.add({
          email,
          username: email,
          provider: "external",
          confirmed: true,
          googleToken,
          googleId: provider_id
        });
      }
    }

    // Create auth provider record
    const authProvider = await strapi.query('api::user-auth-provider.user-auth-provider').create({
      data: {
        user: user.id,
        provider,
        provider_id: provider_id || email,
        publishedAt: new Date()
      }
    });

    // Generate JWT token
    const token = strapi.plugins["users-permissions"].services.jwt.issue({ id: user.id });

    return ctx.send({
      jwt: token,
      user: sanitizedUser,
      authprovider: authProvider
    });
  },

  async signup(ctx) {
    // Custom signup logic with additional fields
    const { email, password, fullname, phone, gender, date, address } = ctx.request.body;

    // Create user with extended profile
    const user = await strapi.plugins["users-permissions"].services.user.add({
      email,
      username: email,
      password,
      provider: 'local',
      confirmed: true,
      fullname,
      phone,
      gender,
      date,
      address
    });

    const token = strapi.plugins["users-permissions"].services.jwt.issue({ id: user.id });

    return ctx.send({
      jwt: token,
      user: sanitizedUser
    });
  }
};
```

---

## 📡 API ENDPOINTS CHI TIẾT

### Core API Categories

| Category | Purpose | Key Endpoints |
|----------|---------|---------------|
| **Authentication** | User management | `/auth/login`, `/auth/signup`, `/auth/profile` |
| **Courses** | Course management | `/courses`, `/chapters`, `/knowledge-sections` |
| **Learning** | Educational content | `/exercises`, `/quiz-questions`, `/streak-questions` |
| **Commerce** | E-commerce | `/orders`, `/payments`, `/vouchers` |
| **Communication** | Messaging | `/send-otp`, `/offline-forms` |
| **Content** | Blog & media | `/blog-posts`, `/video-features`, `/uploads` |

### API Structure Analysis

```mermaid
graph TD
    subgraph "API Categories"
        A[🔐 Authentication] --> B[👤 User Management]
        A --> C[🔑 JWT Token System]

        D[📚 Learning Content] --> E[📖 Courses & Chapters]
        D --> F[📝 Exercises & Quizzes]
        D --> G[🎯 Streak System]

        H[💰 E-commerce] --> I[🛒 Order Processing]
        H --> J[💳 Payment Integration]
        H --> K[🎫 Voucher System]

        L[📧 Communication] --> M[📱 OTP System]
        L --> N[📧 Email Services]
        L --> O[📋 Form Submissions]
    end

    subgraph "Data Relations"
        P[👥 Users] --> Q[📚 Courses]
        Q --> R[📖 Chapters]
        R --> S[📝 Exercises]
        P --> T[💰 Orders]
        T --> U[💳 Payments]
    end
```

### Content Types & Relationships

```mermaid
erDiagram
    USER ||--o{ ORDER : places
    USER ||--o{ STREAK : participates
    USER ||--o{ QUESTIONS-ANSWER : submits

    COURSE ||--|{ CHAPTER : contains
    CHAPTER ||--|{ KNOWLEDGE-SECTION : has
    CHAPTER ||--|{ EXERCISE : includes

    COURSE ||--|{ COURSE-TIER : has
    ORDER ||--|| COURSE : for
    ORDER ||--|| COURSE-TIER : specifies

    STREAK-QUESTION ||--|{ QUESTION : contains
    STREAK-QUESTION ||--|{ QUESTIONS-ANSWER : receives

    BLOG-POST ||--|{ BLOG-CONTENT-SECTION : has
    VIDEO-FEATURE ||--o{ COURSE : enhances

    VOUCHER ||--o{ ORDER : applies
    ACTIVATION-CODE ||--o{ ORDER : activates
```

---

## 🔧 CUSTOM BUSINESS LOGIC

### Payment Integration

```mermaid
flowchart TD
    A[💳 Payment Request] --> B[🎯 Payment Controller]
    B --> C[📊 Validate Order]
    C --> D[🔗 Call PayOS API]
    D --> E[📝 Create Payment Link]
    E --> F[💾 Store Order Status]
    F --> G[📧 Send Confirmation]
    G --> H[📤 Return Payment URL]
```

### Streak System Logic

```mermaid
flowchart TD
    A[🎯 Start Streak] --> B[⚙️ Streak Controller]
    B --> C[📊 Check User Eligibility]
    C --> D[📝 Get Questions]
    D --> E[⏰ Start Timer]
    E --> F[📊 Process Answers]
    F --> G[📈 Calculate Score]
    G --> H[💾 Update Progress]
    H --> I[🏆 Award Points]
    I --> J[📊 Update Rankings]
```

### OTP System

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 User
    participant F as 🌐 Frontend
    participant B as 🖥️ Backend
    participant E as 📧 Email Service

    U->>F: Request Password Reset
    F->>B: POST /send-otp
    B->>B: Generate OTP Code
    B->>E: Send OTP Email
    E-->>U: OTP Email Received
    U->>F: Enter OTP Code
    F->>B: POST /verify-otp
    B->>B: Validate OTP
    B-->>F: Verification Success
    F->>U: Proceed to Reset Password
```

---

## 📊 DATABASE SCHEMA

### Main Tables Structure

```sql
-- Users Table (from users-permissions plugin)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(255),
    password VARCHAR(255),
    provider VARCHAR(100),
    confirmed BOOLEAN DEFAULT FALSE,
    blocked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Courses Table
CREATE TABLE courses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    grade VARCHAR(50),
    published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Orders Table
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_amount DECIMAL(10,2),
    payment_status ENUM('pending', 'completed', 'failed', 'cancelled'),
    payos_order_code VARCHAR(255),
    discount_amount DECIMAL(10,2) DEFAULT 0,
    voucher_code VARCHAR(100),
    user_id INT,
    course_id INT,
    course_tier_id INT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (course_id) REFERENCES courses(id)
);

-- Streak System Tables
CREATE TABLE streak_questions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT,
    value DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id)
);

CREATE TABLE streaks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    streak_question_id INT,
    is_join BOOLEAN DEFAULT FALSE,
    time INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (streak_question_id) REFERENCES streak_questions(id)
);

-- OTP System
CREATE TABLE otps (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL,
    code VARCHAR(10) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Database Relationships

```mermaid
graph TD
    A[👥 Users] --> B[💰 Orders]
    A --> C[🎯 Streaks]
    A --> D[📝 Questions Answers]

    B --> E[📚 Courses]
    B --> F[🏷️ Course Tiers]

    E --> G[📖 Chapters]
    G --> H[📚 Knowledge Sections]
    G --> I[📝 Exercises]

    J[🎯 Streak Questions] --> K[❓ Questions]
    K --> D

    L[📧 OTPs] --> A
    M[🎫 Vouchers] --> B
```

---

## 🔗 EXTERNAL INTEGRATIONS

### PayOS Payment Gateway

```mermaid
flowchart LR
    A[🛒 Order Created] --> B[💳 PayOS API]
    B --> C[🔗 Generate Payment URL]
    C --> D[👨‍🎓 User Pays]
    D --> E[🔄 Webhook Notification]
    E --> F[🖥️ Backend Updates Order]
    F --> G[📧 Send Confirmation Email]
    G --> H[🎯 Activate Course Access]
```

### Brevo Email Service

```mermaid
flowchart TD
    A[📧 Email Event] --> B[📝 Template Selection]
    B --> C{Email Type}
    C -->|OTP| D[🔐 OTP Template]
    C -->|Payment| E[💳 Payment Confirmation]
    C -->|Course| F[📚 Course Access]

    D --> G[📨 Brevo API]
    E --> G
    F --> G

    G --> H[📧 Send Email]
    H --> I[📊 Track Delivery]
    I --> J[📈 Analytics]
```

### AWS S3 File Storage

```mermaid
flowchart TD
    A[📁 File Upload] --> B[📊 Validate File]
    B --> C[☁️ Upload to S3]
    C --> D[🔗 Generate URL]
    D --> E[💾 Store in Database]
    E --> F[📤 Return File URL]
```

---

## 🚀 DEPLOYMENT & PRODUCTION

### Production Configuration

```mermaid
graph TD
    A[🚀 Production Server] --> B[📦 PM2 Process Manager]
    B --> C[🖥️ Strapi Application]
    C --> D[🗄️ MySQL Database]
    C --> E[📧 Brevo Email]
    C --> F[💳 PayOS API]

    G[🔧 Environment Config] --> H[🔐 .env Variables]
    H --> I[📊 Database Connection]
    H --> J[🔑 API Keys]

    K[📈 Monitoring] --> L[📊 PM2 Monitoring]
    K --> M[📋 Error Logs]
    K --> N[⚡ Performance Metrics]
```

### PM2 Ecosystem Configuration

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'ong-ba-day-hoa-backend',
    script: 'npm run start',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 1337
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

---

## 📈 PERFORMANCE OPTIMIZATION

### Database Optimization

```mermaid
mindmap
  root((⚡ Database Performance))
    Indexing Strategy
      Primary Keys
      Foreign Keys
      Composite Indexes
      Full-text Search
    Query Optimization
      N+1 Problem Prevention
      Efficient Joins
      Query Caching
      Connection Pooling
    Data Structure
      Normalized Tables
      Proper Relationships
      Data Types Optimization
      Storage Engine Selection
```

### API Optimization

```mermaid
mindmap
  root((🚀 API Performance))
    Response Optimization
      Data Serialization
      Compression
      Pagination
      Selective Fields
    Caching Strategy
      Redis Cache
      Response Cache
      Database Query Cache
      Static Asset Cache
    Rate Limiting
      Request Throttling
      IP-based Limits
      User-based Limits
      Endpoint-specific Rules
```

---

## 🔒 SECURITY MEASURES

### Security Layers

```mermaid
flowchart TD
    A[🌐 External Request] --> B[🛡️ CORS Policy]
    B --> C[🔐 Authentication]
    C --> D[📝 Input Validation]
    D --> E[🔒 Authorization]
    E --> F[🗄️ Database Security]
    F --> G[📤 Response Sanitization]

    H[🔧 Additional Security] --> I[📊 Rate Limiting]
    H --> J[📋 Request Logging]
    H --> K[🚫 SQL Injection Prevention]
    H --> L[🔐 Data Encryption]
```

### Security Implementation

```typescript
// Security middleware configuration
export default [
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        directives: {
          'default-src': ["'self'"],
          'script-src': ["'self'", "'unsafe-inline'"],
          'style-src': ["'self'", "'unsafe-inline'"],
          'img-src': ["'self'", 'data:', 'blob:', 'https:'],
          'connect-src': ["'self'", 'https:'],
        }
      }
    }
  },
  // CORS protection
  {
    name: 'strapi::cors',
    config: {
      origin: ['https://ongbadayhoa.com', 'https://admin.ongbadayhoa.com'],
      credentials: true,
      maxAge: 86400
    }
  }
];
```

---

## 📊 MONITORING & LOGGING

### Application Monitoring

```mermaid
flowchart TD
    A[📊 Monitoring System] --> B[⚡ Performance Metrics]
    A --> C[🚨 Error Tracking]
    A --> D[📈 User Analytics]
    A --> E[🖥️ System Health]

    B --> F[📊 Response Times]
    B --> G[💾 Memory Usage]
    B --> H[🗄️ Database Queries]

    C --> I[📋 Error Logs]
    C --> J[🔍 Exception Tracking]
    C --> K[🚨 Alert System]

    D --> L[👥 Active Users]
    D --> M[📊 API Usage]
    D --> N[🎯 Feature Usage]
```

### Logging Strategy

```typescript
// Custom logging configuration
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'ong-ba-day-hoa-backend' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

---

## 🎯 CONCLUSION

Backend Strapi của dự án "Ông Ba Dạy Hóa" được thiết kế với:

- **🏗️ Kiến trúc mạnh mẽ**: Headless CMS với custom logic
- **🔐 Bảo mật cao**: JWT, CORS, input validation
- **📊 Performance tối ưu**: Database indexing, caching
- **🔧 Tích hợp linh hoạt**: PayOS, Brevo, AWS S3
- **📈 Scalability**: Connection pooling, load balancing
- **🛠️ Developer-friendly**: TypeScript, auto-generated APIs

**Strapi backend cung cấp nền tảng vững chắc cho toàn bộ hệ thống! 🚀**
