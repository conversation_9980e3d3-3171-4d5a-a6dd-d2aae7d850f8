# 🎨 PHÂN TÍCH UI COMPONENTS - "ÔNG BA DẠY HÓA"

## 📋 TỔNG QUAN COMPONENT ARCHITECTURE

Dự án "Ông Ba Dạy Hóa" sử dụng kiến trúc component-based với sự kết hợp giữa:
- **Atomic Design**: Atomic → Molecules → Organisms → Templates → Pages
- **Component Composition**: Props drilling, Context API, Custom hooks
- **Styling**: Tailwind CSS với design system riêng
- **State Management**: React hooks + Context API

```mermaid
graph TD
    A[🎯 Component Architecture] --> B[⚛️ React Components]
    A --> C[🎨 Styling System]
    A --> D[🔄 State Management]
    A --> E[📱 Responsive Design]

    B --> F[🧩 Atomic Components]
    B --> G[🏗️ Composite Components]
    B --> H[📱 Layout Components]

    C --> I[🎨 Tailwind CSS]
    C --> J[🎯 Design Tokens]
    C --> K[📐 Utility Classes]

    D --> L[🔄 React Hooks]
    D --> M[🌍 Context API]
    D --> N[📦 State Libraries]

    E --> O[📱 Mobile-First]
    E --> P[💻 Desktop Adaptive]
    E --> Q[📊 Breakpoint System]
```

---

## 🧩 ATOMIC COMPONENTS

### 1. Button Component

```mermaid
flowchart TD
    A[🔘 Button.jsx] --> B[📥 Props Interface]
    A --> C[🎨 Variant System]
    A --> D[📱 Responsive Behavior]

    B --> E[variant, children, icon, className, ...]
    B --> F[onClick, disabled, type]

    C --> G[Primary - CTA Actions]
    C --> H[Secondary Gray - Alternative Actions]
    C --> I[Secondary Color - Supporting Actions]

    D --> J[Mobile Touch Targets]
    D --> K[Desktop Hover States]
    D --> L[Focus Management]
```

**Code Structure:**
```typescript
// Button Variants
const variants = {
  primary: "bg-primary-default text-primary-default border-primary-default hover:bg-primary-hover...",
  secondaryGray: "bg-secondary-gray-default text-secondary-gray-default...",
  secondaryColor: "bg-secondary-color-default text-secondary-color-default..."
}

// Icon Positioning
{iconPosition === "left" && icon && <span key="left-icon">{icon}</span>}
{children}
{iconPosition === "right" && icon && <span key="right-icon">{icon}</span>}
```

**Usage Patterns:**
```jsx
// Primary CTA Button
<Button variant="primary" onClick={handlePurchase}>
  Mua khóa học ngay
</Button>

// Secondary Action with Icon
<Button 
  variant="secondaryGray" 
  icon={<ArrowLeftIcon />}
  iconPosition="left"
  onClick={goBack}
>
  Quay lại
</Button>
```

### 2. TextField Component

```mermaid
flowchart TD
    A[📝 TextField.jsx] --> B[📋 Input Types]
    A --> C[✅ Validation States]
    A --> D[♿ Accessibility]

    B --> E[text, email, password, tel, number]
    B --> F[textarea, select, file]

    C --> G[Default State]
    C --> H[Focus State]
    C --> I[Error State]
    C --> J[Success State]
    C --> K[Disabled State]

    D --> L[ARIA Labels]
    D --> M[Keyboard Navigation]
    D --> N[Screen Reader Support]
```

**Design System:**
```css
/* Input Base Styles */
.input-base {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg
         focus:outline-none focus:ring-2 focus:ring-primary-500
         placeholder:text-gray-400 transition-colors;
}

/* Validation States */
.input-error {
  @apply border-red-500 focus:ring-red-500;
}

.input-success {
  @apply border-green-500 focus:ring-green-500;
}
```

### 3. Modal Component

```mermaid
flowchart TD
    A[📱 Modal.jsx] --> B[🎯 Overlay System]
    A --> C[📦 Content Container]
    A --> D[❌ Close Mechanisms]

    B --> E[Backdrop Blur]
    B --> F[Click Outside to Close]
    B --> G[Escape Key Handling]

    C --> H[Header Section]
    C --> I[Body Content]
    C --> J[Footer Actions]

    D --> E[Close Button]
    D --> F[Backdrop Click]
    D --> G[Keyboard Events]
```

**Accessibility Features:**
```jsx
// Focus Management
useEffect(() => {
  if (isOpen) {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }
}, [isOpen]);

// Keyboard Navigation
useEffect(() => {
  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      onClose();
    }
  };
  document.addEventListener('keydown', handleKeyDown);
  return () => document.removeEventListener('keydown', handleKeyDown);
}, [onClose]);
```

---

## 🏗️ COMPOSITE COMPONENTS

### 4. Header Component

```mermaid
flowchart TD
    A[🧭 Header.jsx] --> B[🌐 Navigation]
    A --> C[🔐 Authentication]
    A --> D[👤 User Menu]
    A --> E[📱 Mobile Menu]

    B --> F[Logo & Branding]
    B --> G[Navigation Links]
    B --> H[CTA Buttons]

    C --> I{User Logged In?}
    I -->|Yes| J[👤 User Profile]
    I -->|No| K[🔑 Login Button]

    D --> L[Profile Settings]
    D --> M[Password Change]
    D --> N[Logout]

    E --> O[🍔 Hamburger Menu]
    E --> P[Mobile Navigation]
    E --> Q[Touch Interactions]
```

**State Management:**
```jsx
// Authentication State
const isLoggedIn = useCallback(() => {
  return isMounted && isAuthenticated;
}, [isAuthenticated, user, isMounted]);

// Mobile Menu State
const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

// User Menu State
const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
```

**Responsive Behavior:**
```jsx
// Mobile Menu Toggle
const toggleMobileMenu = () => {
  setIsMobileMenuOpen(!isMobileMenuOpen);
};

// Desktop Hover Menu
const handleMouseEnter = () => {
  if (!isMobile) {
    setIsUserMenuOpen(true);
  }
};
```

### 5. Dashboard Layout Component

```mermaid
flowchart TD
    A[📊 DashboardLayout.jsx] --> B[📐 Layout Structure]
    A --> C[📱 Responsive Sidebar]
    A --> D[🔥 Streak Integration]
    A --> E[📅 Live Class Notifications]

    B --> F[Sidebar Navigation]
    B --> G[Header Section]
    B --> H[Main Content Area]
    B --> I[Bottom Mobile Nav]

    C --> J[Desktop Expanded]
    C --> K[Tablet Collapsed]
    C --> L[Mobile Bottom Nav]

    D --> M[Streak Counter]
    D --> N[Chemistry Points]
    D --> O[Progress Tracking]

    E --> P[Live Class Banner]
    E --> Q[Next Class Info]
    E --> R[Join Class Button]
```

**Complex State Logic:**
```jsx
// Screen Size Detection
const [widthScreen, setWidthScreen] = useState({
  gt1280: true,      // Desktop large
  bw9611279: false,  // Desktop medium
  bw641960: false,   // Tablet
  bw375640: false    // Mobile
});

// Live Class Logic
const [isLive, setIsLive] = useState(false);
useEffect(() => {
  const interval = setInterval(() => {
    const now = new Date();
    setIsLive(now >= startDate && now <= endDate);
  }, 1000);
  return () => clearInterval(interval);
}, [course]);
```

---

## 🎨 DESIGN SYSTEM & STYLING

### Tailwind Configuration

```mermaid
mindmap
  root((🎨 Design System))
    Color Palette
      Primary (#198C43 - Green)
      Secondary Gray (#6B7280)
      Utility Colors
      Semantic Colors (Success, Error, Warning)
    Typography
      Font Family (Inter)
      Font Sizes (xs to 7xl)
      Font Weights (300 to 900)
      Line Heights
    Spacing
      Padding (xs to 7xl)
      Margin (xs to 7xl)
      Gap (xs to 7xl)
    Breakpoints
      sm (640px)
      md (768px)
      lg (1024px)
      xl (1280px)
      2xl (1536px)
    Component Variants
      Button States (default, hover, focus, active, disabled)
      Input States (default, focus, error, success)
      Modal Sizes (sm, md, lg, xl)
```

### Design Tokens

```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          default: '#198C43',
          hover: '#166B34',
          focused: '#166B34',
          pressed: '#0F4A22',
          disabled: '#F3F4F6'
        },
        secondary: {
          gray: {
            default: '#6B7280',
            hover: '#4B5563',
            // ...
          }
        }
      },
      spacing: {
        'xs': '4px',
        'sm': '8px',
        'md': '12px',
        'lg': '16px',
        'xl': '24px',
        '2xl': '32px',
        '3xl': '48px',
        '4xl': '64px'
      },
      fontSize: {
        'xs': ['12px', { lineHeight: '16px' }],
        'sm': ['14px', { lineHeight: '20px' }],
        'md': ['16px', { lineHeight: '24px' }],
        'lg': ['18px', { lineHeight: '28px' }],
        'xl': ['20px', { lineHeight: '28px' }],
        '2xl': ['24px', { lineHeight: '32px' }]
      }
    }
  }
}
```

---

## 🔄 STATE MANAGEMENT PATTERNS

### Context API Implementation

```mermaid
flowchart TD
    A[🌍 Context Architecture] --> B[🔐 AuthContext]
    A --> C[👤 UserProvider]
    A --> D[📢 NotificationProvider]
    A --> E[🍞 ToastProvider]

    B --> F[Authentication State]
    B --> G[Login/Logout Actions]
    B --> H[Token Management]

    C --> I[User Profile Data]
    C --> J[User Preferences]
    C --> K[Subscription Status]

    D --> L[Notification List]
    D --> M[Read/Unread Status]
    D --> N[Notification Actions]

    E --> O[Toast Messages]
    E --> P[Toast Types]
    E --> Q[Auto Dismiss]
```

### Custom Hooks Pattern

```mermaid
flowchart TD
    A[🪝 Custom Hooks] --> B[📡 useApi]
    A --> C[📱 useScreenSize]
    A --> D[🔐 useAuth]
    A --> E[📊 useLocalStorage]

    B --> F[API Calls]
    B --> G[Loading States]
    B --> H[Error Handling]

    C --> I[Breakpoint Detection]
    C --> J[Device Type]
    C --> K[Orientation]

    D --> L[Auth State]
    D --> M[User Permissions]
    D --> N[Redirect Logic]

    E --> O[Persistent Storage]
    E --> P[Data Serialization]
    E --> Q[Cross-tab Sync]
```

---

## 📱 RESPONSIVE DESIGN PATTERNS

### Mobile-First Approach

```mermaid
flowchart TD
    A[📱 Mobile-First Design] --> B[📋 Base Styles]
    A --> C[📱 Small Screens (sm)]
    A --> D[📱 Medium Screens (md)]
    A --> E[💻 Large Screens (lg)]

    B --> F[Touch-Friendly Targets]
    B --> G[Readable Typography]
    B --> H[Optimized Images]

    C --> I[Single Column Layout]
    C --> J[Bottom Navigation]
    C --> K[Simplified Menus]

    D --> L[Two Column Layout]
    D --> M[Collapsible Sidebar]
    D --> N[Tablet Optimizations]

    E --> O[Multi-Column Layout]
    E --> P[Hover Interactions]
    E --> Q[Advanced Navigation]
```

### Responsive Navigation

```mermaid
flowchart TD
    A[📱 Responsive Navigation] --> B{Device Type}

    B -->|Mobile| C[🍔 Hamburger Menu]
    C --> D[📋 Slide-out Menu]
    C --> E[🔄 Overlay Backdrop]

    B -->|Tablet| F[📐 Collapsed Sidebar]
    F --> G[👆 Touch Interactions]
    F --> H[📏 Medium Layout]

    B -->|Desktop| I[🖥️ Full Sidebar]
    I --> J[🖱️ Hover Effects]
    I --> K[📊 Expanded Menu]
```

---

## ♿ ACCESSIBILITY PATTERNS

### A11Y Implementation

```mermaid
flowchart TD
    A[♿ Accessibility] --> B[⌨️ Keyboard Navigation]
    A --> C[📢 Screen Readers]
    A --> D[🎨 Visual Indicators]
    A --> E[🔊 Audio Cues]

    B --> F[Tab Order]
    B --> G[Focus Management]
    B --> H[Keyboard Shortcuts]

    C --> I[ARIA Labels]
    C --> J[Semantic HTML]
    C --> K[Live Regions]

    D --> L[Color Contrast]
    D --> M[Focus Indicators]
    D --> N[Error Messages]

    E --> O[Status Announcements]
    E --> P[Form Validation]
    E --> Q[Success Feedback]
```

### ARIA Implementation

```jsx
// Accessible Button
<button
  aria-label="Close modal"
  aria-describedby="modal-description"
  onClick={onClose}
>
  <CloseIcon aria-hidden="true" />
</button>

// Accessible Modal
<div
  role="dialog"
  aria-modal="true"
  aria-labelledby="modal-title"
  aria-describedby="modal-description"
>
  <h2 id="modal-title">Modal Title</h2>
  <div id="modal-description">Modal content description</div>
</div>
```

---

## 🧪 COMPONENT TESTING PATTERNS

### Testing Strategy

```mermaid
flowchart TD
    A[🧪 Testing Pyramid] --> B[🔝 E2E Tests]
    A --> C[🔄 Integration Tests]
    A --> D[🔧 Unit Tests]

    B --> E[📱 User Journeys]
    B --> F[🌐 Critical Flows]
    B --> G[🔄 Browser Testing]

    C --> H[📡 API Integration]
    C --> I[🧩 Component Integration]
    C --> J[🔄 State Management]

    D --> K[⚛️ Component Logic]
    D --> L[🪝 Custom Hooks]
    D --> M[🔧 Utility Functions]
```

### Test Implementation

```javascript
// Unit Test Example
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Button from '../components/Button';

describe('Button Component', () => {
  test('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  test('handles click events', async () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    await userEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('applies correct variant styles', () => {
    render(<Button variant="primary">Primary</Button>);
    const button = screen.getByText('Primary');
    expect(button).toHaveClass('bg-primary-default');
  });
});
```

---

## 📊 PERFORMANCE OPTIMIZATION

### Component Performance

```mermaid
flowchart TD
    A[⚡ Performance] --> B[🔄 Code Splitting]
    A --> C[📦 Lazy Loading]
    A --> D[💾 Memoization]

    B --> E[⚛️ Dynamic Imports]
    B --> F[📄 Route-based Splitting]
    B --> G[🧩 Component Splitting]

    C --> H[🖼️ Image Lazy Loading]
    C --> I[📊 Data Lazy Loading]
    C --> J[🎯 Intersection Observer]

    D --> K[⚛️ React.memo]
    D --> L[🪝 useMemo]
    D --> M[🪝 useCallback]
```

### Bundle Optimization

```mermaid
flowchart TD
    A[📦 Bundle Optimization] --> B[🌳 Tree Shaking]
    A --> C[📊 Code Analysis]
    A --> D[🔄 Dynamic Imports]

    B --> E[🗑️ Unused Code Removal]
    B --> F[📏 Bundle Size Reduction]
    B --> G[⚡ Loading Performance]

    C --> H[📈 Bundle Analyzer]
    C --> I[🔍 Dependency Analysis]
    C --> J[📊 Size Tracking]

    D --> K[📄 Page-based Loading]
    D --> L[🧩 Component-based Loading]
    D --> M[📚 Library Splitting]
```

---

## 🔄 COMPONENT LIFECYCLE

### Component Development Workflow

```mermaid
journey
    title Component Development

    section Design
        Designer: 5: Create mockups and prototypes
        Developer: 4: Review design requirements
        Team: 5: Discuss component API

    section Implementation
        Developer: 5: Create component structure
        Developer: 4: Implement functionality
        Developer: 5: Add responsive behavior
        Developer: 4: Implement accessibility

    section Testing
        Developer: 5: Write unit tests
        Developer: 4: Test across browsers
        Developer: 5: Test responsive design
        Developer: 4: Accessibility testing

    section Documentation
        Developer: 4: Write component documentation
        Developer: 5: Create usage examples
        Developer: 4: Update design system

    section Integration
        Developer: 5: Integrate with existing components
        Developer: 4: Test in application context
        Developer: 5: Performance optimization
```

---

## 🎯 CONCLUSION

Component architecture của dự án "Ông Ba Dạy Hóa" được thiết kế với:

- **🧩 Modular Design**: Atomic → Composite → Layout components
- **📱 Responsive First**: Mobile-first với progressive enhancement
- **♿ Accessible**: WCAG compliant với ARIA support
- **⚡ Performant**: Code splitting, lazy loading, memoization
- **🧪 Testable**: Comprehensive testing strategy
- **🔄 Maintainable**: Clean code, documentation, design system

**Component system cung cấp foundation vững chắc cho user experience! 🚀**
