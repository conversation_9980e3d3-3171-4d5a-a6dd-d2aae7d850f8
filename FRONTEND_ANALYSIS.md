# 🌐 PHÂN TÍCH FRONTEND (NEXT.JS) - "ÔNG BA DẠY HÓA"

## 📋 TỔNG QUAN FRONTEND

Frontend của dự án "Ông Ba Dạy Hóa" được xây dựng trên **Next.js 15** với kiến trúc App Router hiện đại, sử dụng **React 19** và **TypeScript** để tạo trải nghiệm người dùng mượt mà:

```mermaid
graph TD
    A[🌐 Next.js 15 Frontend] --> B[📱 App Router]
    A --> C[⚛️ React 19]
    A --> D[🎨 Tailwind CSS]
    A --> E[🔄 State Management]
    A --> F[📡 API Integration]

    B --> G[🏠 Server Components]
    B --> H[📄 Client Components]
    B --> I[📊 SEO Optimization]

    C --> J[🔗 React Hooks]
    C --> K[📱 Mobile-First]
    C --> L[⚡ Performance]

    D --> M[🎨 Utility-First CSS]
    D --> N[📱 Responsive Design]
    D --> O[🌙 Custom Theme]

    E --> P[🔐 Authentication]
    E --> Q[👤 User Management]
    E --> R[📊 Global State]

    F --> S[🔗 Axios Client]
    F --> T[🍪 Cookie Management]
    F --> U[🔄 Real-time Updates]
```

---

## 🏗️ NEXT.JS ARCHITECTURE

### App Router Structure

```mermaid
graph TD
    subgraph "📁 app/ Directory"
        A[🏠 layout.js] --> B[🏠 Root Layout]
        A --> C[🏠 page.js]
        A --> D[📄 error.js]
        A --> E[📄 loading.js]

        F[📱 Components] --> G[🎨 Header.jsx]
        F --> H[🦶 Footer.jsx]
        F --> I[📐 AppShell.js]

        J[📄 Pages] --> K[🏫 /khoa-hoc/[slug]]
        J --> L[📝 /bai-viet/[id]]
        J --> M[👥 /quan-ly]

        N[🔗 API Routes] --> O[🔐 /api/auth/login]
        N --> P[📚 /api/courses]
        N --> Q[💳 /api/payment]
    end

    subgraph "🎯 App Router Features"
        R[📊 SEO] --> S[📈 Metadata API]
        R --> T[🔍 Open Graph]
        R --> U[📱 Twitter Cards]

        V[⚡ Performance] --> W[🚀 Server Components]
        V --> X[📦 Streaming SSR]
        V --> Y[🔄 ISR]
    end
```

### Component Architecture

```mermaid
flowchart TD
    A[📱 AppShell] --> B[🧭 Header]
    A --> C[📄 Page Content]
    A --> D[🦶 Footer]

    B --> E[👤 User Menu]
    B --> F[🔍 Search Bar]
    B --> G[📱 Mobile Menu]

    C --> H[📚 Course Components]
    C --> I[📝 Blog Components]
    C --> J[👨‍🎓 Dashboard Components]

    K[🔄 Context Providers] --> L[🔐 AuthProvider]
    K --> M[👤 UserProvider]
    K --> N[📢 NotificationProvider]
    K --> O[🍞 ToastProvider]
```

---

## 📂 CẤU TRÚC THƯ MỤC FRONTEND

### Core Structure

```mermaid
graph TD
    A[frontend/] --> B[📦 package.json]
    A --> C[⚙️ next.config.mjs]
    A --> D[🎨 tailwind.config.js]
    A --> E[🛡️ middleware.js]

    F[📱 app/] --> G[🏠 layout.js]
    F --> H[🏠 page.js]
    F --> I[🔗 api/]
    F --> J[📄 pages/]

    K[🧩 components/] --> L[🎨 ui/]
    K --> M[📚 dashboard/]
    K --> N[📱 layouts/]

    O[🔧 utils/] --> P[🍪 cookieHelper.js]
    O --> Q[🔗 CommonUtil.js]
    O --> R[✅ validators.js]

    S[📊 context/] --> T[🔐 AuthContext.js]
    S --> U[👤 UserProvider.jsx]
    S --> V[📢 NotificationContext.jsx]
```

### App Directory Deep Dive

```mermaid
mindmap
  root((📁 app/ Directory))
    Core Files
      layout.js
      page.js
      providers.js
      globals.css
    Authentication
      dang-nhap/page.jsx
      dang-ky/page.jsx
      quen-mat-khau/page.jsx
      xac-thuc/page.jsx
    Content Pages
      khoa-hoc/[slug]/page.jsx
      bai-viet/[id]/page.jsx
      thong-bao/page.jsx
    Dashboard
      quan-ly/page.jsx
      thong-tin-ca-nhan/page.jsx
      tai-khoan/page.jsx
      thanh-toan/page.jsx
      hoa-don/page.jsx
    API Routes
      auth/login/route.js
      courses/route.js
      payment/route.js
```

---

## 🎯 KEY COMPONENTS ANALYSIS

### AppShell Component

```mermaid
flowchart TD
    A[📐 AppShell] --> B{Path Check}
    B -->|Public| C[🧭 Header + Content + 🦶 Footer]
    B -->|Dashboard| D[📊 Content Only]
    B -->|Auth| E[🔐 Content Only]

    F[📦 Providers] --> G[🔐 AuthProvider]
    F --> H[👤 UserProvider]
    F --> I[📢 NotificationProvider]
    F --> J[🍞 ToastProvider]

    K[🔄 Context Wrapping] --> L[🎯 Route-specific Logic]
    L --> M[📱 Mobile Menu]
    L --> N[👤 User Profile]
    L --> O[🔔 Notifications]
```

### Header Component Architecture

```mermaid
flowchart TD
    A[🧭 Header] --> B[📱 Responsive Design]
    A --> C[🔐 Auth State]
    A --> D[👤 User Menu]

    B --> E[💻 Desktop Layout]
    B --> F[📱 Mobile Layout]

    C --> G{Logged In?}
    G -->|Yes| H[👤 User Profile]
    G -->|No| I[🔑 Login Button]

    D --> J[👤 Profile Settings]
    D --> K[🔓 Logout]
    D --> L[📊 Dashboard]

    M[🔍 Search] --> N[📚 Course Search]
    M --> O[📝 Blog Search]

    P[📱 Mobile Menu] --> Q[🍔 Hamburger Menu]
    P --> R[📋 Navigation Items]
```

---

## 🔄 STATE MANAGEMENT

### Context Providers Architecture

```mermaid
flowchart TD
    A[🌳 Provider Tree] --> B[📐 AppShell]
    B --> C[🍞 ToastProvider]
    C --> D[👤 UserProvider]
    D --> E[🔐 AuthProvider]
    E --> F[📢 NotificationProvider]

    G[📊 State Flow] --> H[🔐 Auth State]
    G --> I[👤 User Data]
    G --> J[📢 Notifications]
    G --> K[🍞 Toast Messages]

    L[🔄 Data Persistence] --> M[🍪 Cookies]
    L --> N[💾 Local Storage]
    L --> O[🌐 HTTP Requests]
```

### UserProvider Implementation

```mermaid
sequenceDiagram
    participant C as 🧩 Component
    participant P as 👤 UserProvider
    participant S as 🍪 Cookies
    participant A as 🔗 API

    C->>P: Request User Data
    P->>S: Check Cookies
    S-->>P: Token Found
    P->>A: Fetch User Profile
    A-->>P: User Data
    P-->>C: User Object

    C->>P: Update User Data
    P->>A: PUT /api/auth/profile
    A-->>P: Updated Data
    P->>S: Update Cookies
    P-->>C: Updated User
```

---

## 🛡️ MIDDLEWARE & AUTHENTICATION

### Middleware Flow

```mermaid
flowchart TD
    A[🌐 Request] --> B[🛡️ Middleware]
    B --> C{Path Check}

    C -->|Public| D[✅ Allow]
    C -->|Protected| E{Token Check}
    C -->|API| F[✅ Allow API]

    E -->|No Token| G[🔄 Redirect Login]
    E -->|Has Token| H{User Info Check}

    H -->|Incomplete| I[🔄 Redirect Profile]
    H -->|OTP Required| J[🔄 Redirect Verify]
    H -->|Complete| K{Order Check}

    K -->|Has Order| L[🔄 Redirect Dashboard]
    K -->|No Order| M[✅ Allow]

    N[📊 Logging] --> O[🔍 Development Logs]
    N --> P[🚨 Error Handling]
```

### Authentication Implementation

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 User
    participant M as 🛡️ Middleware
    participant C as 🍪 Cookies
    participant A as 🔗 API
    participant R as 🔄 Router

    U->>M: Access Protected Page
    M->>C: Check Token
    C-->>M: Token Found
    M->>A: Validate Token
    A-->>M: User Valid
    M->>R: Allow Access
    R-->>U: Render Page

    U->>M: Access Without Token
    M->>C: Check Token
    C-->>M: No Token
    M->>R: Redirect Login
    R-->>U: Login Page
```

---

## 📡 API INTEGRATION

### Axios Client Architecture

```mermaid
flowchart TD
    A[🔗 API Client] --> B[⚙️ Configuration]
    A --> C[🔐 Interceptors]
    A --> D[📡 Request Methods]

    B --> E[🌐 Base URL]
    B --> F[⏰ Timeout]
    B --> G[📊 Headers]

    C --> H[🔑 Auth Token]
    C --> I[⚠️ Error Handling]
    C --> J[📝 Logging]

    D --> K[📚 Courses API]
    D --> L[👤 Auth API]
    D --> M[💳 Payment API]
    D --> N[📧 OTP API]

    O[📊 Response Handling] --> P[✅ Success]
    O --> Q[⚠️ Error]
    O --> R[🔄 Retry Logic]
```

### API Methods Structure

```typescript
// app/api/strapi.js
const strapiAPI = {
  // Authentication
  auth: {
    login: async (email, password) => { /* ... */ },
    signup: async (userData) => { /* ... */ },
    googleAuth: async (googleData) => { /* ... */ },
    logout: () => { /* ... */ }
  },

  // Courses
  courses: {
    getAllCourses: async () => { /* ... */ },
    getCourseById: async (id) => { /* ... */ },
    getCourseBySlug: async (slug) => { /* ... */ }
  },

  // Payment & Orders
  payment: {
    createPaymentLink: async (data) => { /* ... */ },
    verifyPayment: async (orderCode) => { /* ... */ }
  },

  // OTP & Communication
  sendOTP: {
    send: async (email) => { /* ... */ },
    verify: async (email, code) => { /* ... */ }
  }
};
```

---

## 🎨 STYLING & UI

### Tailwind CSS Configuration

```mermaid
flowchart TD
    A[🎨 Styling System] --> B[📝 tailwind.config.js]
    A --> C[🎨 globals.css]
    A --> D[📱 Responsive Design]

    B --> E[🎨 Custom Colors]
    B --> F[📏 Custom Spacing]
    B --> G[🔤 Custom Fonts]

    C --> H[🌐 Global Styles]
    C --> I[🎯 Utility Classes]
    C --> J[📊 Component Styles]

    D --> K[💻 Desktop Styles]
    D --> L[📱 Tablet Styles]
    D --> M[📱 Mobile Styles]
```

### Component Library Structure

```mermaid
mindmap
  root((🧩 Component Library))
    UI Components
      Button.jsx
      TextField.jsx
      Modal.jsx
      RadioGroup.jsx
    Layout Components
      Header.jsx
      Footer.jsx
      AppShell.js
      MobileMenu.jsx
    Feature Components
      CourseCard.jsx
      VideoPlayer.jsx
      QuizGradeSelect.jsx
      CountdownTimer.jsx
    Dashboard Components
      ProfileModal.jsx
      ReportStreak.jsx
      StarPointPopup.jsx
      Notification.jsx
```

---

## 🔄 ROUTING & NAVIGATION

### App Router Implementation

```mermaid
flowchart TD
    A[📡 App Router] --> B[🏠 Root Layout]
    A --> C[📄 Page Components]
    A --> D[🔄 Nested Routes]

    B --> E[📱 Layout Structure]
    B --> F[🎨 Global Styles]
    B --> G[📊 SEO Metadata]

    C --> H[🏠 Homepage]
    C --> I[📚 Course Pages]
    C --> J[👨‍🎓 Dashboard]
    C --> K[🔐 Auth Pages]

    D --> L[📖 /khoa-hoc/[slug]]
    D --> M[📝 /bai-viet/[id]]
    D --> N[👥 /quan-ly/*]

    O[🔄 Navigation] --> P[📱 Client-side]
    O --> Q[⚡ Server-side]
    O --> R[🔄 Middleware]
```

### Route Protection Strategy

```mermaid
flowchart TD
    A[🛡️ Route Protection] --> B[🛡️ Middleware]
    A --> C[🔐 Auth Context]
    A --> D[👤 User Context]

    B --> E{Token?}
    E -->|Yes| F{User Info Complete?}
    E -->|No| G[🔄 Redirect Login]

    F -->|Yes| H{OTP Verified?}
    F -->|No| I[🔄 Redirect Profile]

    H -->|Yes| J{Order Status?}
    H -->|No| K[🔄 Redirect Verify]

    J -->|Completed| L[✅ Allow Dashboard]
    J -->|Pending| M[✅ Allow Public]
```

---

## 📱 RESPONSIVE DESIGN

### Mobile-First Approach

```mermaid
flowchart TD
    A[📱 Mobile-First Design] --> B[📱 Mobile Layout]
    A --> C[📱 Tablet Adaptation]
    A --> D[💻 Desktop Enhancement]

    B --> E[📐 Mobile Menu]
    B --> F[👆 Touch Interactions]
    B --> G[📏 Compact Layout]

    C --> H[📋 Side Navigation]
    C --> I[🎯 Touch & Mouse]
    C --> J[📐 Medium Layout]

    D --> K[🖱️ Desktop Navigation]
    D --> L[🖱️ Hover Effects]
    D --> M[📐 Wide Layout]

    N[🎨 Breakpoints] --> O[📱 sm: 640px]
    N --> P[📱 md: 768px]
    N --> Q[💻 lg: 1024px]
    N --> R[💻 xl: 1280px]
```

---

## 🚀 PERFORMANCE OPTIMIZATION

### Next.js Performance Features

```mermaid
mindmap
  root((⚡ Performance Optimization))
    Image Optimization
      Next.js Image Component
      Automatic WebP conversion
      Lazy loading
      CDN optimization
    Code Splitting
      Dynamic imports
      Route-based splitting
      Bundle analysis
    Caching Strategy
      Browser cache
      CDN cache
      Service Worker
      Redis cache
    SEO Optimization
      Metadata API
      Open Graph tags
      Twitter Cards
      Sitemap generation
```

### Bundle Analysis

```mermaid
flowchart TD
    A[📦 Bundle Analysis] --> B[⚙️ webpack-bundle-analyzer]
    A --> C[📊 Bundle Size]
    A --> D[🔍 Unused Code]

    B --> E[📈 Identify Large Chunks]
    B --> F[🎯 Code Splitting Opportunities]

    C --> G[📱 Mobile Bundle]
    C --> H[💻 Desktop Bundle]

    D --> H[🌳 Tree Shaking]
    D --> I[🔄 Dynamic Imports]

    J[🚀 Optimization Results] --> K[⚡ Faster Load Times]
    J --> L[💾 Smaller Bundle Size]
    J --> M[📱 Better Mobile Performance]
```

---

## 🔧 DEVELOPMENT EXPERIENCE

### Development Tools

```mermaid
mindmap
  root((🛠️ Development Tools))
    Code Quality
      ESLint
      Prettier
      TypeScript
      Husky pre-commit hooks
    Testing
      Jest
      React Testing Library
      API Testing
      E2E Testing
    Development Server
      Next.js Dev Server
      Hot Reload
      Fast Refresh
      Error Overlay
    Build Tools
      Webpack
      PostCSS
      Tailwind CSS
      Image Optimization
```

### Development Workflow

```mermaid
flowchart TD
    A[🔄 Development Workflow] --> B[📝 Code Changes]
    A --> C[⚡ Hot Reload]
    A --> D[🔍 ESLint Check]
    A --> E[📦 Build Process]

    B --> F[⚛️ React Components]
    B --> G[🎨 Tailwind Styles]
    B --> H[🔗 API Integration]

    C --> I[📱 Browser Update]
    C --> J[📊 DevTools]

    D --> K[✅ Code Standards]
    D --> L[⚠️ Error Prevention]

    E --> M[📦 Production Bundle]
    E --> N[🚀 Deployment Ready]

    O[🧪 Testing] --> P[🧩 Unit Tests]
    O --> Q[🔗 Integration Tests]
    O --> R[📱 E2E Tests]
```

---

## 📊 ANALYTICS & MONITORING

### Frontend Analytics

```mermaid
flowchart TD
    A[📊 Analytics Setup] --> B[🔥 Google Analytics]
    A --> C[📈 Hotjar]
    A --> D[📊 Custom Events]

    B --> E[👥 User Behavior]
    B --> F[📈 Page Views]
    B --> G[⏱️ Session Duration]

    C --> H[🖱️ Click Tracking]
    C --> I[📱 User Recordings]
    C --> J[🌡️ Heatmaps]

    D --> K[🎯 Conversion Funnel]
    D --> L[📚 Course Engagement]
    D --> M[💰 Payment Flow]

    N[📋 Performance Metrics] --> O[⚡ Load Times]
    N --> P[📦 Bundle Size]
    N --> Q[🖱️ Interaction Times]
```

---

## 🎯 CONCLUSION

Frontend Next.js của dự án "Ông Ba Dạy Hóa" được thiết kế với:

- **🏗️ Kiến trúc hiện đại**: Next.js 15 + App Router
- **⚡ Performance tối ưu**: Server Components, ISR, Image Optimization
- **📱 Mobile-First**: Responsive design, Touch interactions
- **🔐 Security**: Middleware protection, JWT authentication
- **🛠️ DX tốt**: TypeScript, Hot reload, Modern tooling
- **🎨 UI/UX**: Tailwind CSS, Component-based architecture

**Frontend mang lại trải nghiệm người dùng mượt mà và hiện đại! 🚀**
