# 📚 TỔNG QUAN DỰ ÁN "ÔNG BA DẠY HÓA" - NỀN TẢNG HỌC HÓA ONLINE

## 🎯 TỔNG QUAN DỰ ÁN

**Ông Ba Dạy Hóa** là một nền tảng học hóa học trực tuyến được thiết kế để hỗ trợ học sinh THPT học tập và nâng cao kiến thức hóa học. Dự án sử dụng kiến trúc hiện đại với **Next.js** (frontend) và **Strapi** (backend) để tạo thành một hệ thống headless CMS đầy đủ tính năng.

---

## 🏗️ KIẾN TRÚC HỆ THỐNG

### Sơ đồ kiến trúc tổng quan

```mermaid
graph TB
    subgraph "Người dùng (Client)"
        A[📱 Học sinh/ Phụ huynh] --> B[🌐 Trình duyệt Web]
    end

    subgraph "Frontend Layer"
        B --> C[⚛️ Next.js App]
        C --> D[🎨 React Components]
        C --> E[📊 Context Providers]
    end

    subgraph "API Communication"
        D --> F[🔗 Axios HTTP Client]
        F --> G[📡 Strapi REST API]
    end

    subgraph "Backend Layer"
        G --> H[🗄️ Strapi CMS]
        H --> I[🗃️ MySQL Database]
        H --> J[📧 Email Service]
        H --> K[💳 Payment Gateway]
    end

    subgraph "External Services"
        K --> L[💰 PayOS Payment]
        J --> M[📨 Brevo/SendinBlue]
        C --> N[🔐 Google OAuth]
    end

    subgraph "Development Tools"
        O[⚙️ Development Server] --> P[🔧 Hot Reload]
        Q[📦 Package Managers] --> R[📚 Dependencies]
    end
```

### Luồng hoạt động chính

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 Học sinh
    participant F as 🌐 Frontend (Next.js)
    participant B as 🖥️ Backend (Strapi)
    participant D as 🗄️ Database (MySQL)
    participant P as 💳 PayOS
    participant E as 📧 Email Service

    U->>F: Truy cập website
    F->>B: Yêu cầu dữ liệu khóa học
    B->>D: Lấy thông tin khóa học
    D-->>B: Trả về dữ liệu
    B-->>F: Trả về API response
    F-->>U: Hiển thị giao diện

    U->>F: Đăng ký tài khoản
    F->>B: Gửi thông tin đăng ký
    B->>B: Xác thực & tạo user
    B->>E: Gửi email xác thực
    B-->>F: Trả về kết quả
    F-->>U: Thông báo thành công

    U->>F: Thanh toán khóa học
    F->>B: Tạo đơn hàng
    B->>P: Tạo link thanh toán
    P-->>B: Trả về payment URL
    B-->>F: Chuyển hướng thanh toán
    F-->>U: Redirect to PayOS

    P-->>U: Xử lý thanh toán
    U->>P: Hoàn tất thanh toán
    P->>B: Webhook thông báo
    B->>D: Cập nhật trạng thái đơn
    B->>E: Gửi email xác nhận
    B-->>U: Kích hoạt khóa học
```

---

## 🛠️ CÔNG NGHỆ SỬ DỤNG (TECH STACK)

### Frontend (Next.js)

```mermaid
mindmap
  root((🌐 Frontend))
    Next.js 15
      App Router
      Server Components
      Client Components
    React 19
      Hooks & Functional Components
    Styling
      Tailwind CSS
      PostCSS
      Custom CSS Variables
    HTTP Client
      Axios
      Interceptors
      Cookie Management
    Authentication
      JWT Tokens
      Cookie Storage
      Google OAuth
    UI Components
      React Icons
      React Spinners
      Custom Components
    State Management
      React Context
      Local Storage
      Cookie Storage
    Utilities
      React Hot Toast
      Form Validation
      Media Handling
```

### Backend (Strapi)

```mermaid
mindmap
  root((🖥️ Backend))
    Strapi 5
      Headless CMS
      REST API
      Custom Controllers
    Database
      MySQL
      ORM (Knex.js)
      Connection Pooling
    Authentication
      JWT Authentication
      User Permissions
      SSO Integration
    Email Service
      Brevo/SendinBlue
      Custom Templates
      OTP System
    Payment Integration
      PayOS Gateway
      Webhook Handling
      Order Management
    File Storage
      AWS S3
      Local Upload
      Image Processing
    Plugins
      Users-Permissions
      SSO Provider
      Email Provider
```

### Công cụ phát triển

```mermaid
mindmap
  root((⚙️ Development Tools))
    Package Managers
      npm
      Node.js >=18.0.0
    Code Quality
      ESLint
      TypeScript
      Jest Testing
    Build Tools
      Webpack
      Babel
      PostCSS
    Development Server
      Next.js Dev Server
      Strapi Dev Server
      Hot Reload
    Deployment
      PM2 Process Manager
      Environment Config
      Docker Support
```

---

## 📁 CẤU TRÚC THƯ MỤC CHI TIẾT

### 1. 📂 **Root Directory** (`ongbadayhoa-v2/`)

| File/Thư mục | Mục đích | Giải thích cho người non-tech |
|--------------|----------|-------------------------------|
| `README.md` | Tài liệu tổng quan | Hướng dẫn sử dụng, cài đặt, và giới thiệu dự án |
| `backend/` | Phần backend (Strapi) | "Bộ não" xử lý dữ liệu, API, và quản lý nội dung |
| `frontend/` | Phần frontend (Next.js) | "Giao diện người dùng" - những gì học sinh thấy trên trình duyệt |

### 2. 🖥️ **Backend Structure** (`backend/`)

#### 📂 **Core Files** (Files chính)
| File | Mục đích | Chi tiết |
|------|----------|----------|
| `package.json` | Quản lý dependencies | Danh sách các thư viện, công cụ backend cần thiết |
| `tsconfig.json` | Cấu hình TypeScript | Quy tắc viết code, kiểm tra lỗi cho backend |
| `ecosystem.config.js` | Cấu hình PM2 | Cách chạy server production (môi trường thật) |

#### 📂 **Configuration** (`config/`)
| File | Mục đích | Chi tiết |
|------|----------|----------|
| `database.ts` | Kết nối MySQL | Thông tin host, username, password database |
| `server.ts` | Cấu hình server | Port (1337), host, security settings |
| `middlewares.ts` | Middleware | Xử lý CORS, authentication, security |
| `plugins.ts` | Cấu hình plugins | Email, SSO, user permissions |
| `admin.ts` | Admin panel | Giao diện quản trị viên |

#### 📂 **API Structure** (`src/api/`)
Đây là phần quan trọng nhất - nơi định nghĩa các API endpoints:

```mermaid
graph TD
    subgraph "API Endpoints"
        A[🏫 Course API] --> B[📚 Chapters & Lessons]
        A --> C[📝 Exercises & Quizzes]
        A --> D[🎯 Knowledge Sections]

        E[👥 User API] --> F[🔐 Authentication]
        E --> G[👤 Profile Management]

        H[💳 Payment API] --> I[💰 Order Processing]
        H --> J[🔗 PayOS Integration]

        K[📧 Communication] --> L[📨 OTP System]
        K --> M[📧 Email Templates]

        N[🎯 Streak System] --> O[📊 Progress Tracking]
        N --> P[🏆 Achievement System]

        Q[📝 Blog API] --> R[📰 Content Management]
        Q --> S[📱 SEO Optimization]
    end
```

**Chi tiết từng API:**

| API | Mục đích | Files liên quan |
|-----|----------|------------------|
| **Course API** | Quản lý khóa học | `course/`, `chapter/`, `knowledge/`, `exercise/` |
| **User API** | Quản lý người dùng | `user/`, authentication endpoints |
| **Payment API** | Xử lý thanh toán | `payment/`, `order/` |
| **OTP API** | Gửi mã xác thực | `send-otp/`, email templates |
| **Streak API** | Hệ thống điểm liên tục | `streak/`, `streak-question/` |
| **Blog API** | Quản lý bài viết | `blog-post/`, `blog-category/` |

#### 📂 **Email Templates** (`src/email-templates/`)
| File | Mục đích |
|------|----------|
| `OTP.html` | Template gửi mã OTP |
| `Bill.html` | Template hóa đơn thanh toán |
| `PaymentConfirmation.html` | Email xác nhận thanh toán |

### 3. 🌐 **Frontend Structure** (`frontend/`)

#### 📂 **Core Files**
| File | Mục đích |
|------|----------|
| `package.json` | Dependencies frontend |
| `next.config.mjs` | Cấu hình Next.js |
| `tailwind.config.js` | Cấu hình Tailwind CSS |
| `jsconfig.json` | Cấu hình JavaScript |

#### 📂 **App Router** (`app/`)
Đây là cách Next.js 13+ tổ chức routing:

```mermaid
graph TD
    subgraph "Pages Structure"
        A[🏠 / (Trang chủ)] --> B[📚 /khoa-hoc (Danh sách khóa học)]
        A --> C[📝 /bai-viet (Blog)]
        A --> D[👤 /dang-nhap (Đăng nhập)]

        B --> E[📖 /khoa-hoc/[slug] (Chi tiết khóa học)]

        F[👨‍🎓 /quan-ly (Dashboard)] --> G[📊 /quan-ly/thong-ke (Thống kê)]
        F --> H[🏆 /quan-ly/streak (Điểm streak)]
        F --> I[👤 /quan-ly/profile (Hồ sơ)]

        J[💳 /thanh-toan (Thanh toán)] --> K[📄 /hoa-don (Hóa đơn)]
    end
```

#### 📂 **Components** (`components/`)
```mermaid
graph TD
    subgraph "UI Components"
        A[📱 Layout Components] --> B[🧭 Header (Menu chính)]
        A --> C[🦶 Footer (Chân trang)]
        A --> D[📐 AppShell (Layout chính)]

        E[🎨 UI Components] --> F[🔘 Button (Nút bấm)]
        E --> G[📝 TextField (Ô nhập liệu)]
        E --> H[📋 Modal (Popup)]
        E --> I[🎯 RadioGroup (Chọn lựa)]

        J[📚 Feature Components] --> K[📖 CourseCard (Card khóa học)]
        J --> L[🎬 VideoPlayer (Trình phát video)]
        J --> M[📊 Dashboard (Bảng điều khiển)]
        J --> N[🔥 Streak Components (Hệ thống điểm)]
    end
```

#### 📂 **Context Providers** (`context/`)
| Provider | Mục đích |
|----------|----------|
| `AuthContext` | Quản lý đăng nhập/xuất |
| `UserProvider` | Thông tin người dùng |
| `NotificationProvider` | Quản lý thông báo |
| `ToastContext` | Hiển thị toast messages |

#### 📂 **Utilities** (`utils/`)
| File | Mục đích |
|------|----------|
| `CommonUtil.js` | Các hàm tiện ích chung |
| `cookieHelper.js` | Xử lý cookies |
| `validators.js` | Validate dữ liệu forms |
| `analytics.js` | Google Analytics |

---

## 🔄 LUỒNG HOẠT ĐỘNG CHI TIẾT

### 1. 🔐 **Luồng Đăng nhập/Đăng ký**

```mermaid
journey
    title Luồng Đăng nhập Học sinh

    section Truy cập
        Học sinh: 5: Truy cập website
        Frontend: 4: Hiển thị trang chủ
        Học sinh: 5: Click "Đăng nhập"

    section Đăng nhập
        Frontend: 4: Hiển thị form đăng nhập
        Học sinh: 5: Nhập email/password
        Frontend: 4: Gọi API auth/local
        Backend: 3: Xác thực thông tin
        Database: 3: Kiểm tra user tồn tại
        Backend: 3: Tạo JWT token
        Frontend: 4: Lưu token vào cookie
        Học sinh: 5: Chuyển hướng dashboard

    section Xác thực OTP
        Backend: 3: Gửi email OTP
        Học sinh: 5: Nhận email, nhập OTP
        Frontend: 4: Gọi API verify-otp
        Backend: 3: Xác thực OTP
        Frontend: 4: Hoàn tất đăng nhập
```

### 2. 📚 **Luồng Học tập**

```mermaid
journey
    title Luồng Học tập Khóa học

    section Khám phá
        Học sinh: 5: Truy cập trang khóa học
        Frontend: 4: Gọi API courses
        Backend: 3: Lấy danh sách khóa học
        Frontend: 4: Hiển thị danh sách

    section Chi tiết khóa học
        Học sinh: 5: Click vào khóa học
        Frontend: 4: Gọi API courses/[id]
        Backend: 3: Lấy thông tin chi tiết
        Frontend: 4: Hiển thị chương, bài học

    section Học bài
        Học sinh: 5: Chọn bài học
        Frontend: 4: Hiển thị nội dung bài học
        Học sinh: 5: Xem video, đọc lý thuyết
        Frontend: 4: Ghi nhận progress

    section Luyện tập
        Học sinh: 5: Làm bài tập
        Frontend: 4: Gửi câu trả lời
        Backend: 3: Chấm điểm, lưu kết quả
        Frontend: 4: Hiển thị kết quả
```

### 3. 💳 **Luồng Thanh toán**

```mermaid
journey
    title Luồng Thanh toán Khóa học

    section Chọn mua
        Học sinh: 5: Chọn khóa học, click mua
        Frontend: 4: Kiểm tra đăng nhập
        Frontend: 4: Hiển thị trang thanh toán

    section Tạo đơn hàng
        Học sinh: 5: Nhập thông tin giao hàng
        Frontend: 4: Gọi API orders
        Backend: 3: Tạo đơn hàng trong database
        Backend: 3: Gọi PayOS API
        PayOS: 3: Tạo payment link

    section Thanh toán
        Frontend: 4: Redirect sang PayOS
        Học sinh: 5: Thanh toán trên PayOS
        PayOS: 3: Xử lý thanh toán
        PayOS: 3: Gửi webhook về backend

    section Xác nhận
        Backend: 3: Nhận webhook
        Backend: 3: Cập nhật trạng thái đơn
        Backend: 3: Gửi email xác nhận
        Học sinh: 5: Nhận email, truy cập khóa học
```

---

## 🎯 **CÁC TÍNH NĂNG CHÍNH**

### 1. 👨‍🎓 **Hệ thống Quản lý Học tập**
- **Dashboard học viên**: Theo dõi tiến độ học
- **Streak system**: Điểm liên tục khi học đều đặn
- **Progress tracking**: Theo dõi hoàn thành bài học
- **Personalized learning**: Học theo lộ trình cá nhân

### 2. 📚 **Nội dung Khóa học**
- **Video bài giảng**: Học trực quan với thí nghiệm
- **Bài tập tương tác**: Luyện tập kiến thức
- **Quiz và kiểm tra**: Đánh giá định kỳ
- **Tài liệu tham khảo**: Download tài liệu học tập

### 3. 💰 **Hệ thống Thanh toán**
- **Tích hợp PayOS**: Thanh toán an toàn
- **Hóa đơn điện tử**: Xuất hóa đơn VAT
- **Lịch sử giao dịch**: Tra cứu đơn hàng
- **Voucher system**: Mã giảm giá

### 4. 📧 **Hệ thống Giao tiếp**
- **OTP verification**: Xác thực qua email
- **Email templates**: Gửi thông báo tự động
- **Notification system**: Thông báo trong app
- **Feedback system**: Thu thập ý kiến học viên

### 5. 🔐 **Bảo mật và Phân quyền**
- **JWT Authentication**: Xác thực người dùng
- **Role-based access**: Phân quyền theo vai trò
- **Google OAuth**: Đăng nhập với Google
- **Secure cookies**: Lưu trữ token an toàn

---

## 🌍 **MÔI TRƯỜNG TRIỂN KHAI**

### Development Environment (Môi trường phát triển)
```mermaid
graph TB
    subgraph "Local Development"
        A[💻 Developer Machine] --> B[📦 Node.js 18+]
        B --> C[🖥️ Backend Server:1337]
        B --> D[🌐 Frontend Server:3000]

        E[🗄️ MySQL Database] --> C
        F[📧 Email Service] --> C
        G[💳 PayOS API] --> C
    end

    subgraph "Development Tools"
        H[⚙️ VS Code] --> I[🔥 Hot Reload]
        J[📊 Browser DevTools] --> K[🐛 Debugging]
        L[📋 Jest Tests] --> M[✅ Code Quality]
    end
```

### Production Environment (Môi trường sản xuất)
```mermaid
graph TB
    subgraph "Production Servers"
        A[🖥️ VPS/Server] --> B[📦 PM2 Process Manager]
        B --> C[🖥️ Backend Server]
        B --> D[🌐 Frontend Server]

        E[🗄️ MySQL Database] --> C
        F[📧 Brevo Email] --> C
        G[💳 PayOS Payment] --> C
        H[☁️ AWS S3] --> C
    end

    subgraph "Monitoring & Analytics"
        I[📊 Google Analytics] --> D
        J[🔥 Hotjar Tracking] --> D
        K[📈 Performance Monitoring] --> B
    end
```

---

## 🔄 **CÁCH CÁC CÔNG NGHỆ HỢP TÁC**

### Frontend ↔ Backend Communication

```mermaid
flowchart TD
    A[👨‍🎓 User Action] --> B[⚛️ React Component]
    B --> C[📞 Axios HTTP Request]
    C --> D[🌐 Next.js API Route]
    D --> E[🔗 Strapi REST API]
    E --> F[🗄️ Database Query]
    F --> G[📊 Data Processing]
    G --> H[📤 JSON Response]
    H --> I[⚛️ State Update]
    I --> J[🎨 UI Re-render]
```

### Data Flow Architecture

```mermaid
flowchart LR
    subgraph "Data Sources"
        A[🗄️ MySQL DB] --> B[🔄 Strapi ORM]
        C[📧 Email Templates] --> B
        D[💳 PayOS Webhooks] --> B
    end

    subgraph "API Layer"
        B --> E[📡 REST API Endpoints]
        E --> F[🔐 Authentication Middleware]
        E --> G[📝 Custom Controllers]
        E --> H[🔄 Business Logic]
    end

    subgraph "Client Layer"
        F --> I[🌐 Next.js Server]
        I --> J[⚛️ React Components]
        J --> K[📱 User Interface]
    end

    subgraph "External Integrations"
        L[🔐 Google OAuth] --> F
        M[📨 Brevo Email] --> H
        N[💰 PayOS Payment] --> H
    end
```

---

## 📋 **HƯỚNG DẪN CÀI ĐẶT CHI TIẾT**

### Bước 1: Chuẩn bị môi trường
```bash
# Cài đặt Node.js (version 18+)
# Cài đặt MySQL
# Cài đặt Git
```

### Bước 2: Clone và cài đặt
```bash
# Clone dự án
git clone <repository-url>
cd ong-b-a-day-hoa-v2

# Cài đặt backend
cd backend
npm install

# Cài đặt frontend
cd ../frontend
npm install
```

### Bước 3: Cấu hình môi trường
```bash
# Backend .env
cp .env.example .env
# Điền thông tin database, API keys...

# Frontend .env
cp .env.example .env
# Điền API URL, Google client ID...
```

### Bước 4: Khởi chạy development
```bash
# Terminal 1: Backend
cd backend
npm run develop

# Terminal 2: Frontend
cd frontend
npm run dev
```

### Bước 5: Truy cập ứng dụng
- **Frontend**: http://localhost:3000
- **Backend Admin**: http://localhost:1337/admin

---

## 🚀 **TRIỂN KHAI LÊN PRODUCTION**

### Backend Deployment
```mermaid
flowchart TD
    A[📦 Build Code] --> B[⚙️ Configure PM2]
    B --> C[🚀 Start Production Server]
    C --> D[🔍 Health Check]
    D --> E[📊 Monitoring Setup]
    E --> F[🔄 Auto Restart]
```

### Frontend Deployment
```mermaid
flowchart TD
    A[📦 Build Static Files] --> B[☁️ Upload to Hosting]
    B --> C[⚙️ Configure Environment]
    C --> D[🔗 Setup Domain]
    D --> E[📊 Analytics Setup]
    E --> F[🚀 Go Live]
```

---

## 🔧 **BẢO TRÌ VÀ CẬP NHẬT**

### Monitoring Checklist
- [ ] Server health check
- [ ] Database performance
- [ ] API response times
- [ ] Error logs monitoring
- [ ] User activity tracking

### Update Process
```mermaid
flowchart TD
    A[📢 New Update Available] --> B[🔍 Check Breaking Changes]
    B --> C[🧪 Test in Development]
    C --> D[📋 Create Backup]
    D --> E[🚀 Deploy to Production]
    E --> F[🔍 Monitor Performance]
    F --> G[✅ Verify Functionality]
```

---

## 📞 **LIÊN HỆ VÀ HỖ TRỢ**

- **Email**: <EMAIL>
- **Website**: https://ongbadayhoa.com
- **Documentation**: 
  - Next.js: https://nextjs.org/docs
  - Strapi: https://docs.strapi.io
  - Tailwind CSS: https://tailwindcss.com/docs

---

## 🎯 **KẾT LUẬN**

Dự án **Ông Ba Dạy Hóa** là một nền tảng học hóa học trực tuyến hiện đại với kiến trúc:

- **Frontend**: Next.js + React - trải nghiệm người dùng mượt mà
- **Backend**: Strapi CMS - quản lý nội dung linh hoạt  
- **Database**: MySQL - lưu trữ dữ liệu ổn định
- **Payment**: PayOS - thanh toán an toàn
- **Authentication**: JWT + Google OAuth - bảo mật tốt
- **Email**: Brevo - giao tiếp hiệu quả

Kiến trúc này cho phép:
- Phát triển nhanh chóng
- Dễ dàng mở rộng tính năng
- Bảo mật và ổn định
- Trải nghiệm người dùng tốt
- Dễ bảo trì và cập nhật

**Dự án hoàn toàn sẵn sàng cho việc phát triển và triển khai thực tế! 🚀**
