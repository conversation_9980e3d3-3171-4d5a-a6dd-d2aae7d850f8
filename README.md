# 🚀 Ông Ba Dạy Hóa - Nền tảng học hóa học trực tuyến

[![Next.js](https://img.shields.io/badge/Next.js-15.3.1-black?style=flat&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.0.0-61DAFB?style=flat&logo=react)](https://reactjs.org/)
[![Strapi](https://img.shields.io/badge/Strapi-5.21.0-4945FF?style=flat&logo=strapi)](https://strapi.io/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0-4479A1?style=flat&logo=mysql)](https://mysql.com/)
[![PayOS](https://img.shields.io/badge/PayOS-Payment-00C4CC?style=flat)](https://payos.vn/)

> **Nền tảng học hóa học trực tuyến toàn diện** - <PERSON>ọc tập hiệu quả với phương pháp trực quan, thí nghiệm thực tế và lộ trình cá nhân hóa.

## 📋 Tổng quan dự án

**Ông Ba Dạy Hóa** là một nền tảng học hóa học trực tuyến được thiết kế chuyên nghiệp để hỗ trợ học sinh THPT nâng cao kiến thức hóa học thông qua:

- 🎓 **Học tập trực quan**: Video bài giảng với thí nghiệm thực tế
- 📚 **Lộ trình cá nhân hóa**: Học theo tiến độ và khả năng của từng học sinh
- 🔥 **Hệ thống điểm thưởng**: Streak system khuyến khích học tập đều đặn
- 💳 **Thanh toán an toàn**: Tích hợp PayOS với hóa đơn điện tử
- 📊 **Theo dõi tiến độ**: Dashboard chi tiết cho học sinh và phụ huynh
- 📱 **Trải nghiệm đa thiết bị**: Responsive design, mobile-first

### 🏗️ Kiến trúc hệ thống

```mermaid
graph TD
    subgraph "🏗️ System Architecture"
        A[⚛️ Next.js 15<br/>Frontend<br/>Port 3000] --> B[📡 Strapi 5<br/>Backend API<br/>Port 1337]
        B --> C[🗄️ MySQL 8<br/>Database]

        D[💳 PayOS<br/>Payment Gateway] --> B
        E[📧 Brevo<br/>Email Service] --> B
        F[☁️ AWS S3<br/>File Storage] --> B

        G[👨‍🎓 Học sinh/Phụ huynh] --> A
        A --> G
    end

    subgraph "🔄 Data Flow"
        H[🌐 HTTP Requests] --> A
        A --> I[📡 REST API Calls]
        I --> B
        B --> J[📊 Database Queries]
        J --> C
    end

    subgraph "🔧 External Integrations"
        B --> D
        B --> E
        B --> F
        D --> K[💰 Secure Payments]
        E --> L[📨 Email Notifications]
        F --> M[📁 Media Assets]
    end
```

### 🎯 Tính năng chính

| Tính năng | Mô tả | Trạng thái |
|-----------|-------|------------|
| 🔐 **Authentication** | Đăng nhập/đăng ký, Google OAuth, OTP verification | ✅ Hoàn thành |
| 📚 **Course Management** | Quản lý khóa học, chương, bài học | ✅ Hoàn thành |
| 🎯 **Learning System** | Video player, exercises, quizzes | ✅ Hoàn thành |
| 🔥 **Streak System** | Điểm thưởng, leaderboard, daily challenges | ✅ Hoàn thành |
| 💳 **Payment Integration** | PayOS gateway, hóa đơn, voucher system | ✅ Hoàn thành |
| 📱 **Responsive UI** | Mobile-first design, modern UX | ✅ Hoàn thành |
| 👨‍💼 **Admin Panel** | Content management, user management | ✅ Hoàn thành |
| 📧 **Email System** | OTP, payment confirmation, notifications | ✅ Hoàn thành |

## 🛠️ Tech Stack

### 🌐 Frontend Stack

| Công nghệ | Version | Mục đích | Chi tiết |
|-----------|---------|----------|----------|
| **Next.js** | 15.3.1 | React Framework | App Router, Server Components, SSR/SSG |
| **React** | 19.0.0 | UI Library | Hooks, Functional Components, Concurrent Features |
| **Tailwind CSS** | 3.4.17 | Styling | Utility-first CSS, Custom design system |
| **TypeScript** | 5.x | Type Safety | Type checking, better DX |
| **Axios** | 1.8.3 | HTTP Client | API calls, interceptors, error handling |
| **React Cookie** | 8.0.1 | State Management | Authentication tokens, user preferences |
| **JWT Decode** | 4.0.0 | Token Handling | Decode JWT tokens, expiration checks |
| **Google OAuth** | @react-oauth/google | Authentication | Social login integration |

#### 🎨 UI/UX Libraries
- **React Icons** (5.5.0) - Icon library
- **React Spinners** (0.16.1) - Loading indicators
- **React Hot Toast** (2.5.2) - Toast notifications
- **KaTeX** (0.16.8) - Math formula rendering
- **Swiper** (11.2.6) - Carousel/slider components

### 🖥️ Backend Stack

| Công nghệ | Version | Mục đích | Chi tiết |
|-----------|---------|----------|----------|
| **Strapi** | 5.21.0 | Headless CMS | API generation, content management |
| **Node.js** | 18.0.0 - 22.x.x | Runtime | Server-side JavaScript execution |
| **TypeScript** | 5.x | Type Safety | Backend type checking |
| **MySQL** | 8.0 | Database | Relational data storage |
| **Knex.js** | 2.5.1 | Query Builder | SQL query building, migrations |

#### 🔌 Strapi Plugins & Integrations
- **Users-Permissions** - User management & roles
- **Email Provider (Brevo)** - Email sending service
- **SSO Plugin** - Single sign-on support
- **AWS S3** - File storage & CDN
- **PayOS** - Payment gateway integration

### 🔧 Development Tools

| Tool | Version | Purpose |
|------|---------|---------|
| **ESLint** | 9.x | Code linting |
| **PostCSS** | 8.5.3 | CSS processing |
| **Autoprefixer** | 10.4.21 | CSS vendor prefixes |
| **Jest** | 30.0.5 | Unit testing |
| **PM2** | Latest | Process management |
| **Docker** | Latest | Containerization |

### 📊 Monitoring & Analytics

- **Google Analytics** - User behavior tracking
- **Hotjar** - Heatmaps & user recordings
- **Custom Dashboard** - Application metrics
- **PM2 Monitoring** - Server health checks

## 🏗️ Kiến trúc hệ thống

### 📊 Luồng dữ liệu

```mermaid
graph TD
    A[👨‍🎓 User Action] --> B[🌐 Next.js Frontend]
    B --> C[🛡️ Middleware Check]
    C --> D{Authenticated?}
    D -->|No| E[🔄 Redirect Login]
    D -->|Yes| F[📡 Axios Request]
    F --> G[🖥️ Strapi API]
    G --> H[🔐 JWT Validation]
    H --> I[⚙️ Business Logic]
    I --> J[🗄️ MySQL Database]
    J --> K[📤 JSON Response]
    K --> L[⚛️ React State Update]
    L --> M[🎨 UI Re-render]
```

### 🔗 API Architecture

#### RESTful Endpoints (60+ APIs)

```mermaid
graph TD
    subgraph "🔐 Authentication APIs"
        A1[POST /auth/local] --> B1[Local Login]
        A2[POST /auth/signup] --> B2[User Registration]
        A3[POST /auth/login] --> B3[Google OAuth]
        A4[POST /send-otp] --> B4[OTP Generation]
        A5[POST /verify-otp] --> B5[OTP Verification]
    end

    subgraph "📚 Content APIs"
        C1[GET /courses] --> D1[Course Listing]
        C2[GET /chapters] --> D2[Chapter Details]
        C3[GET /exercises] --> D3[Exercise Content]
        C4[GET /blog-posts] --> D4[Blog Articles]
        C5[GET /video-features] --> D5[Video Content]
    end

    subgraph "💰 Commerce APIs"
        E1[POST /orders] --> F1[Order Creation]
        E2[POST /payments/create-payment-link] --> F2[Payment Link]
        E3[POST /payments/verify] --> F3[Payment Verification]
        E4[POST /vouchers/validate] --> F4[Voucher Validation]
        E5[GET /orders] --> F5[Order History]
    end

    subgraph "🎯 Learning APIs"
        G1[POST /streaks] --> H1[Streak Creation]
        G2[GET /questions] --> H2[Quiz Questions]
        G3[POST /questions-answers] --> H3[Answer Submission]
        G4[GET /streak-questions] --> H4[Daily Challenges]
        G5[POST /reports] --> H5[Progress Reports]
    end
```

#### Security Implementation

```mermaid
flowchart TD
    A[🌐 API Request] --> B[🛡️ CORS Policy]
    B --> C[🔐 JWT Authentication]
    C --> D{Valid Token?}
    D -->|No| E[🚫 401 Unauthorized]
    D -->|Yes| F[📝 Input Validation]
    F --> G[👥 Role Authorization]
    G --> H{Authorized?}
    H -->|No| I[🚫 403 Forbidden]
    H -->|Yes| J[⚙️ Business Logic]
    J --> K[📊 Rate Limiting]
    K --> L[📤 Response]
```

### 🗄️ Database Schema

```mermaid
erDiagram
    USER ||--o{ ORDER : places
    USER ||--o{ STREAK : participates
    USER ||--o{ QUESTIONS-ANSWER : submits

    COURSE ||--|{ CHAPTER : contains
    COURSE ||--o{ COURSE-TIER : has
    COURSE ||--o{ VIDEO-FEATURE : features

    CHAPTER ||--|{ KNOWLEDGE-SECTION : has
    CHAPTER ||--|{ EXERCISE : includes

    ORDER ||--|| COURSE : for
    ORDER ||--|| COURSE-TIER : specifies
    ORDER ||--o{ ACTIVATION-CODE : generates

    STREAK ||--|| STREAK-QUESTION : based_on
    STREAK-QUESTION ||--|{ QUESTION : contains

    QUESTIONS-ANSWER ||--|| QUESTION : answers
    QUESTIONS-ANSWER ||--|| STREAK : part_of

    BLOG-POST ||--|{ BLOG-CONTENT-SECTION : has
    BLOG-POST ||--o{ BLOG-CATEGORY : belongs_to

    VOUCHER ||--o{ ORDER : applies_to
    OTP ||--|| USER : sent_to
```

## 🌐 Frontend Architecture (Next.js)

### 📂 Project Structure

```
frontend/
├── 📱 app/                          # Next.js 13+ App Router
│   ├── 🏠 layout.js                 # Root layout với providers
│   ├── 🏠 page.js                   # Trang chủ
│   ├── 📡 api/
│   │   └── 🔗 strapi.js            # Axios client + API methods
│   ├── 🔐 auth/                    # Authentication pages
│   │   ├── 🖊️ dang-ky/             # User registration
│   │   ├── 🔑 dang-nhap/           # Login page
│   │   ├── 📧 quen-mat-khau/       # Password reset
│   │   ├── 🔢 xac-thuc/            # OTP verification
│   │   └── 🔓 mat-khau-moi/        # New password
│   ├── 📚 khoa-hoc/
│   │   └── 📖 [slug]/              # Dynamic course pages
│   ├── 📝 bai-viet/
│   │   └── 📄 [id]/                # Dynamic blog pages
│   ├── 👨‍🎓 quan-ly/                # Learning dashboard
│   ├── 💳 thanh-toan/              # Payment page
│   ├── 📄 hoa-don/                 # Invoice page
│   ├── 👤 thong-tin-ca-nhan/       # Profile page
│   ├── 🔔 thong-bao/               # Notifications
│   ├── 🏦 tai-khoan/               # Account settings
│   ├── 🧑‍🏫 streak-giao-vien/       # Teacher streak
│   └── 📄 not-found.js             # 404 page
├── 🧩 components/                   # Reusable React Components
│   ├── 🧩 AppLoading.jsx           # Loading spinner
│   ├── 📐 AppShell.js              # Main layout wrapper
│   ├── 🔘 Button.jsx               # Custom button component
│   ├── 🔥 ContinueStreak.jsx       # Streak continuation
│   ├── ⏱️ CountdownTimer.jsx       # Timer for quizzes
│   ├── 📢 CtaSection.jsx           # Call-to-action sections
│   ├── 🧭 Header.jsx               # Navigation header
│   ├── 🦶 Footer.jsx               # Site footer
│   ├── 📱 MobileMenu.jsx           # Mobile navigation
│   ├── 📝 Modal.jsx                # Reusable modal
│   ├── 🔔 Notification.jsx        # Notification component
│   ├── 🚫 OutStreakPopup.jsx      # Streak warning
│   ├── 📝 TextField.jsx            # Custom input field
│   ├── 🎬 VideoPlayer.jsx          # Video player component
│   ├── 📱 VideoPopup.jsx           # Video modal
│   ├── 🎯 dashboard/               # Dashboard components
│   ├── 🎨 icons/                   # Custom SVG icons
│   ├── 📐 layouts/                 # Layout components
│   ├── 🧑‍🏫 streak-teacher/        # Teacher-specific components
│   └── 🎨 ui/                      # Base UI components
├── 🔄 context/                     # React Context Providers
│   ├── 🔐 AuthContext.js          # Authentication state
│   ├── 👤 UserProvider.jsx        # User data management
│   ├── 🔔 NotificationContext.jsx # Notification state
│   ├── 🍞 ToastContext.jsx        # Toast notifications
│   └── 📐 DashboardLayoutContext.jsx # Dashboard layout
├── 🗂️ data/                        # Static data
│   └── 🏫 schools/                 # School information
├── 🖼️ public/                      # Static assets
│   ├── 🖼️ images/                  # Image assets
│   ├── 📄 Favi.png                # Favicon
│   └── 📱 site.webmanifest        # PWA manifest
├── 🛠️ utils/                       # Utility functions
│   ├── 🍪 cookieHelper.js         # Cookie management
│   ├── 🔗 CommonUtil.js           # Common utilities
│   └── ✅ validators.js            # Form validation
└── ⚙️ Config files                 # Configuration
    ├── 📦 package.json            # Dependencies & scripts
    ├── ⚙️ next.config.mjs          # Next.js configuration
    ├── 🎨 tailwind.config.js       # Tailwind CSS config
    ├── 🛡️ middleware.js            # Route protection
    ├── 📋 next-env.d.ts            # TypeScript types
    └── 🔧 jsconfig.json            # JavaScript config
```

### 🎯 Key Features

#### 🔄 State Management
- **Context API**: Authentication, user data, notifications
- **Local Storage**: Persistent user preferences
- **Cookie Storage**: Secure JWT token storage
- **Custom Hooks**: Reusable stateful logic

#### 📱 Responsive Design
- **Mobile-First**: Optimized for mobile devices
- **Breakpoint System**: sm/md/lg/xl/2xl
- **Touch Interactions**: Swipe gestures, touch targets
- **Progressive Enhancement**: Enhanced features for larger screens

#### 🎨 Design System
- **Tailwind CSS**: Utility-first CSS framework
- **Custom Components**: Button, Modal, TextField, etc.
- **Icon Library**: Custom SVG icons for consistency
- **Color Palette**: Primary, secondary, semantic colors

#### 🔐 Authentication Flow
- **JWT Tokens**: Secure API authentication
- **Google OAuth**: Social login integration
- **OTP Verification**: Email-based verification
- **Middleware Protection**: Route-level security

### 📄 Pages & Routes

| Route | Component | Purpose | Access |
|-------|-----------|---------|--------|
| `/` | `page.js` | Homepage với hero section | Public |
| `/khoa-hoc` | `page.jsx` | Danh sách khóa học | Public |
| `/khoa-hoc/[slug]` | `[slug]/page.jsx` | Chi tiết khóa học | Public |
| `/bai-viet` | `page.jsx` | Danh sách blog | Public |
| `/bai-viet/[id]` | `[id]/page.jsx` | Chi tiết bài viết | Public |
| `/dang-nhap` | `page.jsx` | Đăng nhập | Public |
| `/dang-ky` | `page.jsx` | Đăng ký | Public |
| `/quen-mat-khau` | `page.jsx` | Quên mật khẩu | Public |
| `/xac-thuc` | `page.jsx` | OTP verification | Public |
| `/thong-tin-ca-nhan` | `page.jsx` | Profile completion | Private |
| `/quan-ly` | `page.jsx` | Learning dashboard | Private |
| `/quan-ly/xem-video` | - | Video learning | Private |
| `/quan-ly/bai-tap` | - | Exercises | Private |
| `/quan-ly/bai-thi` | - | Quizzes | Private |
| `/quan-ly/khu-tap-luyen` | - | Practice area | Private |
| `/thanh-toan` | `page.jsx` | Payment page | Private |
| `/hoa-don` | `page.jsx` | Invoice page | Private |
| `/thong-bao` | `page.jsx` | Notifications | Private |
| `/tai-khoan` | `page.jsx` | Account settings | Private |
| `/streak-giao-vien` | `page.jsx` | Teacher streak | Public |

### 🧩 Core Components

#### Layout Components
- **AppShell**: Main layout wrapper với conditional rendering
- **Header**: Navigation với responsive menu
- **Footer**: Site footer với links
- **DashboardLayout**: Learning dashboard layout
- **MobileMenu**: Mobile navigation drawer

#### UI Components
- **Button**: Custom button với variants (primary, secondary)
- **TextField**: Custom input field với validation
- **Modal**: Reusable modal với overlay
- **Notification**: Toast notification system
- **VideoPlayer**: Custom video player với controls

#### Feature Components
- **ContinueStreak**: Streak continuation popup
- **CountdownTimer**: Quiz timer component
- **OutStreakPopup**: Streak warning modal
- **StarPointPopup**: Achievement notifications

#### Dashboard Components
- **Avatar**: User avatar với dropdown
- **NavItem**: Navigation item với active state
- **ProfileModal**: User profile editing
- **NotificationBell**: Notification dropdown
- **BadgeCourse**: Course completion badges

### 🔄 Context Architecture

```mermaid
graph TD
    A[🌳 Context Tree] --> B[📐 AppShell]
    B --> C[🍞 ToastProvider]
    C --> D[👤 UserProvider]
    D --> E[🔐 AuthProvider]
    E --> F[🔔 NotificationProvider]
    F --> G[📊 DashboardLayoutProvider]

    H[📊 State Flow] --> I[🔐 Auth State]
    H --> J[👤 User Data]
    H --> K[🔔 Notifications]
    H --> L[🍞 Toast Messages]
    H --> M[📐 Layout Config]
```

### 📡 API Integration

#### Axios Client Architecture
```javascript
// app/api/strapi.js
const strapiAPI = {
  // Authentication
  auth: {
    login: async (email, password) => { /* JWT token + user data */ },
    signup: async (userData) => { /* User registration */ },
    googleAuth: async (googleData) => { /* Google OAuth */ },
    resetPassword: async (data) => { /* Password reset */ }
  },

  // Content
  courses: {
    getAllCourses: async () => { /* Course listing */ },
    getCourseById: async (id) => { /* Course details */ },
    getCourseBySlug: async (slug) => { /* Course by slug */ }
  },

  // Commerce
  payment: {
    createPaymentLink: async (data) => { /* PayOS integration */ },
    verifyPayment: async (orderCode) => { /* Payment verification */ }
  },

  // Learning
  streak: {
    getDataByUser: async (data) => { /* User streak data */ },
    createStreak: async (data) => { /* Start streak */ },
    saveQuestionAnswer: async (data) => { /* Submit answer */ }
  }
};
```

### 🛡️ Middleware Protection

```javascript
// middleware.js - Route Protection
export function middleware(request) {
  const token = request.cookies.get('access_token')?.value;
  const user_data = request.cookies.get('user_data')?.value;
  const { pathname } = request.nextUrl;

  // Public paths
  const publicPaths = ['/', '/dang-nhap', '/dang-ky', '/khoa-hoc', '/bai-viet'];

  // Private paths requiring authentication
  const privatePaths = ['/quan-ly', '/thanh-toan', '/thong-tin-ca-nhan'];

  // Redirect logic based on authentication and user status
  if (!token && !publicPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.redirect(new URL('/dang-nhap', request.url));
  }

  // Check user profile completion
  if (token && user_data) {
    const userData = JSON.parse(user_data);
    const hasFullPersonalInfo = userData.fullname && userData.date && (userData.gender !== undefined);

    if (!hasFullPersonalInfo && pathname !== '/thong-tin-ca-nhan') {
      return NextResponse.redirect(new URL('/thong-tin-ca-nhan', request.url));
    }
  }

  return NextResponse.next();
}
```

## Chi tiết Backend (Strapi)

### Cấu trúc thư mục
```
backend/
├── src/                      # Source code
│   ├── api/                  # API endpoints và models
│   │   ├── course/           # Course API
│   │   │   ├── content-types/# Course model
│   │   │   ├── controllers/  # Course controllers
│   │   │   ├── routes/       # Course routes
│   │   │   └── services/     # Course services
│   │   ├── chapter/          # Chapter API
│   │   ├── knowledge/        # Knowledge API
│   │   ├── exercise/         # Exercise API
│   │   ├── blog-post/        # Blog API
│   │   ├── payment/          # Payment API
│   │   ├── order/            # Order API
│   │   ├── send-otp/         # OTP API
│   │   └── ...
│   ├── email-templates/      # Templates email (HTML)
│   │   ├── OTP.html          # OTP verification email
│   │   ├── PaymentConfirmation.html # Xác nhận thanh toán
│   │   └── ...
│   └── index.ts              # Entry point
├── config/                   # Cấu hình Strapi
│   ├── admin.ts              # Admin panel configuration
│   ├── database.ts           # Database connection
│   ├── middlewares.ts        # Middleware configuration
│   ├── plugins.ts            # Plugin configuration
│   └── server.ts             # Server configuration
├── public/                   # Public assets
├── dist/                     # Compiled code
├── ecosystem.config.js       # PM2 configuration
├── package.json              # Dependencies and scripts
└── tsconfig.json             # TypeScript configuration
```

## Biến môi trường

### Backend (.env)
```
HOST=0.0.0.0
PORT=1337

APP_KEYS=<your_app_keys>
API_TOKEN_SALT=<your_api_token_salt>
ADMIN_JWT_SECRET=<your_admin_jwt_secret>
TRANSFER_TOKEN_SALT=<your_transfer_token_salt>
JWT_SECRET=<your_jwt_secret>

# Database
DATABASE_HOST=<your_database_host>
DATABASE_PORT=<your_database_port>
DATABASE_NAME=<your_database_name>
DATABASE_USERNAME=<your_database_username>
DATABASE_PASSWORD=<your_database_password>
DATABASE_SSL=<your_database_ssl>

# SendMail
SENDINBLUE_API_KEY=<your_sendinblue_api_key>

# Google
GOOGLE_CLIENT_ID=<your_google_client_id>
GOOGLE_CLIENT_SECRET=<your_google_client_secret>
GOOGLE_REDIRECT_URI=<your_google_redirect_uri>

# Payos
PAYOS_CLIENT_ID=<your_payos_client_id>
PAYOS_API_KEY=<your_payos_api_key>
PAYOS_CHECKSUM_KEY=<your_payos_checksum_key>
PAYOS_API_URL=<your_payos_api_url>
BACKEND_URL=<your_backend_url>
```

### Frontend (.env)
```
NEXT_PUBLIC_STRAPI_API_URL=http://localhost:1337/api
NEXT_PUBLIC_STRAPI_PROTOCOL=http
NEXT_PUBLIC_STRAPI_HOST=localhost
NEXT_PUBLIC_STRAPI_PORT=1337
NEXT_PUBLIC_GOOGLE_CLIENT_ID=<your_google_client_id>
```

## Hướng dẫn cài đặt chi tiết

### Backend (Strapi)

1. **Cài đặt dependencies**
   ```bash
   cd backend
   npm install
   ```

2. **Cấu hình biến môi trường**
   - Tạo file `.env` từ `.env.example`
   - Điền các thông tin cần thiết

3. **Khởi động development server**
   ```bash
   npm run develop
   ```
   Server sẽ chạy tại http://localhost:1337 với Admin panel tại http://localhost:1337/admin

4. **Build cho production**
   ```bash
   npm run build
   npm run start
   ```

5. **Triển khai với PM2**
   ```bash
   pm2 start ecosystem.config.js
   ```

### Frontend (Next.js)

1. **Cài đặt dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Cấu hình biến môi trường**
   - Tạo file `.env` với các biến cần thiết

3. **Khởi động development server**
   ```bash
   npm run dev
   ```
   Server sẽ chạy tại http://localhost:3000

4. **Build cho production**
   ```bash
   npm run build
   npm run start
   ```

## Quy trình phát triển

### Quy trình làm việc với Strapi
1. **Content Types**: Tạo và cấu hình content types trong Strapi Admin
2. **API Permissions**: Cấu hình quyền truy cập API trong Users & Permissions plugin
3. **Custom Logic**: Viết controllers và services tùy chỉnh trong `src/api/*/controllers` và `src/api/*/services`
4. **Build**: Chạy `npm run build` sau khi thay đổi cấu trúc

### Quy trình làm việc với Next.js
1. **Components**: Phát triển UI components trong thư mục `components/`
2. **Pages**: Tạo pages trong thư mục `app/`
3. **API Integration**: Sử dụng Axios client trong `app/api/strapi.js` để gọi API
4. **Styling**: Sử dụng Tailwind CSS với các class được định nghĩa trong `tailwind.config.js`

## Triển khai

### Backend
- **Server Requirements**: Node.js >=18.0.0 <=22.x.x, MySQL
- **Process Manager**: PM2 với file cấu hình `ecosystem.config.js`
- **Database**: MySQL với cấu hình trong `config/database.ts`
- **CORS**: Cấu hình trong `config/middlewares.ts` để cho phép các domain cụ thể

### Frontend
- **Hosting Options**: Vercel, Netlify, hoặc bất kỳ hosting nào hỗ trợ Next.js
- **Environment Variables**: Cấu hình các biến môi trường cần thiết
- **Static Assets**: Được phục vụ từ thư mục `public/`

## Bảo trì và cập nhật

### Strapi
- Kiểm tra cập nhật: `npm outdated`
- Cập nhật Strapi: Xem hướng dẫn cập nhật chính thức
- Backup database trước khi cập nhật

### Next.js
- Cập nhật dependencies: `npm update`
- Kiểm tra breaking changes trong Next.js changelog
- Chạy tests sau khi cập nhật

## Xử lý sự cố

### Backend
- Kiểm tra logs: `pm2 logs`
- Kiểm tra kết nối database
- Xác minh cấu hình CORS
- Kiểm tra quyền truy cập API

### Frontend
- Kiểm tra console errors
- Xác minh API endpoints
- Kiểm tra authentication state
- Xác minh biến môi trường

## Liên hệ và hỗ trợ

- **Email hỗ trợ**: <EMAIL>
- **Tài liệu Strapi**: https://docs.strapi.io
- **Tài liệu Next.js**: https://nextjs.org/docs
