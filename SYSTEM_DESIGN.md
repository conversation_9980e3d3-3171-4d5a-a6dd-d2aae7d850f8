# 🏗️ THIẾT KẾ HỆ THỐNG & KIẾN TRÚC - "ÔNG BA DẠY HÓA"

## 📋 TỔNG QUAN KIẾN TRÚC

Dự án "Ông Ba Dạy Hóa" được thiết kế với kiến trúc **Microservices-oriented Monolith** kết hợp **Headless CMS**, tối ưu cho việc phát triển nhanh và dễ bảo trì:

```mermaid
graph TB
    subgraph "🏗️ System Architecture"
        A[🌐 Users] --> B[🌐 Next.js Frontend]
        B --> C[📡 API Gateway Layer]
        C --> D[🖥️ Strapi Backend]
        D --> E[🗄️ MySQL Database]

        F[🔗 External Services] --> D
        F --> G[💳 PayOS Payment]
        F --> H[📧 Brevo Email]
        F --> I[☁️ AWS S3 Storage]
    end

    subgraph "📊 Data Flow"
        J[📱 Mobile/Web Clients] --> B
        K[📧 Email Notifications] --> H
        L[💰 Payment Processing] --> G
        M[📁 Media Storage] --> I
    end

    subgraph "🛠️ Development Tools"
        N[⚡ Development] --> O[🔥 Hot Reload]
        N --> P[📦 Build Tools]
        N --> Q[🧪 Testing Suite]

        R[🚀 Deployment] --> S[🐳 Docker]
        R --> T[📊 Monitoring]
        R --> U[🔄 CI/CD]
    end
```

---

## 🏛️ HIGH-LEVEL ARCHITECTURE

### System Context Diagram

```mermaid
graph TD
    subgraph "🎓 Education Platform Ecosystem"
        A[👨‍🎓 Students] --> B[🌐 Learning Platform]
        C[👨‍👩‍👧‍👦 Parents] --> B
        D[👨‍🏫 Teachers] --> B

        B --> E[📚 Content Delivery]
        B --> F[💳 Payment System]
        B --> G[📊 Progress Tracking]

        H[📧 Email Service] --> I[📬 Notifications]
        I --> A
        I --> C

        J[💰 Payment Gateway] --> K[💳 Secure Transactions]
        K --> F

        L[☁️ Cloud Storage] --> M[📁 Media Assets]
        M --> E
    end

    subgraph "🔧 Supporting Systems"
        N[📊 Analytics] --> O[📈 User Insights]
        P[📋 Admin Panel] --> Q[⚙️ Content Management]
        R[🛡️ Security] --> S[🔐 Access Control]
    end
```

### Architecture Patterns Used

```mermaid
mindmap
  root((🏗️ Architecture Patterns))
    Presentation Layer
      Model-View-Controller
      Component-Based Architecture
      Server-Side Rendering
    Business Logic Layer
      Service Layer Pattern
      Repository Pattern
      Strategy Pattern
    Data Access Layer
      Data Access Object
      Unit of Work
      Active Record
    External Integration
      Adapter Pattern
      Facade Pattern
      Observer Pattern
    Cross-Cutting Concerns
      Middleware Pattern
      Decorator Pattern
      Interceptor Pattern
```

---

## 📊 DETAILED SYSTEM COMPONENTS

### Frontend Architecture

```mermaid
flowchart TD
    A[🌐 Next.js Application] --> B[🏠 App Router]
    A --> C[⚛️ React Components]
    A --> D[🎨 Styling System]
    A --> E[🔄 State Management]

    B --> F[📄 Pages & Layouts]
    B --> G[🛡️ Middleware]
    B --> H[📡 API Routes]

    C --> I[🧩 UI Components]
    C --> J[📚 Feature Components]
    C --> K[🔄 Context Providers]

    D --> L[🎨 Tailwind CSS]
    D --> M[📐 Responsive Design]

    E --> N[🔐 Auth Context]
    E --> O[👤 User Context]
    E --> P[📢 Notification Context]

    Q[📱 Mobile Adaptation] --> R[📋 Touch Interactions]
    Q --> S[📊 Mobile Navigation]
    Q --> T[📏 Responsive Layouts]
```

### Backend Architecture

```mermaid
flowchart TD
    A[🖥️ Strapi Application] --> B[📡 API Layer]
    A --> C[🔐 Authentication]
    A --> D[📊 Content Management]

    B --> E[📋 REST Endpoints]
    B --> F[🎯 Custom Controllers]
    B --> G[⚙️ Business Services]

    C --> H[JWT Authentication]
    C --> I[👥 User Management]
    C --> J[🔑 Permission System]

    D --> K[📝 Content Types]
    D --> L[📊 Admin Panel]
    D --> M[🔄 Auto-generated APIs]

    N[🗄️ Database Layer] --> O[📊 ORM Queries]
    N --> P[🔄 Migrations]
    N --> Q[📈 Performance Optimization]

    R[🔗 External Integrations] --> S[💳 PayOS]
    R --> T[📧 Brevo]
    R --> U[☁️ AWS S3]
```

---

## 🎯 COMPONENT DESIGN PATTERNS

### React Component Patterns

```mermaid
graph TD
    subgraph "⚛️ Component Architecture"
        A[🏗️ Container Components] --> B[📊 State Management]
        A --> C[📡 API Calls]
        A --> D[🎯 Business Logic]

        E[🧩 Presentational Components] --> F[🎨 UI Rendering]
        E --> G[📱 Responsive Design]
        E --> H[⚡ Performance]

        I[🔄 Custom Hooks] --> J[📊 Data Fetching]
        I --> K[🔐 Auth Logic]
        I --> L[📱 Device Detection]

        M[📋 Context Providers] --> N[🌍 Global State]
        M --> O[🔄 State Updates]
        M --> P[📢 Event Handling]
    end

    subgraph "📱 Component Types"
        Q[🏠 Layout Components] --> R[🧭 Header]
        Q --> S[🦶 Footer]
        Q --> T[📐 AppShell]

        U[🎨 UI Components] --> V[🔘 Button]
        U --> W[📝 TextField]
        U --> X[📋 Modal]

        Y[📚 Feature Components] --> Z[📖 CourseCard]
        Y --> AA[🎬 VideoPlayer]
        Y --> BB[📊 Dashboard]
    end
```

### Strapi Service Patterns

```mermaid
graph TD
    subgraph "⚙️ Service Layer Architecture"
        A[📡 API Controllers] --> B[🔄 Request Handling]
        A --> C[📝 Input Validation]
        A --> D[📤 Response Formatting]

        E[🔧 Business Services] --> F[📊 Data Processing]
        E --> G[🔄 Business Rules]
        E --> H[📈 Calculations]

        I[📊 Data Services] --> J[🗄️ Database Queries]
        I --> K[📋 Data Transformation]
        I --> L[📈 Optimization]

        M[🔗 Integration Services] --> N[💳 Payment API]
        M --> O[📧 Email Service]
        M --> P[☁️ File Storage]
    end

    subgraph "🎯 Service Patterns"
        Q[🏗️ Singleton Pattern] --> R[📊 Shared Services]
        Q --> S[🔄 Connection Pooling]

        T[🏭 Factory Pattern] --> U[📝 Dynamic Content Types]
        T --> V[🔧 Custom Controllers]

        W[🎨 Strategy Pattern] --> X[🔐 Authentication Methods]
        W --> Y[💳 Payment Gateways]
    end
```

---

## 📊 DATA ARCHITECTURE & MODELING

### Database Schema Design

```mermaid
erDiagram
    USER ||--o{ ORDER : places
    USER ||--o{ STREAK : participates_in
    USER ||--o{ QUESTIONS-ANSWER : submits

    COURSE ||--|{ CHAPTER : contains
    COURSE ||--o{ COURSE-TIER : has
    COURSE ||--o{ VIDEO-FEATURE : features

    CHAPTER ||--|{ KNOWLEDGE-SECTION : has
    CHAPTER ||--|{ EXERCISE : includes

    ORDER ||--|| COURSE : for
    ORDER ||--|| COURSE-TIER : specifies
    ORDER ||--o{ ACTIVATION-CODE : generates

    STREAK ||--|| STREAK-QUESTION : based_on
    STREAK-QUESTION ||--|{ QUESTION : contains

    QUESTIONS-ANSWER ||--|| QUESTION : answers
    QUESTIONS-ANSWER ||--|| STREAK : part_of

    BLOG-POST ||--|{ BLOG-CONTENT-SECTION : has
    BLOG-POST ||--o{ BLOG-CATEGORY : belongs_to

    VOUCHER ||--o{ ORDER : applies_to
    OTP ||--|| USER : sent_to
```

### Data Flow Architecture

```mermaid
flowchart TD
    A[🌐 User Request] --> B[🛡️ Authentication]
    B --> C{Is Authenticated?}
    C -->|No| D[🔄 Redirect Login]
    C -->|Yes| E[📡 API Request]

    E --> F[🎯 Controller]
    F --> G[⚙️ Service Layer]
    G --> H[📊 Business Logic]
    H --> I[🗄️ Database Query]
    I --> J[📋 Data Processing]
    J --> K[📤 Response]

    L[🔄 Caching Layer] --> M[⚡ Redis Cache]
    L --> N[📦 In-Memory Cache]

    O[📊 Logging] --> P[📋 Request Logs]
    O --> Q[⚠️ Error Logs]
    O --> R[📈 Performance Logs]

    S[🔗 External Services] --> T[💳 Payment API]
    S --> U[📧 Email Service]
    S --> V[☁️ File Storage]
```

---

## 🔐 SECURITY ARCHITECTURE

### Authentication & Authorization

```mermaid
flowchart TD
    A[🔐 Security Architecture] --> B[🌐 Frontend Security]
    A --> C[🖥️ Backend Security]
    A --> D[🗄️ Database Security]
    A --> E[🔗 API Security]

    B --> F[🍪 Secure Cookies]
    B --> G[🔄 CSRF Protection]
    B --> H[🛡️ XSS Prevention]

    C --> I[JWT Authentication]
    C --> J[🔑 API Key Management]
    C --> K[📝 Input Validation]

    D --> L[🔐 Data Encryption]
    D --> M[📋 Access Control]
    D --> N[📊 Audit Logging]

    E --> O[🛡️ CORS Policy]
    E --> P[⏱️ Rate Limiting]
    E --> Q[📊 Request Monitoring]

    R[🔒 Security Layers] --> S[🌐 Network Security]
    R --> T[📱 Application Security]
    R --> U[🗄️ Data Security]
```

### Security Implementation

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 User
    participant F as 🌐 Frontend
    participant M as 🛡️ Middleware
    participant B as 🖥️ Backend
    participant D as 🗄️ Database

    U->>F: Login Request
    F->>M: Authenticate
    M->>M: Validate Token
    M->>B: API Request
    B->>B: Check Permissions
    B->>D: Database Query
    D-->>B: Filtered Data
    B-->>M: Sanitized Response
    M-->>F: Secure Response
    F-->>U: Display Data
```

---

## ⚡ PERFORMANCE ARCHITECTURE

### Performance Optimization Layers

```mermaid
flowchart TD
    A[⚡ Performance Architecture] --> B[🌐 Frontend Optimization]
    A --> C[🖥️ Backend Optimization]
    A --> D[🗄️ Database Optimization]
    A --> E[🔗 Network Optimization]

    B --> F[📦 Code Splitting]
    B --> G[🖼️ Image Optimization]
    B --> H[📊 Bundle Analysis]

    C --> I[📋 Response Caching]
    C --> J[🔄 Connection Pooling]
    C --> K[⚡ Query Optimization]

    D --> L[🔍 Database Indexing]
    D --> M[📊 Query Caching]
    D --> N[📈 Read Replicas]

    E --> O[📡 CDN Integration]
    E --> P[🗜️ Data Compression]
    E --> Q[🌐 HTTP/2 Support]

    R[📊 Monitoring] --> S[⏱️ Response Times]
    R --> T[📈 Throughput]
    R --> U[💾 Resource Usage]
```

### Caching Strategy

```mermaid
graph TD
    subgraph "📦 Multi-Layer Caching"
        A[🌐 Browser Cache] --> B[📋 Static Assets]
        A --> C[🍪 Cookie Storage]

        D[🌐 CDN Cache] --> E[🖼️ Images & Assets]
        D --> F[📄 Static Content]

        G[⚡ Application Cache] --> H[📊 API Responses]
        G --> I[📋 Computed Data]

        J[🗄️ Database Cache] --> K[🔍 Query Results]
        J --> L[📊 Metadata]
    end

    subgraph "⏰ Cache Invalidation"
        M[🔄 Event-Driven] --> N[📝 Data Updates]
        M --> O[👤 User Actions]

        P[⏱️ Time-Based] --> Q[📅 TTL Expiry]
        P --> R[🔄 Scheduled Refresh]

        S[📏 Size-Based] --> T[📊 LRU Eviction]
        S --> U[💾 Memory Limits]
    end
```

---

## 📈 SCALABILITY ARCHITECTURE

### Horizontal Scaling Strategy

```mermaid
graph TD
    subgraph "🔄 Load Balancing"
        A[🌐 Load Balancer] --> B[🖥️ Server 1]
        A --> C[🖥️ Server 2]
        A --> D[🖥️ Server 3]

        B --> E[🗄️ Database Cluster]
        C --> E
        D --> E
    end

    subgraph "🗄️ Database Scaling"
        E --> F[📊 Master Node]
        E --> G[📖 Read Replica 1]
        E --> H[📖 Read Replica 2]

        I[📋 Connection Pooling] --> J[🔄 Connection Reuse]
        I --> K[📈 Concurrent Users]
    end

    subgraph "☁️ Cloud Infrastructure"
        L[📦 Auto Scaling] --> M[📈 Traffic-based]
        L --> N[⚡ Performance-based]

        O[🗂️ Data Partitioning] --> P[📊 Sharding Strategy]
        O --> Q[🔍 Data Distribution]
    end
```

### Microservices Evolution Path

```mermaid
journey
    title Microservices Evolution

    section Current State
        Monolithic App: 5: Strapi Backend + Next.js Frontend
        Single Database: 4: MySQL with optimized queries
        Integrated Services: 4: Payment, Email, Storage

    section Phase 1 - Service Separation
        API Gateway: 4: Central request routing
        Auth Service: 5: Separate authentication
        Content Service: 4: Course & blog management
        Payment Service: 5: Isolated payment processing

    section Phase 2 - Data Partitioning
        User Database: 4: User data & preferences
        Content Database: 5: Courses, blogs, media
        Analytics Database: 4: Tracking & reporting

    section Phase 3 - Full Microservices
        Event-Driven Architecture: 5: Message queues
        Container Orchestration: 4: Kubernetes deployment
        Service Mesh: 5: Istio for service communication
```

---

## 🏭 DEPLOYMENT ARCHITECTURE

### Development Environment

```mermaid
graph TD
    subgraph "💻 Local Development"
        A[👨‍💻 Developer] --> B[📦 Node.js Environment]
        B --> C[🖥️ Backend Server:1337]
        B --> D[🌐 Frontend Server:3000]

        E[🗄️ MySQL Local] --> C
        F[📧 Email Service] --> C
        G[💳 Payment API] --> C

        H[🛠️ Development Tools] --> I[🔥 Hot Reload]
        H --> J[🐛 Debugging]
        H --> K[📊 DevTools]
    end

    subgraph "🔄 Development Workflow"
        L[📝 Code Changes] --> M[⚡ Auto Reload]
        L --> N[🔍 ESLint Check]
        L --> O[📦 Build Process]

        P[🧪 Testing] --> Q[🧩 Unit Tests]
        P --> R[🔗 Integration Tests]
    end
```

### Production Environment

```mermaid
graph TD
    subgraph "🚀 Production Infrastructure"
        A[🌐 CDN] --> B[🌐 Load Balancer]
        B --> C[🖥️ Application Server 1]
        B --> D[🖥️ Application Server 2]

        E[📧 Email Service] --> C
        E --> D

        F[💳 Payment Gateway] --> C
        F --> D

        G[🗄️ Primary Database] --> H[📖 Read Replica 1]
        G --> I[📖 Read Replica 2]

        J[☁️ Object Storage] --> C
        J --> D

        K[📊 Monitoring] --> L[📈 Application Metrics]
        K --> M[📋 Error Tracking]
        K --> N[⚡ Performance Monitoring]
    end

    subgraph "🛠️ DevOps Tools"
        O[🐳 Docker] --> P[📦 Containerization]
        O --> Q[📊 Monitoring]

        R[📈 CI/CD Pipeline] --> S[🧪 Automated Testing]
        R --> T[🚀 Automated Deployment]
        R --> U[📊 Rollback Strategy]
    end
```

---

## 🔄 DEPLOYMENT STRATEGIES

### Blue-Green Deployment

```mermaid
flowchart TD
    A[🚀 Deployment Process] --> B[📦 Build New Version]
    B --> C[🧪 Test New Version]
    C --> D{Tests Pass?}
    D -->|No| E[🔧 Fix Issues]
    D -->|Yes| F[🌐 Deploy to Green Environment]

    F --> G[🔄 Switch Load Balancer]
    G --> H[📊 Monitor Green Environment]
    H --> I{Performance Good?}
    I -->|No| J[🔄 Rollback to Blue]
    I -->|Yes| K[🗑️ Decommission Blue Environment]

    L[📊 Traffic Management] --> M[🔄 Zero-Downtime Switch]
    L --> N[📈 Gradual Traffic Shift]
    L --> O[🛡️ Fallback Strategy]
```

### Rolling Deployment

```mermaid
flowchart TD
    A[🔄 Rolling Deployment] --> B[📦 Deploy to Server 1]
    B --> C[🧪 Health Check Server 1]
    C --> D{Server 1 Healthy?}
    D -->|No| E[🔄 Rollback Server 1]
    D -->|Yes| F[📦 Deploy to Server 2]

    F --> G[🧪 Health Check Server 2]
    G --> H{Server 2 Healthy?}
    H -->|No| I[🔄 Rollback Server 2]
    H -->|Yes| J[📦 Deploy to Server 3]

    K[📊 Monitoring] --> L[📈 Performance Metrics]
    K --> M[⚠️ Error Rates]
    K --> N[📊 Success Rate]

    O[🔄 Rollback Strategy] --> P[📦 Previous Version]
    O --> Q[📊 Data Integrity Check]
    O --> R[👥 User Communication]
```

---

## 📊 MONITORING & OBSERVABILITY

### Monitoring Architecture

```mermaid
flowchart TD
    A[📊 Monitoring System] --> B[📈 Application Metrics]
    A --> C[🖥️ Infrastructure Metrics]
    A --> D[👥 User Experience]

    B --> E[📊 Response Times]
    B --> F[⚠️ Error Rates]
    B --> G[📈 Throughput]

    C --> H[💾 CPU Usage]
    C --> I[🧠 Memory Usage]
    C --> J[💽 Disk I/O]

    D --> K[📱 Page Load Times]
    D --> L[🔄 User Interactions]
    D --> M[📊 Conversion Rates]

    N[🚨 Alert System] --> O[📧 Email Alerts]
    N --> P[📱 SMS Alerts]
    N --> Q[💬 Slack Notifications]

    R[📊 Dashboard] --> S[📈 Real-time Metrics]
    R --> T[📋 Historical Data]
    R --> U[🔍 Anomaly Detection]
```

### Logging Strategy

```mermaid
flowchart TD
    A[📋 Logging Architecture] --> B[📝 Application Logs]
    A --> C[🔗 API Logs]
    A --> D[🗄️ Database Logs]
    A --> E[🛡️ Security Logs]

    B --> F[📊 Request/Response]
    B --> G[⚠️ Error Details]
    B --> H[🔍 Debug Information]

    C --> I[📡 API Calls]
    C --> J[📊 Performance]
    C --> K[📈 Usage Patterns]

    D --> L[🔍 Query Logs]
    D --> M[📊 Slow Queries]
    D --> N[📋 Connection Logs]

    E --> O[🔐 Authentication]
    E --> P[📋 Access Attempts]
    E --> Q[⚠️ Security Events]

    R[📦 Log Storage] --> S[🗄️ Centralized Storage]
    R --> T[🔍 Search & Analysis]
    R --> U[📊 Retention Policy]
```

---

## 🧪 TESTING ARCHITECTURE

### Testing Pyramid

```mermaid
graph TD
    subgraph "🧪 Testing Strategy"
        A[🔝 E2E Tests] --> B[🌐 User Journeys]
        A --> C[📱 Critical Flows]

        D[🔄 Integration Tests] --> E[📡 API Endpoints]
        D --> F[🗄️ Database Operations]

        G[🔧 Unit Tests] --> H[⚙️ Business Logic]
        G --> I[🧩 Component Logic]

        J[📊 Performance Tests] --> K[⚡ Load Testing]
        J --> L[📈 Stress Testing]

        M[🔒 Security Tests] --> N[🛡️ Vulnerability Scanning]
        M --> O[📋 Penetration Testing]
    end

    subgraph "🛠️ Testing Tools"
        P[🧪 Test Framework] --> Q[📱 Jest]
        P --> R[⚛️ React Testing Library]
        P --> S[📡 Supertest]

        T[📊 Test Automation] --> U[🔄 CI/CD Integration]
        T --> V[📈 Coverage Reports]
        T --> W[📋 Test Reports]
    end
```

---

## 🎯 RELIABILITY & RESILIENCE

### Fault Tolerance

```mermaid
flowchart TD
    A[🛡️ Fault Tolerance] --> B[🔄 Circuit Breakers]
    A --> C[📦 Retry Mechanisms]
    A --> D[🗑️ Fallback Strategies]
    A --> E[📊 Health Checks]

    B --> F[🌐 Service Isolation]
    B --> G[📈 Performance Protection]

    C --> H[📡 API Retries]
    C --> I[🗄️ Database Retries]
    C --> J[🔗 External Service Retries]

    D --> K[📋 Default Data]
    D --> L[🔄 Graceful Degradation]
    D --> M[📊 Cached Data]

    E --> N[💓 Heartbeat Monitoring]
    E --> P[📈 Metric Collection]
    E --> Q[🚨 Alert Generation]

    R[🔄 Recovery Strategies] --> S[📦 Auto Recovery]
    R --> T[📋 Manual Intervention]
    R --> U[📊 Incident Response]
```

### Disaster Recovery

```mermaid
flowchart TD
    A[🛡️ Disaster Recovery] --> B[💾 Data Backup]
    A --> C[🔄 System Replication]
    A --> D[📋 Recovery Plans]
    A --> E[🧪 Regular Testing]

    B --> F[📊 Automated Backups]
    B --> G[☁️ Offsite Storage]
    B --> H[📈 Retention Policies]

    C --> I[🖥️ Multi-Region Deployment]
    C --> J[🗄️ Database Replication]
    C --> K[📦 Application Redundancy]

    D --> L[📋 Step-by-Step Procedures]
    D --> M[👥 Responsibility Assignment]
    D --> N[⏰ Recovery Time Objectives]

    E --> O[🧪 Backup Testing]
    E --> P[📊 Recovery Testing]
    E --> Q[📋 Plan Updates]

    R[📊 Recovery Metrics] --> S[⏰ RTO - Recovery Time]
    S --> T[📊 RPO - Recovery Point]
    T --> U[💰 Recovery Cost]
```

---

## 🎯 CONCLUSION

Kiến trúc hệ thống của dự án "Ông Ba Dạy Hóa" được thiết kế với:

- **🏗️ Scalable Architecture**: Microservices-oriented với khả năng mở rộng
- **🔒 Security-First**: Multi-layer security với authentication & authorization
- **⚡ Performance Optimized**: Caching, CDN, database optimization
- **📊 Observable**: Comprehensive monitoring & logging
- **🛡️ Fault Tolerant**: Circuit breakers, retry mechanisms, fallback strategies
- **🚀 Deployment Ready**: CI/CD, containerization, auto-scaling
- **🧪 Testable**: Comprehensive testing strategy từ unit đến E2E

**Kiến trúc system đảm bảo reliability, scalability và maintainability! 🚀**
