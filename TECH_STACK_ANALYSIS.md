# 🛠️ PHÂN TÍCH TECH STACK - "ÔNG BA DẠY HÓA"

## 📋 TỔNG QUAN CÔNG NGHỆ

Dự án "Ông Ba Dạy Hó<PERSON>" sử dụng kiến trúc **Full-Stack Modern** với sự kết hợp hoàn hảo gi<PERSON>a các công nghệ hiện đại:

```mermaid
mindmap
  root((🏗️ Tech Stack))
    Frontend
      Next.js 15
      React 19
      TypeScript
      Tailwind CSS
    Backend
      Strapi 5
      Node.js 18+
      TypeScript
      MySQL
    Database
      MySQL 8.0
      Connection Pooling
      Migration Support
    External Services
      PayOS Payment
      Brevo Email
      Google OAuth
      AWS S3 Storage
    Development Tools
      ESLint
      Jest Testing
      PM2 Process Manager
      Docker Support
```

---

## 🌐 FRONTEND STACK

### Core Framework: Next.js 15

```mermaid
graph TD
    A[🏠 Next.js 15] --> B[📱 App Router]
    A --> C[⚛️ React 19]
    A --> D[🔄 Server Components]
    A --> E[📦 Client Components]

    B --> F[🏗️ File-based Routing]
    B --> G[📊 SEO Optimization]
    B --> H[🚀 Performance]

    C --> I[🔗 React Hooks]
    C --> J[⚡ Concurrent Features]
    C --> K[📱 Mobile-First]

    D --> L[⚡ Server-Side Rendering]
    D --> M[📈 Better Performance]
    D --> N[🔍 SEO Friendly]

    E --> O[🎮 Interactive UI]
    E --> P[📊 State Management]
    E --> Q[🌐 Browser APIs]
```

#### Next.js 15 Features Used:
- **App Router**: Routing hiện đại, không cần pages/
- **Server Components**: Render ở server, tối ưu performance
- **Middleware**: Xử lý auth, redirects
- **Image Optimization**: Tự động optimize hình ảnh
- **Font Optimization**: Tự động load font Google

### Styling: Tailwind CSS

```mermaid
flowchart TD
    A[🎨 Tailwind CSS] --> B[📝 Utility-First]
    A --> C[🔧 Custom Config]
    A --> D[📱 Responsive Design]
    A --> E[🌙 Dark Mode Ready]

    B --> F[⚡ Fast Development]
    B --> G[📦 Small Bundle Size]
    B --> H[🎯 Consistent Design]

    C --> I[🎨 Custom Colors]
    C --> J[📏 Custom Spacing]
    C --> K[🔤 Custom Fonts]

    D --> L[📱 Mobile-First]
    D --> M[💻 Tablet Support]
    D --> N[🖥️ Desktop Optimized]
```

#### Tailwind Configuration:
```javascript
// tailwind.config.js
module.exports = {
  content: ['./app/**/*.{js,ts,jsx,tsx}', './components/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: '#198C43',
        secondary: '#2563eb',
        accent: '#f59e0b'
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif']
      }
    }
  }
}
```

### HTTP Client: Axios

```mermaid
flowchart LR
    A[🌐 Frontend] --> B[📡 Axios Instance]
    B --> C[🔗 Base URL Config]
    B --> D[🔐 Interceptors]
    B --> E[🍪 Cookie Handling]

    C --> F[🏠 Local: localhost:1337]
    C --> G[🌐 Prod: api.ongbadayhoa.com]

    D --> H[🔑 Auto Token Attach]
    D --> I[⚠️ Error Handling]
    D --> J[🔄 Request/Response Log]

    E --> K[💾 Secure Storage]
    E --> L[🔒 HTTP-Only Cookies]
    E --> M[⏰ Auto Expiry]
```

---

## 🖥️ BACKEND STACK

### Core Framework: Strapi 5

```mermaid
graph TD
    A[🖥️ Strapi 5] --> B[🏗️ Headless CMS]
    A --> C[📡 REST API]
    A --> D[🔧 Custom Logic]
    A --> E[👥 User Management]

    B --> F[📝 Content Types]
    B --> G[🔐 Permissions]
    B --> H[🌐 API Access]

    C --> I[📊 Auto-generated APIs]
    C --> J[🔄 CRUD Operations]
    C --> K[📋 Query Parameters]

    D --> L[🎯 Custom Controllers]
    D --> M[🔄 Business Logic]
    D --> N[📈 Data Processing]

    E --> O[👤 User Profiles]
    E --> P[🔐 Authentication]
    E --> Q[👥 Roles & Permissions]
```

#### Strapi Architecture:
```mermaid
flowchart TD
    A[🌐 Request] --> B[🔐 Authentication]
    B --> C{Is Authenticated?}
    C -->|Yes| D[📝 Content Type]
    C -->|No| E[🚫 Access Denied]

    D --> F[🔄 Controller]
    F --> G[⚙️ Service]
    G --> H[🗄️ Database]
    H --> I[📤 Response]

    J[🎯 Custom Logic] --> F
    K[📧 Email Service] --> G
    L[💳 Payment API] --> G
```

### Database: MySQL

```mermaid
graph TD
    A[🗄️ MySQL 8.0] --> B[📊 Relational Data]
    A --> C[🔄 Transactions]
    A --> D[🔍 Indexing]
    A --> E[📈 Performance]

    B --> F[👥 Users Table]
    B --> G[📚 Courses Table]
    B --> H[💳 Orders Table]
    B --> I[📧 OTP Table]

    C --> J[💰 Payment Processing]
    C --> K[📝 User Registration]
    C --> L[📊 Progress Tracking]

    D --> M[🚀 Fast Queries]
    D --> N[📊 Optimized Search]
    D --> O[⚡ Better Performance]
```

#### Database Schema Overview:
```sql
-- Main Tables
users (id, email, username, role, created_at)
courses (id, title, slug, description, price, created_at)
chapters (id, course_id, title, order, created_at)
orders (id, user_id, course_id, payment_status, amount, created_at)
streak_questions (id, course_id, value, created_at)
questions_answers (id, user_id, streak_question_id, answer, is_correct, created_at)
```

### Runtime: Node.js 18+

```mermaid
flowchart TD
    A[🟢 Node.js 18+] --> B[⚡ V8 Engine]
    A --> C[📦 NPM Packages]
    A --> D[🔧 TypeScript Support]

    B --> E[🚀 Fast Execution]
    B --> F[💾 Memory Management]
    B --> G[🔄 Event Loop]

    C --> H[📚 Strapi Framework]
    C --> I[📧 Email Libraries]
    C --> J[💳 Payment SDKs]

    D --> K[🔍 Type Safety]
    D --> L[🛠️ Better Development]
    D --> M[📖 Code Documentation]
```

---

## 🔗 EXTERNAL SERVICES INTEGRATION

### Payment Gateway: PayOS

```mermaid
flowchart LR
    A[🛒 User] --> B[💳 PayOS]
    B --> C[🏦 Bank Transfer]
    B --> D[💳 Credit Card]
    B --> E[📱 E-wallet]

    F[🔗 Webhook] --> G[🖥️ Backend]
    G --> H[📊 Update Order Status]
    G --> I[📧 Send Confirmation Email]
    G --> J[🎯 Activate Course Access]
```

### Email Service: Brevo (SendinBlue)

```mermaid
flowchart TD
    A[📧 Email Events] --> B[🎯 Brevo API]
    B --> C[📝 Template Engine]
    B --> D[📨 SMTP Server]

    C --> E[🔐 OTP Template]
    C --> F[💳 Payment Confirmation]
    C --> G[📊 Course Access]

    D --> H[📬 Send OTP]
    D --> I[📄 Send Invoice]
    D --> J[🎓 Send Course Access]
```

### Authentication: Google OAuth

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 User
    participant F as 🌐 Frontend
    participant G as 🔐 Google OAuth
    participant B as 🖥️ Backend

    U->>F: Click "Đăng nhập Google"
    F->>G: Redirect to Google
    G->>U: Google Login Form
    U->>G: Enter credentials
    G->>F: Authorization Code
    F->>B: Exchange code for token
    B->>G: Verify token
    G->>B: User profile data
    B->>F: JWT token
    F->>U: Login success
```

---

## 🛠️ DEVELOPMENT & DEPLOYMENT TOOLS

### Development Tools

```mermaid
mindmap
  root((🛠️ Development))
    Code Quality
      ESLint
      Prettier
      TypeScript
    Testing
      Jest
      React Testing Library
      API Testing
    Version Control
      Git
      GitHub
      Branch Strategy
    Package Management
      npm
      Package Lock
      Security Audit
```

### Deployment Tools

```mermaid
graph TD
    A[🚀 Deployment] --> B[📦 PM2]
    A --> C[🐳 Docker]
    A --> D[☁️ Cloud Hosting]

    B --> E[🔄 Process Management]
    B --> F[📊 Monitoring]
    B --> G[🔄 Auto Restart]

    C --> H[📦 Containerization]
    C --> I[🔧 Environment Config]
    C --> J[📈 Scalability]

    D --> K[🌐 CDN]
    D --> L[🛡️ Security]
    D --> M[📈 Performance]
```

### Monitoring & Analytics

```mermaid
flowchart TD
    A[📊 Analytics] --> B[🔥 Google Analytics]
    A --> C[📈 Hotjar]
    A --> D[📊 Custom Dashboard]

    B --> E[👥 User Behavior]
    B --> F[📈 Conversion Tracking]
    B --> G[🎯 Goal Completion]

    C --> H[🖱️ Click Tracking]
    C --> I[📱 User Recordings]
    C --> J[🔥 Heatmaps]

    D --> K[📊 Revenue Metrics]
    D --> L[📚 Course Engagement]
    D --> M[👨‍🎓 Student Progress]
```

---

## 📊 TECH STACK COMPATIBILITY MATRIX

| Công nghệ | Frontend | Backend | Database | External |
|-----------|----------|---------|----------|----------|
| **Next.js** | ✅ Core | ❌ | ❌ | ❌ |
| **Strapi** | ❌ | ✅ Core | ❌ | ❌ |
| **MySQL** | ❌ | ❌ | ✅ Core | ❌ |
| **PayOS** | ❌ | ✅ API | ❌ | ✅ Payment |
| **Brevo** | ❌ | ✅ Email | ❌ | ✅ Communication |
| **Google OAuth** | ✅ Auth | ✅ Verify | ❌ | ✅ Authentication |
| **AWS S3** | ❌ | ✅ Storage | ❌ | ✅ File Storage |

---

## 🔄 WORKFLOW INTEGRATION

### Development Workflow

```mermaid
journey
    title Development Workflow

    section Planning
        Developer: 4: Analyze requirements
        Developer: 5: Design API endpoints
        Developer: 4: Plan database schema

    section Development
        Developer: 5: Build backend API
        Developer: 4: Create frontend components
        Developer: 5: Integrate APIs
        Developer: 4: Test functionality

    section Testing
        Developer: 5: Unit tests
        Developer: 4: Integration tests
        Developer: 5: User acceptance tests

    section Deployment
        Developer: 4: Build production
        Developer: 5: Deploy to server
        Developer: 4: Monitor performance
```

### CI/CD Pipeline

```mermaid
flowchart TD
    A[🔄 Code Push] --> B[📋 Lint & Test]
    B --> C{✅ Pass?}
    C -->|Yes| D[📦 Build]
    C -->|No| E[🔧 Fix Issues]

    D --> F[🐳 Docker Build]
    F --> G[☁️ Deploy to Staging]
    G --> H[🧪 Integration Test]
    H --> I{✅ Pass?}
    I -->|Yes| J[🚀 Deploy to Production]
    I -->|No| K[🔍 Debug]

    J --> L[📊 Monitoring]
    L --> M[🚨 Alert if Issues]
```

---

## 🎯 PERFORMANCE OPTIMIZATIONS

### Frontend Optimizations

```mermaid
mindmap
  root((⚡ Frontend Performance))
    Image Optimization
      Next.js Image Component
      WebP Format
      Lazy Loading
    Code Splitting
      Dynamic Imports
      Route-based Splitting
      Component Lazy Loading
    Caching Strategy
      Browser Cache
      CDN Cache
      Service Worker
    Bundle Optimization
      Tree Shaking
      Code Minification
      Compression
```

### Backend Optimizations

```mermaid
mindmap
  root((🚀 Backend Performance))
    Database Optimization
      Connection Pooling
      Query Indexing
      Caching Layer
    API Optimization
      Response Caching
      Rate Limiting
      Request Batching
    Server Optimization
      PM2 Clustering
      Load Balancing
      Memory Management
```

---

## 🔒 SECURITY CONSIDERATIONS

### Authentication & Authorization

```mermaid
flowchart TD
    A[🔐 Security Layer] --> B[JWT Tokens]
    A --> C[Role-Based Access]
    A --> D[Secure Cookies]

    B --> E[Access Token]
    B --> F[Refresh Token]
    B --> G[Token Expiry]

    C --> H[Student Role]
    C --> I[Teacher Role]
    C --> J[Admin Role]

    D --> K[HTTP-Only]
    D --> L[Secure Flag]
    D --> M[Same-Site Policy]
```

### Data Protection

```mermaid
flowchart TD
    A[🛡️ Data Security] --> B[Encryption]
    A --> C[Input Validation]
    A --> D[SQL Injection Prevention]

    B --> E[HTTPS Only]
    B --> F[Data at Rest]
    B --> G[Data in Transit]

    C --> H[Sanitize Input]
    C --> I[Validate Schema]
    C --> J[Type Checking]

    D --> K[Prepared Statements]
    D --> L[ORM Protection]
    D --> M[Query Sanitization]
```

---

## 📈 SCALABILITY CONSIDERATIONS

### Horizontal Scaling

```mermaid
graph TD
    A[📈 Scale Out] --> B[🔄 Load Balancer]
    B --> C[🖥️ Server 1]
    B --> D[🖥️ Server 2]
    B --> E[🖥️ Server 3]

    C --> F[🗄️ Database Cluster]
    D --> F
    E --> F

    F --> G[📊 Read Replicas]
    F --> H[📈 Auto Scaling]
    F --> I[🔄 Failover]
```

### Database Scaling

```mermaid
flowchart TD
    A[🗄️ Database Scaling] --> B[Read Replicas]
    A --> C[Sharding]
    A --> D[Connection Pooling]

    B --> E[📖 Read Operations]
    B --> F[📈 Performance Boost]
    B --> G[🔄 Auto Sync]

    C --> H[📊 Data Distribution]
    C --> I[📈 Capacity Increase]
    C --> J[🔄 Complex Queries]

    D --> K[🔗 Connection Reuse]
    D --> L[📈 Concurrent Users]
    D --> M[💾 Memory Efficiency]
```

---

## 🎯 CONCLUSION

Tech stack của dự án "Ông Ba Dạy Hóa" được thiết kế với:

- **🚀 Performance**: Next.js + Strapi cho tốc độ tối ưu
- **🔒 Security**: JWT + OAuth + Secure practices
- **📈 Scalability**: Database clustering + Load balancing
- **🛠️ Developer Experience**: TypeScript + Modern tooling
- **💰 Cost Efficiency**: Open source + Cloud services
- **🔧 Maintainability**: Well-structured architecture

**Kết hợp hoàn hảo giữa công nghệ hiện đại và thực tế triển khai! 🎉**
