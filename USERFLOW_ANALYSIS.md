# 👥 PHÂN TÍCH USER FLOW CHI TIẾT - "ÔNG BA DẠY HÓA"

## 📋 TỔNG QUAN USER JOURNEY

Dự án "Ông Ba Dạy Hóa" phục vụ 3 nhóm người dùng chính với những hành trình khác nhau:

```mermaid
journey
    title User Journey Overview

    section Học sinh
        Học sinh: 5: Tìm hiểu về khóa học
        Học sinh: 4: Đăng ký tài khoản
        Học sinh: 5: Họ<PERSON> tập và luyện tập
        Học sinh: 4: <PERSON> dõi tiến độ
        Học sinh: 3: Tham gia lớp học live

    section Phụ huynh
        Phụ huynh: 4: Tìm hiểu chương trình học
        Phụ huynh: 3: <PERSON> dõi tiến độ con
        <PERSON><PERSON> huynh: 4: <PERSON>h to<PERSON> học phí
        <PERSON>ụ huynh: 3: N<PERSON><PERSON>n thông báo từ trường

    section Gi<PERSON>o viên
        Gi<PERSON><PERSON> viên: 4: Qu<PERSON>n lý nội dung bài giảng
        Giáo viên: 3: T<PERSON><PERSON> bài tập và quiz
        Giáo viên: 4: <PERSON> dõi học sinh
        Giáo viên: 3: Dạy lớp học live
```

---

## 🔐 AUTHENTICATION FLOWS

### 1. User Registration Flow

```mermaid
journey
    title Đăng ký tài khoản

    section Khám phá
        Học sinh: 5: Truy cập trang chủ
        Học sinh: 4: Xem giới thiệu khóa học
        Học sinh: 5: Click "Đăng ký ngay"

    section Đăng ký
        Học sinh: 5: Nhập thông tin cá nhân
        Học sinh: 4: Nhập email và mật khẩu
        Học sinh: 5: Xác nhận điều khoản
        Học sinh: 4: Submit form đăng ký

    section Xác thực
        Hệ thống: 3: Gửi OTP về email
        Học sinh: 5: Nhận email OTP
        Học sinh: 4: Nhập mã OTP
        Học sinh: 5: Xác thực thành công

    section Hoàn thiện hồ sơ
        Hệ thống: 4: Chuyển đến trang thông tin cá nhân
        Học sinh: 5: Điền thông tin chi tiết
        Học sinh: 4: Chọn cấp học và sở thích
        Học sinh: 5: Hoàn thành setup
```

**Detailed Flow:**
```mermaid
stateDiagram-v2
    [*] --> VisitHomepage
    VisitHomepage --> ViewCourses: Click "Khóa học"
    ViewCourses --> ClickRegister: Interested in course
    ClickRegister --> EnterDetails: Open registration form
    EnterDetails --> SubmitForm: Fill required fields
    SubmitForm --> SendOTP: Valid information
    SendOTP --> EnterOTP: OTP sent to email
    EnterOTP --> OTPValid: Correct OTP
    EnterOTP --> OTPInvalid: Wrong OTP
    OTPInvalid --> EnterOTP: Try again
    OTPValid --> CompleteProfile: OTP verified
    CompleteProfile --> [*]: Profile complete

    note right of SendOTP
        Email chứa:
        - Mã OTP 6 số
        - Hướng dẫn xác thực
        - Link đăng nhập
    end note

    note right of CompleteProfile
        Thông tin bắt buộc:
        - Họ tên đầy đủ
        - Ngày sinh
        - Giới tính
        - Số điện thoại
    end note
```

### 2. Google OAuth Login Flow

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 Học sinh
    participant F as 🌐 Frontend
    participant G as 🔐 Google OAuth
    participant B as 🖥️ Backend
    participant D as 🗄️ Database

    U->>F: Click "Đăng nhập Google"
    F->>G: Redirect với OAuth URL
    G->>U: Google login page
    U->>G: Enter Google credentials
    G->>F: Authorization code
    F->>B: Exchange code for tokens
    B->>G: Verify token & get user info
    G->>B: User profile data
    B->>D: Check existing user
    D-->>B: User found/not found

    alt User exists
        B->>B: Update Google token
        B->>F: JWT + user data
    else User doesn't exist
        B->>D: Create new user
        D-->>B: New user created
        B->>F: JWT + user data
    end

    F->>U: Login success, redirect
    F->>F: Store JWT in cookie
```

### 3. Password Reset Flow

```mermaid
journey
    title Khôi phục mật khẩu

    section Yêu cầu reset
        Học sinh: 5: Click "Quên mật khẩu"
        Học sinh: 4: Nhập địa chỉ email
        Học sinh: 5: Click "Gửi yêu cầu"

    section Xác thực email
        Hệ thống: 3: Kiểm tra email tồn tại
        Hệ thống: 4: Gửi email reset với OTP
        Học sinh: 5: Nhận email và click link
        Học sinh: 4: Nhập mã OTP

    section Đặt lại mật khẩu
        Hệ thống: 4: Xác thực OTP thành công
        Học sinh: 5: Nhập mật khẩu mới
        Học sinh: 4: Xác nhận mật khẩu mới
        Học sinh: 5: Click "Cập nhật mật khẩu"

    section Hoàn thành
        Hệ thống: 4: Cập nhật mật khẩu trong database
        Hệ thống: 5: Gửi email xác nhận
        Học sinh: 5: Đăng nhập với mật khẩu mới
```

---

## 🛒 PURCHASE & PAYMENT FLOWS

### 4. Course Purchase Flow

```mermaid
journey
    title Mua khóa học

    section Chọn khóa học
        Học sinh: 5: Xem danh sách khóa học
        Học sinh: 4: Click vào khóa học quan tâm
        Học sinh: 5: Đọc mô tả và đánh giá
        Học sinh: 4: Chọn gói học phù hợp

    section Chuẩn bị thanh toán
        Học sinh: 5: Click "Mua ngay" hoặc "Thêm vào giỏ"
        Hệ thống: 4: Kiểm tra đăng nhập
        Hệ thống: 5: Hiển thị trang thanh toán
        Học sinh: 5: Kiểm tra thông tin đơn hàng

    section Xử lý thanh toán
        Học sinh: 5: Nhập thông tin giao hàng
        Học sinh: 4: Chọn phương thức thanh toán
        Học sinh: 5: Áp dụng mã giảm giá (nếu có)
        Học sinh: 4: Click "Thanh toán"

    section Hoàn tất giao dịch
        Hệ thống: 3: Tạo link PayOS
        Học sinh: 5: Thanh toán trên PayOS
        Hệ thống: 4: Nhận webhook thanh toán
        Hệ thống: 5: Cập nhật trạng thái đơn hàng
        Học sinh: 5: Nhận email xác nhận
```

**Payment State Flow:**
```mermaid
stateDiagram-v2
    [*] --> CourseSelected
    CourseSelected --> CartReview: Add to cart
    CartReview --> CheckoutPage: Proceed to checkout
    CheckoutPage --> PaymentProcessing: Submit payment

    PaymentProcessing --> PayOSGateway: Create payment link
    PayOSGateway --> UserPayment: Redirect to PayOS
    UserPayment --> PaymentSuccess: Payment completed
    UserPayment --> PaymentFailed: Payment failed

    PaymentSuccess --> OrderConfirmation: Update order status
    OrderConfirmation --> EmailNotification: Send confirmation
    EmailNotification --> CourseAccess: Grant course access
    CourseAccess --> [*]: Access granted

    PaymentFailed --> RetryPayment: Try again
    RetryPayment --> PayOSGateway
```

### 5. Voucher Application Flow

```mermaid
sequenceDiagram
    participant U as 👨‍🎓 Học sinh
    participant F as 🌐 Frontend
    participant B as 🖥️ Backend
    participant D as 🗄️ Database

    U->>F: Enter voucher code
    F->>B: POST /vouchers/validate
    B->>D: Query voucher details
    D-->>B: Voucher data

    alt Valid voucher
        B->>B: Check usage limits
        B->>B: Calculate discount
        B-->>F: Valid + discount amount
        F->>U: Show discount applied
    else Invalid voucher
        B-->>F: Error message
        F->>U: Show error
    end

    U->>F: Complete purchase
    F->>B: POST /vouchers/increment-uses
    B->>D: Update voucher usage
    D-->>B: Usage updated
    B-->>F: Purchase confirmed
```

---

## 📚 LEARNING FLOWS

### 6. Course Learning Flow

```mermaid
journey
    title Hành trình học tập

    section Truy cập khóa học
        Học sinh: 5: Đăng nhập vào tài khoản
        Học sinh: 4: Vào trang "Quản lý học tập"
        Học sinh: 5: Chọn khóa học đã mua
        Học sinh: 4: Xem danh sách chương học

    section Học lý thuyết
        Học sinh: 5: Chọn chương muốn học
        Học sinh: 4: Xem video bài giảng
        Học sinh: 5: Đọc tài liệu lý thuyết
        Học sinh: 4: Làm ghi chú cá nhân

    section Luyện tập
        Học sinh: 5: Chọn bài tập tương ứng
        Học sinh: 4: Đọc đề bài và hướng dẫn
        Học sinh: 5: Giải bài tập trên giấy
        Học sinh: 4: Kiểm tra đáp án

    section Kiểm tra
        Học sinh: 5: Làm bài kiểm tra chương
        Học sinh: 4: Nhận kết quả và đánh giá
        Học sinh: 5: Xem lời giải chi tiết
        Học sinh: 4: Luyện tập thêm nếu cần

    section Hoàn thành chương
        Hệ thống: 4: Cập nhật tiến độ học tập
        Hệ thống: 5: Mở khóa chương tiếp theo
        Học sinh: 5: Nhận thông báo thành tích
        Học sinh: 4: Tiếp tục học chương mới
```

**Learning Progress Flow:**
```mermaid
stateDiagram-v2
    [*] --> CourseOverview
    CourseOverview --> ChapterSelection: Select chapter
    ChapterSelection --> VideoLearning: Watch video
    VideoLearning --> MaterialReading: Read materials
    MaterialReading --> PracticeExercises: Do exercises
    PracticeExercises --> QuizAssessment: Take quiz
    QuizAssessment --> ChapterComplete: Pass quiz
    ChapterComplete --> NextChapter: Unlock next
    NextChapter --> ChapterSelection

    note right of VideoLearning
        Features:
        - Video player với controls
        - Progress tracking
        - Bookmark functionality
        - Note-taking
    end note

    note right of PracticeExercises
        Exercise types:
        - Multiple choice
        - Fill in blanks
        - Essay questions
        - Lab simulations
    end note
```

### 7. Streak System Flow

```mermaid
journey
    title Hệ thống Streak

    section Bắt đầu ngày học
        Học sinh: 5: Vào trang "Streak"
        Học sinh: 4: Xem câu hỏi của ngày
        Học sinh: 5: Đọc đề bài kỹ lưỡng
        Học sinh: 4: Chuẩn bị giải bài

    section Làm bài
        Học sinh: 5: Giải bài tập trên giấy
        Học sinh: 4: Nhập đáp án vào hệ thống
        Học sinh: 5: Kiểm tra kết quả
        Học sinh: 4: Xem lời giải nếu sai

    section Hoàn thành streak
        Hệ thống: 4: Tính điểm và thời gian
        Hệ thống: 5: Cập nhật streak counter
        Học sinh: 5: Nhận điểm thưởng
        Học sinh: 4: Xem xếp hạng

    section Duy trì streak
        Hệ thống: 5: Gửi reminder học tập
        Học sinh: 5: Học đều đặn hàng ngày
        Hệ thống: 4: Theo dõi progress
        Học sinh: 4: Nhận achievement badges
```

**Streak State Flow:**
```mermaid
stateDiagram-v2
    [*] --> StreakAvailable
    StreakAvailable --> QuestionDisplayed: Start streak
    QuestionDisplayed --> AnswerSubmitted: Submit answer
    AnswerSubmitted --> AnswerCorrect: Correct answer
    AnswerSubmitted --> AnswerIncorrect: Wrong answer

    AnswerCorrect --> StreakCompleted: All questions done
    AnswerIncorrect --> RetryQuestion: Try again
    RetryQuestion --> AnswerSubmitted

    StreakCompleted --> PointsAwarded: Calculate points
    PointsAwarded --> LeaderboardUpdated: Update rankings
    LeaderboardUpdated --> AchievementCheck: Check badges
    AchievementCheck --> [*]: Day complete

    note right of StreakAvailable
        Streak rules:
        - 1 streak per day
        - Time limit: 30 minutes
        - Points based on speed & accuracy
    end note
```

---

## 👨‍🏫 TEACHER FEATURES FLOWS

### 8. Content Creation Flow

```mermaid
journey
    title Giáo viên tạo nội dung

    section Chuẩn bị
        Giáo viên: 5: Đăng nhập vào hệ thống quản trị
        Giáo viên: 4: Chọn "Quản lý nội dung"
        Giáo viên: 5: Xem danh sách bài học hiện có
        Giáo viên: 4: Quyết định tạo bài học mới

    section Tạo nội dung
        Giáo viên: 5: Nhập thông tin cơ bản bài học
        Giáo viên: 4: Upload video bài giảng
        Giáo viên: 5: Viết tài liệu lý thuyết
        Giáo viên: 4: Tạo bài tập thực hành

    section Tạo câu hỏi
        Giáo viên: 5: Thiết kế câu hỏi trắc nghiệm
        Giáo viên: 4: Nhập đáp án và lời giải
        Giáo viên: 5: Cấu hình độ khó
        Giáo viên: 4: Test thử câu hỏi

    section Xuất bản
        Giáo viên: 5: Preview toàn bộ bài học
        Giáo viên: 4: Chỉnh sửa nếu cần
        Giáo viên: 5: Publish bài học
        Giáo viên: 4: Thông báo học sinh
```

### 9. Live Class Management Flow

```mermaid
sequenceDiagram
    participant T as 👨‍🏫 Giáo viên
    participant S as 🖥️ Strapi Admin
    participant B as 🖥️ Backend
    participant U as 👨‍🎓 Học sinh

    T->>S: Login to admin panel
    S->>B: Authenticate teacher
    B-->>S: Access granted

    T->>S: Create live class schedule
    S->>B: POST /class-schedules
    B->>B: Validate schedule data
    B-->>S: Schedule created

    T->>S: Start live class
    S->>B: Update class status
    B->>U: Send push notification
    U-->>B: Join class request

    T->>S: Share presentation
    S->>B: Upload presentation
    B->>U: Send presentation to students

    T->>S: End live class
    S->>B: Update class status
    B->>U: Send class summary
```

---

## 📊 DASHBOARD & ANALYTICS FLOWS

### 10. Student Dashboard Flow

```mermaid
journey
    title Học sinh sử dụng Dashboard

    section Tổng quan
        Học sinh: 5: Đăng nhập vào tài khoản
        Học sinh: 4: Vào trang "Quản lý học tập"
        Học sinh: 5: Xem tổng quan tiến độ
        Học sinh: 4: Kiểm tra streak hiện tại

    section Theo dõi học tập
        Học sinh: 5: Xem danh sách khóa học
        Học sinh: 4: Kiểm tra tiến độ từng khóa
        Học sinh: 5: Xem lịch học và deadline
        Học sinh: 4: Nhận thông báo nhắc nhở

    section Báo cáo thành tích
        Học sinh: 5: Xem điểm số và xếp hạng
        Học sinh: 4: So sánh với bạn bè
        Học sinh: 5: Xem biểu đồ tiến bộ
        Học sinh: 4: Nhận certificate hoàn thành
```

### 11. Progress Tracking Flow

```mermaid
stateDiagram-v2
    [*] --> Login
    Login --> DashboardOverview: Successful login
    DashboardOverview --> CourseProgress: View progress
    CourseProgress --> ChapterDetails: Select chapter
    ChapterDetails --> VideoProgress: Check video watched
    VideoProgress --> ExerciseProgress: Check exercises done
    ExerciseProgress --> QuizResults: View quiz scores
    QuizResults --> OverallProgress: Calculate completion
    OverallProgress --> [*]: Update dashboard

    note right of DashboardOverview
        Dashboard shows:
        - Current streak
        - Chemistry points
        - Course completion %
        - Upcoming deadlines
    end note

    note right of ChapterDetails
        Chapter progress includes:
        - Videos watched (duration)
        - Exercises completed
        - Quiz scores
        - Time spent
    end note
```

---

## 📧 NOTIFICATION FLOWS

### 12. Notification System Flow

```mermaid
sequenceDiagram
    participant S as 📧 System
    participant B as 🖥️ Backend
    participant D as 🗄️ Database
    participant U as 👨‍🎓 User
    participant E as 📧 Email Service

    S->>B: Trigger notification event
    B->>D: Query user preferences
    D-->>B: User notification settings

    alt Email notifications enabled
        B->>E: Send email notification
        E-->>U: Email received
    end

    alt In-app notifications enabled
        B->>D: Store notification in DB
        D-->>B: Notification stored
        B->>U: Push notification via WebSocket
    end

    alt SMS notifications enabled
        B->>E: Send SMS notification
        E-->>U: SMS received
    end

    U->>B: Mark notification as read
    B->>D: Update notification status
    D-->>B: Status updated
```

**Notification Types:**
- **Learning Reminders**: Nhắc nhở học tập hàng ngày
- **Live Class Notifications**: Thông báo lớp học sắp bắt đầu
- **Progress Updates**: Cập nhật tiến độ học tập
- **Achievement Unlocks**: Mở khóa thành tích mới
- **Payment Confirmations**: Xác nhận thanh toán
- **System Announcements**: Thông báo từ hệ thống

---

## 🛠️ ADMIN & MANAGEMENT FLOWS

### 13. Content Management Flow

```mermaid
journey
    title Quản lý nội dung

    section Truy cập
        Admin: 5: Đăng nhập vào Strapi Admin
        Admin: 4: Xem Content Manager
        Admin: 5: Chọn Collection Type
        Admin: 4: Xem danh sách entries

    section Tạo nội dung
        Admin: 5: Click "Create new entry"
        Admin: 4: Điền thông tin cơ bản
        Admin: 5: Upload media files
        Admin: 4: Thiết lập quan hệ dữ liệu

    section Review & Publish
        Admin: 5: Preview nội dung
        Admin: 4: Kiểm tra validation
        Admin: 5: Publish hoặc Save as Draft
        Admin: 4: Xem trên frontend

    section Maintenance
        Admin: 5: Theo dõi performance
        Admin: 4: Update nội dung cũ
        Admin: 5: Backup dữ liệu
        Admin: 4: Optimize media files
```

### 14. User Management Flow

```mermaid
stateDiagram-v2
    [*] --> UserList
    UserList --> UserDetails: Select user
    UserDetails --> EditUser: Click edit
    EditUser --> UpdateProfile: Modify details
    UpdateProfile --> SaveChanges: Submit changes
    SaveChanges --> UserList: Return to list

    UserList --> CreateUser: Add new user
    CreateUser --> EnterDetails: Fill user info
    EnterDetails --> SetPermissions: Configure roles
    SetPermissions --> SendInvite: Send invitation
    SendInvite --> UserList: User created

    UserList --> DeactivateUser: Select user
    DeactivateUser --> ConfirmDeactivation: Confirm action
    ConfirmDeactivation --> UserDeactivated: User blocked
    UserDeactivated --> UserList: Return to list

    note right of SetPermissions
        Permission levels:
        - Student: Basic access
        - Teacher: Content creation
        - Admin: Full system access
    end note
```

---

## 🔄 ERROR & EDGE CASE FLOWS

### 15. Error Handling Flow

```mermaid
flowchart TD
    A[🚨 Error Occurs] --> B{Error Type}

    B -->|Network Error| C[📡 Retry Request]
    C --> D{Success?}
    D -->|Yes| E[✅ Continue]
    D -->|No| F[⚠️ Show Offline Mode]

    B -->|Authentication Error| G[🔐 Refresh Token]
    G --> H{Token Valid?}
    H -->|Yes| I[🔄 Retry Request]
    H -->|No| J[📝 Redirect to Login]

    B -->|Validation Error| K[📝 Show Field Errors]
    K --> L[👤 Highlight Problem Fields]
    L --> M[💡 Show Help Text]

    B -->|Server Error| N[📊 Log Error]
    N --> O[📧 Notify Admin]
    O --> P[⚠️ Show User-Friendly Message]
    P --> Q[🔄 Offer Retry Option]

    B -->|Payment Error| R[💳 Show Payment Error]
    R --> S[🔄 Allow Retry]
    S --> T[📞 Provide Support Contact]
```

### 16. Offline Mode Flow

```mermaid
journey
    title Chế độ offline

    section Phát hiện offline
        Học sinh: 5: Sử dụng app bình thường
        Hệ thống: 3: Mất kết nối internet
        Hệ thống: 4: Chuyển sang chế độ offline
        Học sinh: 5: Nhận thông báo offline

    section Chức năng offline
        Học sinh: 5: Xem nội dung đã tải trước
        Học sinh: 4: Làm bài tập đã lưu
        Học sinh: 5: Đọc tài liệu PDF
        Học sinh: 4: Ghi chú cá nhân

    section Đồng bộ khi online
        Hệ thống: 4: Phát hiện kết nối trở lại
        Hệ thống: 5: Đồng bộ dữ liệu local
        Học sinh: 5: Xem tiến độ được cập nhật
        Hệ thống: 4: Upload bài làm và kết quả

    section Trải nghiệm liền mạch
        Học sinh: 5: Tiếp tục học tập bình thường
        Hệ thống: 5: Không mất dữ liệu học tập
        Học sinh: 5: Nhận thông báo khi online
```

---

## 🎯 CONCLUSION

User flows của dự án "Ông Ba Dạy Hóa" được thiết kế với:

- **👨‍🎓 Student-Centric**: Tập trung vào trải nghiệm học sinh
- **🔄 Seamless Journey**: Quy trình mượt mà từ khám phá đến học tập
- **📱 Multi-Device**: Tương thích trên mọi thiết bị
- **🛡️ Error Resilient**: Xử lý lỗi linh hoạt và thông minh
- **📊 Progress Tracking**: Theo dõi tiến độ chi tiết
- **🏆 Gamification**: Động lực học tập qua streak và điểm thưởng
- **📧 Communication**: Giao tiếp hiệu quả giữa tất cả stakeholders

**User flows tạo nên trải nghiệm học tập toàn diện và hấp dẫn! 🚀**
