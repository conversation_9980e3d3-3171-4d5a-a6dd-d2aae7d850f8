# 🖥️ Ông Ba Dạy Hóa - Backend (Strapi)

[![Strapi](https://img.shields.io/badge/Strapi-5.21.0-4945FF?style=flat&logo=strapi)](https://strapi.io/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-339933?style=flat&logo=node.js)](https://nodejs.org/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0-4479A1?style=flat&logo=mysql)](https://mysql.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-3178C6?style=flat&logo=typescript)](https://typescriptlang.org/)

> **Headless CMS Backend** - Nền tảng API mạnh mẽ cho nền tảng học hóa học trực tuyến

## 📋 Tổng quan

Backend của **Ông Ba Dạy H<PERSON>** được xây dựng trên **Strapi 5** - headless CMS hiện đại với kiến trúc API-first. <PERSON>ệ thống cung cấp 60+ RESTful endpoints cho việc quản lý nội dung, xác thực người dùng, thanh toán và các tính năng học tập.

### 🎯 Tính năng chính

| Tính năng | Mô tả | Trạng thái |
|-----------|-------|------------|
| 🔐 **Authentication** | JWT, Google OAuth, OTP verification | ✅ Hoàn thành |
| 📚 **Content Management** | Courses, chapters, exercises, blog | ✅ Hoàn thành |
| 💳 **Payment Integration** | PayOS gateway, orders, vouchers | ✅ Hoàn thành |
| 🎯 **Learning System** | Streaks, quizzes, progress tracking | ✅ Hoàn thành |
| 📧 **Email System** | Brevo integration, templates | ✅ Hoàn thành |
| 👨‍💼 **Admin Panel** | Content management, user management | ✅ Hoàn thành |

## 🏗️ Kiến trúc hệ thống

```mermaid
graph TD
    A[🌐 HTTP Request] --> B[🛡️ CORS Middleware]
    B --> C[🔐 JWT Authentication]
    C --> D[📝 Content Type Routes]
    D --> E[🎯 Custom Controllers]
    E --> F[⚙️ Business Services]
    F --> G[🗄️ MySQL Database]
    G --> H[📤 JSON Response]

    I[🔧 Custom Logic] --> E
    J[📧 Email Service] --> F
    K[💳 Payment API] --> F
    L[☁️ File Storage] --> F
```

## 📂 Cấu trúc dự án

```
backend/
├── 📦 src/                          # Source code
│   ├── 📡 api/                      # API endpoints (60+ routes)
│   │   ├── 🔐 auth/                 # Authentication system
│   │   │   ├── controllers/auth.ts  # Login, signup, OAuth
│   │   │   └── routes/auth.ts       # Auth routes
│   │   ├── 📚 course/               # Course management
│   │   │   ├── content-types/       # Course model
│   │   │   ├── controllers/         # Business logic
│   │   │   └── routes/              # API routes
│   │   ├── 💳 payment/              # Payment integration
│   │   ├── 🎯 streak/               # Streak system
│   │   ├── 📝 blog-post/            # Blog content
│   │   ├── 📧 send-otp/             # OTP system
│   │   ├── 🔔 notification/         # Notifications
│   │   └── ...                      # 50+ more APIs
│   ├── 📧 email-templates/          # Email templates
│   │   ├── 📧 OTP.html              # OTP verification
│   │   ├── 💳 Bill.html             # Invoice template
│   │   └── 🎓 PaymentConfirmation.html
│   ├── 🛡️ middlewares/              # Custom middleware
│   │   └── 📝 custom-middleware.ts  # Business rules
│   └── 🚀 index.ts                  # Application entry
├── ⚙️ config/                       # Configuration
│   ├── 🗄️ database.ts               # MySQL connection
│   ├── 🛡️ middlewares.ts            # Security & CORS
│   ├── 📡 server.ts                 # Server config
│   └── 👨‍💼 admin.ts                # Admin panel config
├── 🗄️ database/                     # Database migrations
├── 📧 public/                       # Static assets
├── 📦 dist/                         # Compiled code
├── 📊 types/                        # TypeScript definitions
└── 📋 Config files                  # Package, tsconfig, etc.
```

## 🛠️ Tech Stack

### Core Technologies

| Công nghệ | Version | Mục đích |
|-----------|---------|----------|
| **Strapi** | 5.21.0 | Headless CMS framework |
| **Node.js** | 18.0.0 - 22.x.x | JavaScript runtime |
| **TypeScript** | 5.x | Type safety & development |
| **MySQL** | 8.0 | Relational database |
| **Knex.js** | 2.5.1 | SQL query builder |

### Integrations

| Service | Purpose | Provider |
|---------|---------|----------|
| **PayOS** | Payment processing | PayOS.vn |
| **Brevo** | Email service | SendinBlue |
| **AWS S3** | File storage | Amazon Web Services |
| **Google OAuth** | Social authentication | Google |

## 🔗 API Endpoints (60+ APIs)

### 🔐 Authentication APIs

```typescript
// POST /api/auth/local - Local login
{
  "identifier": "<EMAIL>",
  "password": "password123"
}

// POST /api/auth/signup - User registration
{
  "email": "<EMAIL>",
  "password": "password123",
  "fullname": "Nguyễn Văn A",
  "phone": "**********"
}

// POST /api/auth/login - Google OAuth
{
  "email": "<EMAIL>",
  "provider": "google",
  "googleToken": "google-jwt-token"
}
```

### 📚 Content Management APIs

```typescript
// GET /api/courses - List courses with filters
GET /api/courses?filters[gradeId][$eq]=10&populate=*

// GET /api/courses/{id} - Get course details
GET /api/courses/course-doc-id?populate[chapters][populate]=exercises

// POST /api/blog-posts - Create blog post
{
  "data": {
    "title": "Bài viết về hóa học",
    "content": "Nội dung bài viết...",
    "featuredImage": imageId
  }
}
```

### 💳 Payment APIs

```typescript
// POST /api/payments/create-payment-link
{
  "orderId": "order-123",
  "amount": 500000,
  "description": "Thanh toán khóa học Hóa 10"
}

// POST /api/payments/verify
{
  "orderCode": "payos-order-code"
}
```

### 🎯 Learning APIs

```typescript
// POST /api/streaks - Start daily streak
{
  "data": {
    "user_id": "user-doc-id",
    "streak_question_id": "question-doc-id"
  }
}

// POST /api/questions-answers - Submit answer
{
  "data": {
    "user_id": "user-doc-id",
    "question_id": "question-doc-id",
    "answer": "A",
    "is_correct": true
  }
}
```

## 🗄️ Database Schema

```mermaid
erDiagram
    USER ||--o{ ORDER : places
    USER ||--o{ STREAK : participates
    USER ||--o{ QUESTIONS-ANSWER : submits

    COURSE ||--|{ CHAPTER : contains
    COURSE ||--o{ COURSE-TIER : has
    COURSE ||--o{ VIDEO-FEATURE : features

    CHAPTER ||--|{ KNOWLEDGE-SECTION : has
    CHAPTER ||--|{ EXERCISE : includes

    ORDER ||--|| COURSE : for
    ORDER ||--|| COURSE-TIER : specifies
    ORDER ||--o{ ACTIVATION-CODE : generates

    STREAK ||--|| STREAK-QUESTION : based_on
    STREAK-QUESTION ||--|{ QUESTION : contains

    QUESTIONS-ANSWER ||--|| QUESTION : answers
    QUESTIONS-ANSWER ||--|| STREAK : part_of

    BLOG-POST ||--|{ BLOG-CONTENT-SECTION : has
    BLOG-POST ||--o{ BLOG-CATEGORY : belongs_to

    VOUCHER ||--o{ ORDER : applies_to
    OTP ||--|| USER : sent_to
```

## ⚙️ Cấu hình

### Environment Variables (.env)

```bash
# Server Configuration
HOST=0.0.0.0
PORT=1337
NODE_ENV=production

# Database
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=ongbadayhoa
DATABASE_USERNAME=your_db_user
DATABASE_PASSWORD=your_db_password
DATABASE_SSL=false

# Authentication
JWT_SECRET=your-super-secret-jwt-key
API_TOKEN_SALT=your-api-token-salt

# Email Service (Brevo)
BREVO_API_KEY=your-brevo-api-key

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# PayOS Payment
PAYOS_CLIENT_ID=your-payos-client-id
PAYOS_API_KEY=your-payos-api-key
PAYOS_CHECKSUM_KEY=your-payos-checksum

# AWS S3
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_S3_BUCKET=your-bucket-name
```

### Database Configuration

```typescript
// config/database.ts
export default ({ env }) => ({
  connection: {
    client: 'mysql',
    connection: {
      host: env('DATABASE_HOST'),
      port: env.int('DATABASE_PORT'),
      database: env('DATABASE_NAME'),
      user: env('DATABASE_USERNAME'),
      password: env('DATABASE_PASSWORD'),
      ssl: env.bool('DATABASE_SSL'),
      connectTimeout: 10000,
    },
    options: {
      useNullAsDefault: true,
    },
  },
});
```

## 🚀 Cài đặt & Chạy

### Prerequisites

- **Node.js**: 18.0.0 - 22.x.x
- **MySQL**: 8.0 hoặc cao hơn
- **npm** hoặc **yarn**

### Installation

```bash
# 1. Clone repository
git clone <repository-url>
cd ong-b-a-day-hoa/backend

# 2. Install dependencies
npm install

# 3. Setup environment
cp .env.example .env
# Edit .env with your configuration

# 4. Setup database
# Create MySQL database
mysql -u root -p
CREATE DATABASE ongbadayhoa;
exit

# 5. Run migrations (if any)
npm run build
```

### Development

```bash
# Start development server
npm run develop

# Server sẽ chạy tại: http://localhost:1337
# Admin panel: http://localhost:1337/admin
```

### Production

```bash
# 1. Build application
npm run build

# 2. Start production server
npm run start

# 3. Hoặc sử dụng PM2
pm2 start ecosystem.config.js
```

## 🔧 Scripts

| Script | Mô tả |
|--------|-------|
| `npm run develop` | Development server với auto-reload |
| `npm run start` | Production server |
| `npm run build` | Build admin panel và optimize |
| `npm run seed:example` | Seed dữ liệu mẫu |

## 🛡️ Bảo mật

### Authentication & Authorization

- **JWT Tokens**: Secure API authentication
- **Role-Based Access**: Student, Teacher, Admin roles
- **Google OAuth**: Social login integration
- **OTP Verification**: Email-based verification

### Security Middleware

```typescript
// config/middlewares.ts
export default [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        directives: {
          'connect-src': ["'self'", 'https:'],
          'img-src': ["'self'", 'data:', 'blob:', 'https:'],
          'frame-src': ["'self'", 'https://accounts.google.com'],
        },
      },
      cors: {
        origin: ['http://localhost:3000', 'https://ongbadayhoa.com'],
        credentials: true,
      },
    },
  },
];
```

## 📊 Monitoring & Performance

### PM2 Configuration

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'ong-ba-day-hoa-backend',
    script: 'npm run start',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 1337
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

### Performance Optimizations

- **Database Indexing**: Optimized queries
- **Connection Pooling**: Efficient database connections
- **Caching**: API response caching
- **Rate Limiting**: Prevent abuse
- **File Compression**: Gzip compression

## 🔄 Deployment

### Production Checklist

- [ ] Environment variables configured
- [ ] Database connection tested
- [ ] SSL certificates installed
- [ ] Domain configured
- [ ] CORS origins updated
- [ ] File storage configured
- [ ] Email service connected
- [ ] Payment gateway configured

### Deployment Options

```bash
# Using PM2
pm2 start ecosystem.config.js

# Using Docker
docker build -t ongbadayhoa-backend .
docker run -p 1337:1337 ongbadayhoa-backend

# Using cloud platforms (Vercel, Heroku, DigitalOcean)
# Follow their respective deployment guides
```

## 📚 API Documentation

### Auto-generated Documentation

Strapi tự động tạo API documentation tại:
- **Development**: `http://localhost:1337/documentation`
- **Production**: `https://your-domain.com/documentation`

### Custom API Documentation

```typescript
// Example API usage
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:1337/api',
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});

// Get courses
const courses = await api.get('/courses?populate=*');

// Create order
const order = await api.post('/orders', {
  data: {
    course: courseId,
    payment_status: 'pending',
    total_amount: 500000
  }
});
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -m 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Create Pull Request

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: https://docs.strapi.io
- **Community**: https://forum.strapi.io

## 📝 License

This project is licensed under the MIT License.