// Script để kiểm tra và tạo grades nếu cần thiết
require('dotenv').config();

const mysql = require('mysql2/promise');

async function checkAndCreateGrades() {
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || 'localhost',
    user: process.env.DATABASE_USERNAME || 'root',
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME || 'ongbadayhoa',
    port: process.env.DATABASE_PORT || 3306,
  });

  try {
    console.log('🔍 Checking grades table...');
    
    // Kiểm tra grades hiện có
    const [grades] = await connection.execute('SELECT * FROM grades');
    console.log('📊 Current grades:', grades);

    // Kiểm tra courses và gradeId
    const [courses] = await connection.execute('SELECT id, title, gradeId FROM courses');
    console.log('📚 Current courses:', courses);

    // Nếu chưa có grades, tạo mới
    if (grades.length === 0) {
      console.log('🛠️ Creating default grades...');
      
      const gradesToCreate = [
        { title: 'Lớp 10' },
        { title: 'Lớp 11' },
        { title: 'Lớp 12' }
      ];

      for (const grade of gradesToCreate) {
        await connection.execute(
          'INSERT INTO grades (title, created_at, updated_at) VALUES (?, NOW(), NOW())',
          [grade.title]
        );
        console.log(`✅ Created grade: ${grade.title}`);
      }

      // Lấy grades vừa tạo
      const [newGrades] = await connection.execute('SELECT * FROM grades ORDER BY id');
      console.log('🆕 New grades:', newGrades);

      // Cập nhật courses để link với grades
      for (const course of courses) {
        if (course.gradeId) {
          // Map gradeId (10, 11, 12) với grade entities
          const gradeIndex = course.gradeId - 10; // 10->0, 11->1, 12->2
          if (gradeIndex >= 0 && gradeIndex < newGrades.length) {
            const gradeEntityId = newGrades[gradeIndex].id;
            await connection.execute(
              'UPDATE courses SET grade_id = ? WHERE id = ?',
              [gradeEntityId, course.id]
            );
            console.log(`🔗 Linked course "${course.title}" with grade entity ${gradeEntityId}`);
          }
        }
      }
    } else {
      console.log('✅ Grades already exist');
      
      // Kiểm tra xem courses đã có grade relations chưa
      const [coursesWithGrades] = await connection.execute(`
        SELECT c.id, c.title, c.gradeId, c.grade_id, g.title as grade_title 
        FROM courses c 
        LEFT JOIN grades g ON c.grade_id = g.id
      `);
      
      console.log('🔗 Courses with grade relations:', coursesWithGrades);
      
      // Nếu có courses chưa có grade_id, cập nhật
      for (const course of coursesWithGrades) {
        if (course.gradeId && !course.grade_id) {
          const gradeIndex = course.gradeId - 10;
          if (gradeIndex >= 0 && gradeIndex < grades.length) {
            const gradeEntityId = grades[gradeIndex].id;
            await connection.execute(
              'UPDATE courses SET grade_id = ? WHERE id = ?',
              [gradeEntityId, course.id]
            );
            console.log(`🔗 Fixed course "${course.title}" grade relation`);
          }
        }
      }
    }

    console.log('✅ Grade setup completed!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await connection.end();
  }
}

checkAndCreateGrades();


