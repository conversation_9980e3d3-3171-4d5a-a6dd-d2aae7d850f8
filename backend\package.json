{"name": "ong-ba-day-hoa-be", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "deploy": "strapi deploy", "develop": "strapi develop", "seed:example": "node ./scripts/seed.js", "start": "strapi start", "strapi": "strapi"}, "dependencies": {"@aws-sdk/client-s3": "^3.523.0", "@aws-sdk/s3-request-presigner": "^3.523.0", "@strapi/plugin-cloud": "^5.21.0", "@strapi/plugin-users-permissions": "^5.21.0", "@strapi/provider-email-nodemailer": "^5.21.0", "@strapi/strapi": "^5.21.0", "axios": "^1.8.1", "bcrypt": "^5.1.1", "better-sqlite3": "11.3.0", "caniuse-lite": "^1.0.30001731", "crypto": "^1.0.1", "form-data": "^4.0.2", "fs-extra": "^10.0.0", "knex": "^2.5.1", "mime-types": "^2.1.27", "mysql2": "^3.13.0", "openai": "^4.26.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-plugin-sso": "^1.0.2", "strapi-provider-email-brevo": "^1.0.4", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20.17.19", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "overrides": {"esbuild": "^0.25.8", "koa": "^2.16.2", "tmp": "^0.2.4"}, "strapi": {"uuid": "451afe27-43c9-480b-8894-b21e0951e447"}}