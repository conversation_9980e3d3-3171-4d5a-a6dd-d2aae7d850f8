/**
 * A set of functions called "actions" for `auth`
 */

import bcrypt from 'bcrypt';

export default {
  async login(ctx) {
    const { provider, email, password, provider_id, googleToken } = ctx.request.body;
    if (!email || !provider) {
      return ctx.badRequest('Email and provider are required');
    }

    let user = await strapi.query('plugin::users-permissions.user').findOne({ where: { email } });

    // Handle local login
    if (provider === "local") {
      if (!password) {
        return ctx.badRequest('Password is required for local login');
      }

      if (!user) {
        return ctx.badRequest('Invalid credentials');
      }

      const validPassword = await strapi.plugins['users-permissions'].services.user.validatePassword(password, user.password);
      if (!validPassword) {
        return ctx.badRequest('Invalid credentials');
      }
    }
    // Handle Google login
    else if (provider === "google") {
      if (!provider_id) {
        return ctx.badRequest('Google ID is required');
      }

      if (!user) {
        // Find the authenticated role
        const authenticatedRole = await strapi.query('plugin::users-permissions.role').findOne({
          where: { type: 'authenticated' }
        });
        
        if (!authenticatedRole) {
          return ctx.badRequest('Authenticated role not found');
        }

        user = await strapi.plugins["users-permissions"].services.user.add({
          email,
          username: email,
          provider: "external",
          confirmed: true,
          role: authenticatedRole.id,
          googleToken,
          googleId: provider_id
        });
      }
    }

    // Check and create auth provider
    let authProvider = await strapi.query('api::user-auth-provider.user-auth-provider').findOne({
      where: { 
        user: user.id,
        provider
      }
    });

    if (!authProvider) {
      authProvider = await strapi.query('api::user-auth-provider.user-auth-provider').create({
        data: {
          user: user.id,
          provider,
          provider_id: provider_id || email,
          publishedAt: new Date()
        }
      });
    }

    const token = strapi.plugins["users-permissions"].services.jwt.issue({
      id: user.id,
    });

    const sanitizedUser = await strapi.plugins["users-permissions"].services.user.fetch(user.id, {
      populate: ['role', 'providers', 'image'] // Add image to populate
    });

    return ctx.send({
      jwt: token,
      user: sanitizedUser,
      authprovider: authProvider
    });
  },

  async signup(ctx) {
    try {
      const {
        email,
        password,
        fullname,
        phone,
        gender,
        date,
        address,
        whereYouKnowWebsite,
      } = ctx.request.body;

      // Validate required fields
      if (!email || !password ) {
        return ctx.badRequest('Email, password are required');
      }

      // Check if user already exists
      const existingUser = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email }
      });

      if (existingUser) {
        return ctx.badRequest('Email already exists');
      }

      // Create new user
      const user = await strapi.plugins["users-permissions"].services.user.add({
        email,
        username: email,
        password,
        provider: 'local',
        confirmed: true,
        role: 1,
        fullname,
        phone,
        gender,
        date,
        address,
        whereYouKnowWebsite
      });

      // Create auth provider entry with 'local' provider
      const authProvider = await strapi.query('api::user-auth-provider.user-auth-provider').create({
        data: {
          user: user.id,
          provider: 'local',
          provider_id: email,
          publishedAt: new Date()
        }
      });

      const token = strapi.plugins["users-permissions"].services.jwt.issue({
        id: user.id,
      });

      // Get sanitized user data with populated relations
      const sanitizedUser = await strapi.plugins["users-permissions"].services.user.fetch(user.id);
      
      // Get auth provider with relations
      const populatedAuthProvider = await strapi.query('api::user-auth-provider.user-auth-provider').findOne({
        where: { id: authProvider.id },
        populate: ['user']
      });

      return ctx.send({
        jwt: token,
        user: sanitizedUser,
        authprovider: populatedAuthProvider
      });
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  },
  async updateProfile(ctx) {
    try {
      const { id } = ctx.params;
      const { fullname, date, gender, phone, address, whereYouKnowWebsite } = ctx.request.body;

      const updatedUser = await strapi.query('plugin::users-permissions.user').update({
        where: { id },
        data: {
          fullname,
          date,
          gender,
          phone,
          address,
          whereYouKnowWebsite,
        },
      });

      // Use fetch instead of sanitizeUser to get complete user data
      const sanitizedUser = await strapi.plugins['users-permissions'].services.user.fetch(updatedUser.id, {
        populate: ['role', 'providers', 'image'] // Include necessary relations
      });

      return ctx.send({
        user: sanitizedUser
      });
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  },
  async findByEmail(ctx) {
    try {
      const { email } = ctx.query;

      if (!email) {
        return ctx.badRequest('Email is required');
      }

      const user = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email },
        select: ['id', 'email'] // Only return necessary fields for security
      });

      if (!user) {
        return ctx.notFound('User not found');
      }

      return ctx.send({
        user
      });
      
    } catch (error) {
      return ctx.badRequest(error.message);
    }
  },
  async updatePassword(ctx) {
    const { email, password, passwordConfirmation } = ctx.request.body;

    // Kiểm tra xem tất cả các trường có được cung cấp không
    if (!email || !password || !passwordConfirmation) {
        return ctx.badRequest('Tất cả các trường là bắt buộc');
    }

    // Kiểm tra xem mật khẩu và xác nhận mật khẩu có khớp không
    if (password !== passwordConfirmation) {
        return ctx.badRequest('Mật khẩu và xác nhận mật khẩu không khớp');
    }

    // Cập nhật mật khẩu
    const user = await strapi.query('plugin::users-permissions.user').findOne({ where: { email } });

    if (!user) {
        return ctx.notFound('Người dùng không tồn tại');
    }

    // Mã hóa mật khẩu mới
    const hashedPassword = await bcrypt.hash(password, 10);

    // Cập nhật mật khẩu trong cơ sở dữ liệu
    await strapi.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: { password: hashedPassword },
    });

    return ctx.send({ message: 'Mật khẩu đã được cập nhật thành công' });
  }
};
