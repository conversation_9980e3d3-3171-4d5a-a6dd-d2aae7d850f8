export default {
  routes: [
    {
      method: "POST",
      path: "/auth/login",
      handler: "api::auth.auth.login",
      config: {
        policies: [],
        auth: false,
      },
    },
    {
      method: "POST",
      path: "/auth/signup",
      handler: "api::auth.auth.signup",
      config: {
        policies: [],
        auth: false,
      },
    },
    {
      method: "PUT",
      path: "/auth/profile/:id",
      handler: "api::auth.auth.updateProfile",
      config: {
        policies: [],
        auth: false,
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/auth/find-by-email",
      handler: "api::auth.auth.findByEmail",
      config: {
        policies: [],
        auth: false,
      },
    },
    {
      method: "PUT",
      path: "/auth/update-password",
      handler: "api::auth.auth.updatePassword",
      config: {
        policies: [],
        auth: false,
      },
    }
  ]
}
