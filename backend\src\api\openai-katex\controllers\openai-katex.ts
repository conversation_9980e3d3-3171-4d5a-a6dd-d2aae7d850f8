/**
 * openai-katex controller
 */

export default {
  /**
   * Convert text to KaTeX format using OpenAI
   */
  async convertToKaTeX(ctx) {
    try {
      const { text, type = 'chemistry' } = ctx.request.body;
      
      if (!text) {
        return ctx.badRequest('Text is required');
      }

      const openaiService = strapi.service('api::openai-katex.openai-katex');
      const convertedText = await openaiService.convertTextToKaTeX(text, type);

      if (!convertedText.success) {
        return ctx.internalServerError('Failed to convert text to KaTeX');
      }

      return {
        success: true,
        original: text,
        converted: convertedText.data,
        type
      };

    } catch (error) {
      console.error('Error converting to KaTeX:', error);
      return ctx.internalServerError('Conversion failed');
    }
  },

  /**
   * Batch convert multiple texts
   */
  async batchConvertToKaTeX(ctx) {
    try {
      const { texts, type = 'chemistry' } = ctx.request.body;
      
      if (!texts || !Array.isArray(texts)) {
        return ctx.badRequest('Texts array is required');
      }

      const results = [];
      const openaiService = strapi.service('api::openai-katex.openai-katex');
      
      for (const text of texts) {
        if (text && text.trim()) {
          const convertedText = await openaiService.convertTextToKaTeX(text, type);
          results.push({
            original: text,
            converted: convertedText.success ? convertedText.data : text,
            success: convertedText.success
          });
        } else {
          results.push({
            original: text,
            converted: text,
            success: true
          });
        }
      }

      return {
        success: true,
        results,
        type
      };

    } catch (error) {
      console.error('Error batch converting to KaTeX:', error);
      return ctx.internalServerError('Batch conversion failed');
    }
  },

  /**
   * Test OpenAI connection
   */
  async testConnection(ctx) {
    try {
      const openaiService = strapi.service('api::openai-katex.openai-katex');
      const testResult = await openaiService.testOpenAIConnection();

      return {
        success: testResult.success,
        message: testResult.message
      };

    } catch (error) {
      console.error('Error testing OpenAI connection:', error);
      return ctx.internalServerError('Connection test failed');
    }
  }
};
