/**
 * openai-katex router
 */

export default {
  routes: [
    {
      method: 'POST',
      path: '/openai-katex/convert',
      handler: 'api::openai-katex.openai-katex.convertToKaTeX',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'POST',
      path: '/openai-katex/batch-convert',
      handler: 'api::openai-katex.openai-katex.batchConvertToKaTeX',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'GET',
      path: '/openai-katex/test',
      handler: 'api::openai-katex.openai-katex.testConnection',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
  ],
};
