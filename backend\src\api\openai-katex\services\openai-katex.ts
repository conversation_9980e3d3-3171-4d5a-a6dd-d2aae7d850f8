/**
 * openai-katex service
 */

const OpenAI = require('openai');

export default {
  /**
   * Khởi tạo OpenAI client
   */
  getOpenAIClient() {
    return new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  },

  /**
   * Convert text to KaTeX format
   */
  async convertTextToKaTeX(text, type = 'chemistry') {
    try {
      if (!text || text.trim() === '') {
        return {
          success: true,
          data: text
        };
      }

      const client = this.getOpenAIClient();
      const systemPrompt = this.getSystemPrompt(type);
      
      const primaryModel = process.env.OPENAI_PRIMARY_MODEL || 'gpt-4o-mini-2024-07-18';
      const fallbackModel = process.env.OPENAI_FALLBACK_MODEL || 'gpt-5-mini-2025-08-07';
      
      let response;
      try {
        // Try primary model first
        response = await client.chat.completions.create({
          model: primaryModel,
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: `<PERSON><PERSON><PERSON><PERSON> đổi văn bản sau sang định dạng KaTeX:\n\n${text}`
            }
          ],
          temperature: 0.1,
          max_completion_tokens: 2000,
        });
      } catch (primaryError) {
        console.warn(`Primary model ${primaryModel} failed, trying fallback model ${fallbackModel}:`, primaryError.message);
        
        // Fallback to secondary model
        response = await client.chat.completions.create({
          model: fallbackModel,
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: `Chuyển đổi văn bản sau sang định dạng KaTeX:\n\n${text}`
            }
          ],
          temperature: 0.1,
          max_completion_tokens: 2000,
        });
      }

      const convertedText = response.choices[0]?.message?.content?.trim();

      if (!convertedText) {
        return {
          success: false,
          error: 'No response from OpenAI'
        };
      }

      return {
        success: true,
        data: convertedText
      };

    } catch (error) {
      console.error('OpenAI conversion error:', error);
      return {
        success: false,
        error: error.message,
        data: text // Fallback to original text
      };
    }
  },

  /**
   * Batch convert multiple texts
   */
  async batchConvertToKaTeX(texts, type = 'chemistry') {
    try {
      if (!texts || !Array.isArray(texts)) {
        return {
          success: false,
          error: 'Texts array is required'
        };
      }

      const results = [];
      const openaiService = strapi.service('api::openai-katex.openai-katex');
      
      for (const text of texts) {
        if (text && text.trim()) {
          const convertedText = await openaiService.convertTextToKaTeX(text, type);
          results.push({
            original: text,
            converted: convertedText.success ? convertedText.data : text,
            success: convertedText.success
          });
        } else {
          results.push({
            original: text,
            converted: text,
            success: true
          });
        }
      }

      return {
        success: true,
        results,
        type
      };

    } catch (error) {
      console.error('Error batch converting to KaTeX:', error);
      return {
        success: false,
        error: 'Batch conversion failed'
      };
    }
  },

  /**
   * Lấy system prompt theo loại nội dung
   */
  getSystemPrompt(type) {
    const basePrompt = `Bạn là AI chuyên gia chuyển đổi văn bản khoa học (Toán, Hóa) sang định dạng KaTeX.

**NHIỆM VỤ:** Rà soát văn bản, chỉ chuyển đổi các công thức, phương trình, và ký hiệu khoa học sang KaTeX. Giữ nguyên 100% văn bản thường. Chỉ trả về kết quả, không giải thích.

### QUY TẮC BẮT BUỘC

**1. Hóa học:**
*   **Công thức & Ion:** Luôn dùng \\ce{}.
    *   Ví dụ: H2SO4 → $\\ce{H2SO4}$, SO42- → $\\ce{SO4^{2-}}$.
*   **Phương trình phản ứng:** Dùng \\ce{} để tự xử lý mũi tên.
    *   Ví dụ: C + H2O <=> CO + H2 → $$\\ce{C(r) + H2O(g) <=> CO(g) + H2(g)}$$.
*   **Trạng thái chất:** (s), (l), (g), (aq) được giữ nguyên trong \\ce{}.
*   **Điều kiện phản ứng:** Ghi trên mũi tên. Ví dụ: --(t°, p)--> → \\ce{->[t^o, p]}.

**2. Toán học & Tính toán:**
*   **Phép nhân:** Ký tự * hoặc x phải chuyển thành \\times.
    *   Ví dụ: 0,15*6,022*10^23 → $0,15 \\times 6,022 \\times 10^{23}$.
*   **Phép chia & Phân số:**
    *   **Phân số đơn giản:** a/b → $\\frac{a}{b}$
    *   **Phân số phức tạp:** (a+b)/(c+d) → $\\frac{a+b}{c+d}$
    *   **Phân số nhiều tầng:** (a/b)/(c/d) → $\\frac{\\frac{a}{b}}{\\frac{c}{d}}$ hoặc $\\frac{a/b}{c/d}$
    *   **Phân số với số mũ:** a^2/b^3 → $\\frac{a^2}{b^3}$
    *   **Phân số với căn:** √a/b → $\\frac{\\sqrt{a}}{b}$
    *   **Phép chia inline đơn giản:** 10÷5 hoặc 10/5 (không có dấu ngoặc) → $10 \\div 5$
*   **Số mũ & Phần trăm:**
    *   Ví dụ: 10^-31 → $10^{-31}$, 100% → $100\\%$.
*   **Dấu phẩy thập phân:** Giữ nguyên dấu phẩy , của tiếng Việt.
    *   Ví dụ: 9,109 → $9,109$.
*   **Bất đẳng thức:** ≤ → $\\le$, ≥ → $\\ge$, ≠ → $\\ne$.
*   **Hệ phương trình:** Sử dụng \\begin{cases}...\\end{cases}.

**3. Định dạng:**
*   Dùng $...$ cho công thức inline (nằm trong câu).
*   Dùng $$...$$ cho phương trình, biểu thức quan trọng hoặc đứng riêng một dòng.

**4. Xử lý câu hỏi và đáp án:**
*   **Câu hỏi trắc nghiệm:** Giữ nguyên cấu trúc A), B), C), D).
*   **Câu đáp án:** Chuyển đổi công thức trong đáp án.
*   **Câu giải thích:** Chuyển đổi công thức trong phần giải thích.

**5. Đơn vị đo lường:**
*   Giữ nguyên đơn vị: M, mol/L, g/mol, kJ/mol.
*   Chuyển đổi ký hiệu: 10^-6 → $10^{-6}$.

**6. Ký hiệu khoa học:**
*   Hằng số: 6,022*10^23 → $6,022 \\times 10^{23}$.
*   Số pi: π → $\\pi$.
*   Số e: e → $e$ (khi là hằng số toán học).`;

    // Thêm prompt cho câu hỏi và đáp án
    const questionAnswerPrompt = `${basePrompt}

**VÍ DỤ XỬ LÝ CÂU HỎI VÀ ĐÁP ÁN:**

*   **Input:** "Câu 1: Tính nồng độ mol của dung dịch HCl 0,1M. A) 0,1 mol/L B) 0,01 mol/L C) 1 mol/L D) 10 mol/L"
*   **Output:** "Câu $1$: Tính nồng độ mol của dung dịch $\\ce{HCl}$ $0,1$M. A) $0,1$ mol/L B) $0,01$ mol/L C) $1$ mol/L D) $10$ mol/L"

*   **Input:** "Đáp án: B. Giải thích: Nồng độ mol = số mol/ thể tích = 0,1 mol/1L = 0,1 mol/L"
*   **Output:** "Đáp án: B. Giải thích: Nồng độ mol = số mol/ thể tích = $0,1$ mol/$1$L = $0,1$ mol/L"

*   **Input:** "Câu 2: Giải phương trình x^2 - 5x + 6 = 0. A) x = 2, x = 3 B) x = -2, x = -3 C) x = 1, x = 6 D) x = 0, x = 5"
*   **Output:** "Câu $2$: Giải phương trình $x^2 - 5x + 6 = 0$. A) $x = 2$, $x = 3$ B) $x = -2$, $x = -3$ C) $x = 1$, $x = 6$ D) $x = 0$, $x = 5$"

*   **Input:** "Đáp án: A. Giải thích: x^2 - 5x + 6 = (x-2)(x-3) = 0 nên x = 2 hoặc x = 3"
*   **Output:** "Đáp án: A. Giải thích: $x^2 - 5x + 6 = (x-2)(x-3) = 0$ nên $x = 2$ hoặc $x = 3$"`;

    const specificPrompts = {
      chemistry: `${basePrompt}

**VÍ DỤ ÁP DỤNG CHO HÓA HỌC:**

*   **Input:** "Tính nồng độ H+ từ pH=2. Ta có [H+] = 10^-2 M."
*   **Output:** "Tính nồng độ $\\ce{H+}$ từ $pH=2$. Ta có $[\\ce{H+}] = 10^{-2} M$."

*   **Input:** "Phản ứng: CaCO3 + 2HCl → CaCl2 + H2O + CO2"
*   **Output:** "Phản ứng: $$\\ce{CaCO3 + 2HCl -> CaCl2 + H2O + CO2}$$"

*   **Input:** "Ion SO4^2- có điện tích -2"
*   **Output:** "Ion $\\ce{SO4^{2-}}$ có điện tích $-2$"

*   **Input:** "Phản ứng thuận nghịch: N2(g) + 3H2(g) ⇌ 2NH3(g) ở 400°C, 200 atm"
*   **Output:** "Phản ứng thuận nghịch: $$\\ce{N2(g) + 3H2(g) <=> 2NH3(g)}$$ ở $400°C$, $200$ atm"

*   **Input:** "Tính khối lượng mol của H2SO4. M(H2SO4) = 2×1 + 32 + 4×16 = 98 g/mol"
*   **Output:** "Tính khối lượng mol của $\\ce{H2SO4}$. $M(\\ce{H2SO4}) = 2 \\times 1 + 32 + 4 \\times 16 = 98$ g/mol"

*   **Input:** "Câu hỏi: Dung dịch HCl 0,1M có pH bằng bao nhiêu? A) 1 B) 2 C) 3 D) 4"
*   **Output:** "Câu hỏi: Dung dịch $\\ce{HCl}$ $0,1$M có pH bằng bao nhiêu? A) $1$ B) $2$ C) $3$ D) $4$"`,

      math: `${basePrompt}

**VÍ DỤ ÁP DỤNG CHO TOÁN HỌC:**

*   **Input:** "Hiệu suất H = (11,7/21,081)*100% = 55,5%"
*   **Output:** "Hiệu suất $H = \\frac{11,7}{21,081} \\times 100\\% = 55,5\\%$"

*   **Input:** "Tính đạo hàm của y = x^2 + 2x + 1"
*   **Output:** "Tính đạo hàm của $y = x^2 + 2x + 1$"

*   **Input:** "Khối lượng = 6,022*10^23 * 1,67*10^-27"
*   **Output:** "Khối lượng = $6,022 \\times 10^{23} \\times 1,67 \\times 10^{-27}$"

**VÍ DỤ VỀ PHÂN SỐ:**

*   **Input:** "Tính 3/4 + 1/2 = 5/4"
*   **Output:** "Tính $\\frac{3}{4} + \\frac{1}{2} = \\frac{5}{4}$"

*   **Input:** "Giải phương trình (x+1)/(x-2) = 3/5"
*   **Output:** "Giải phương trình $\\frac{x+1}{x-2} = \\frac{3}{5}$"

*   **Input:** "Tỉ số a^2/b^3 = 4/9"
*   **Output:** "Tỉ số $\\frac{a^2}{b^3} = \\frac{4}{9}$"

*   **Input:** "Căn √2/3 ≈ 0,471"
*   **Output:** "Căn $\\frac{\\sqrt{2}}{3} ≈ 0,471$"

*   **Input:** "Phép chia đơn giản: 15÷3 = 5"
*   **Output:** "Phép chia đơn giản: $15 \\div 3 = 5$"

**VÍ DỤ VỀ PHƯƠNG TRÌNH VÀ BẤT ĐẲNG THỨC:**

*   **Input:** "Giải hệ phương trình: x + y = 5 và 2x - y = 1"
*   **Output:** "Giải hệ phương trình: $$\\begin{cases} x + y = 5 \\\\ 2x - y = 1 \\end{cases}$$"

*   **Input:** "Tìm x sao cho x^2 - 4x + 3 ≥ 0"
*   **Output:** "Tìm $x$ sao cho $x^2 - 4x + 3 \\ge 0$"

*   **Input:** "Tính tích phân ∫(x^2 + 2x)dx từ 0 đến 1"
*   **Output:** "Tính tích phân $$\\int_{0}^{1} (x^2 + 2x)dx$$"

*   **Input:** "Câu hỏi: Phương trình x^2 + bx + c = 0 có nghiệm kép khi nào? A) b^2 = 4c B) b^2 > 4c C) b^2 < 4c D) b^2 ≠ 4c"
*   **Output:** "Câu hỏi: Phương trình $x^2 + bx + c = 0$ có nghiệm kép khi nào? A) $b^2 = 4c$ B) $b^2 > 4c$ C) $b^2 < 4c$ D) $b^2 \\ne 4c$"

*   **Input:** "Tính giới hạn lim(x→0) (sin x)/x = 1"
*   **Output:** "Tính giới hạn $$\\lim_{x \\to 0} \\frac{\\sin x}{x} = 1$$"`,

      question: questionAnswerPrompt
    };

    return specificPrompts[type] || specificPrompts.chemistry;
  },

  /**
   * Test OpenAI connection
   */
  async testOpenAIConnection() {
    try {
      if (!process.env.OPENAI_API_KEY) {
        return {
          success: false,
          message: 'OpenAI API key not configured'
        };
      }

      const client = this.getOpenAIClient();
      const primaryModel = process.env.OPENAI_PRIMARY_MODEL || 'gpt-4o-mini-2024-07-18';
      const fallbackModel = process.env.OPENAI_FALLBACK_MODEL || 'gpt-5-mini-2025-08-07';
      
      let response;
      let usedModel = primaryModel;
      
      try {
        // Try primary model first
        response = await client.chat.completions.create({
          model: primaryModel,
          messages: [
            {
              role: 'user',
              content: 'Hello, can you respond with "OpenAI connection successful"?'
            }
          ],
          max_tokens: 50,
        });
      } catch (primaryError) {
        console.warn(`Primary model ${primaryModel} failed, trying fallback model ${fallbackModel}:`, primaryError.message);
        usedModel = fallbackModel;
        
        // Fallback to secondary model
        response = await client.chat.completions.create({
          model: fallbackModel,
          messages: [
            {
              role: 'user',
              content: 'Hello, can you respond with "OpenAI connection successful"?'
            }
          ],
          max_tokens: 50,
        });
      }

      if (response.choices[0]?.message?.content) {
        return {
          success: true,
          message: `OpenAI connection successful using model: ${usedModel}`
        };
      } else {
        return {
          success: false,
          message: 'OpenAI responded but with empty content'
        };
      }

    } catch (error) {
      console.error('OpenAI test error:', error);
      return {
        success: false,
        message: `OpenAI connection failed: ${error.message}`
      };
    }
  },

  /**
   * Validate environment variables
   */
  validateOpenAIConfig() {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('Missing OPENAI_API_KEY environment variable');
    }
    
    // Log current model configuration
    const primaryModel = process.env.OPENAI_PRIMARY_MODEL || 'gpt-4o-mini-2024-07-18';
    const fallbackModel = process.env.OPENAI_FALLBACK_MODEL || 'gpt-5-mini-2025-08-07';
    
    console.log(`OpenAI Configuration:`);
    console.log(`- Primary Model: ${primaryModel}`);
    console.log(`- Fallback Model: ${fallbackModel}`);
    
    return true;
  }
};
