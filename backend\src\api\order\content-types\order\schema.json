{"kind": "collectionType", "collectionName": "orders", "info": {"singularName": "order", "pluralName": "orders", "displayName": "Order", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"order_id": {"type": "string", "unique": true}, "order_date": {"type": "datetime"}, "total_amount": {"type": "decimal"}, "payos_order_code": {"type": "string", "unique": true}, "payment_status": {"type": "enumeration", "default": "pending", "enum": ["pending", "completed", "failed", "cancelled"]}, "payment_method": {"type": "string"}, "course": {"type": "relation", "relation": "manyToOne", "target": "api::course.course", "inversedBy": "orders"}, "users_permissions_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "orders"}, "delivery_address": {"type": "text"}, "orderCode": {"type": "string"}, "activation_codes": {"type": "relation", "relation": "manyToMany", "target": "api::activation-code.activation-code", "inversedBy": "orders"}, "course_tier": {"type": "relation", "relation": "manyToOne", "target": "api::course-tier.course-tier", "inversedBy": "orders"}, "email_sent": {"type": "boolean", "default": false}, "discount_amount": {"type": "decimal", "default": 0}, "voucher_code": {"type": "string"}, "admin_notified": {"type": "enumeration", "enum": ["notified", "resolved"]}}}