/**
 * streak-teacher controller
 */

export default {
  /**
   * <PERSON><PERSON><PERSON> lịch câu hỏi theo grade
   */
  async getCalendar(ctx) {
    try {
      const { grade } = ctx.params;
      
      if (!grade) {
        return ctx.badRequest('Grade is required');
      }

      // Lấy tất cả streak_questions theo course/grade
      const courses = await strapi.entityService.findMany('api::course.course', {
        filters: { gradeId: grade }
      });

      if (!courses || courses.length === 0) {
        return ctx.notFound('Course not found for this grade');
      }

      const courseId = courses[0].id;

      // Lấy danh sách ngày đã có câu hỏi
      const streakQuestions = await strapi.entityService.findMany('api::streak-question.streak-question', {
        filters: { 
          course: {
            id: courseId
          }
        },
        populate: ['course']
      });

      // Format dữ liệu trả về
      const calendar = streakQuestions.map(sq => ({
        date: sq.value,
        hasQuestions: true,
        courseId: courseId,
        streakQuestionId: sq.id
      }));

      return { calendar, grade, courseId };
    } catch (error) {
      console.error('Error getting calendar:', error);
      return ctx.internalServerError('Internal server error');
    }
  },

  /**
   * Lấy câu hỏi theo ngày và grade
   */
  async getQuestionsByDate(ctx) {
    try {
      const { date, grade } = ctx.params;
      
      if (!date || !grade) {
        return ctx.badRequest('Date and grade are required');
      }

      // Lấy course theo grade
      const courses = await strapi.entityService.findMany('api::course.course', {
        filters: { gradeId: grade }
      });

      if (!courses || courses.length === 0) {
        return ctx.notFound('Course not found for this grade');
      }

      const courseId = courses[0].id;

      // Tìm streak_question theo ngày và course
      const streakQuestions = await strapi.entityService.findMany('api::streak-question.streak-question', {
        filters: { 
          value: date,
          course: {
            id: courseId
          }
        },
        populate: ['course']
      });

      if (!streakQuestions || streakQuestions.length === 0) {
        return { questions: [], date, grade, exists: false };
      }

      const streakQuestionId = streakQuestions[0].id;

      // Lấy câu hỏi liên quan
      const questions = await strapi.entityService.findMany('api::question.question', {
        filters: { 
          streak_question: {
            id: streakQuestionId
          }
        },
        populate: ['exercise_type', 'grade', 'chapter', 'streak_question']
      });

      return { 
        questions, 
        date, 
        grade, 
        exists: true,
        streakQuestionId 
      };
    } catch (error) {
      console.error('Error getting questions by date:', error);
      return ctx.internalServerError('Internal server error');
    }
  },

  /**
   * Tạo hoặc cập nhật bộ câu hỏi
   */
  async createOrUpdateQuestions(ctx) {
    try {
      const { date, grade, questions } = ctx.request.body;
      
      // Validation
      if (!date || !grade || !questions || questions.length !== 3) {
        return ctx.badRequest('Date, grade and exactly 3 questions are required');
      }

      // Kiểm tra ngày không được nhỏ hơn ngày hiện tại (theo múi giờ Việt Nam)
      const currentDate = new Date();
      // Lấy ngày hiện tại theo UTC+7
      const vietnamNow = new Date(currentDate.toLocaleString("en-US", {timeZone: "Asia/Ho_Chi_Minh"}));
      const questionDate = new Date(date);
      const currentDateMidnight = new Date(vietnamNow.setHours(0, 0, 0, 0));
      if (questionDate < currentDateMidnight) {
        return ctx.badRequest('Cannot create questions for past dates');
      }

      // Lấy course theo grade
      const courses = await strapi.entityService.findMany('api::course.course', {
        filters: { gradeId: grade }
      });

      if (!courses || courses.length === 0) {
        return ctx.notFound('Course not found for this grade');
      }

      const courseId = courses[0].id;
      
      // Lấy grade entity dựa trên grade number
      const gradeTitle = `Lớp ${grade}`;
      const gradeEntities = await strapi.entityService.findMany('api::grade.grade', {
        filters: { title: gradeTitle }
      });
      
      const gradeEntity = gradeEntities?.[0] || null;

      // Tìm hoặc tạo streak_question
      let streakQuestionArray = await strapi.entityService.findMany('api::streak-question.streak-question', {
        filters: { 
          value: date,
          course: {
            id: courseId
          }
        }
      });

      let streakQuestion;
      if (!streakQuestionArray || streakQuestionArray.length === 0) {
        // Tạo mới streak_question
        streakQuestion = await strapi.entityService.create('api::streak-question.streak-question', {
          data: {
            value: date,
            course: courseId,
            description: `Câu hỏi streak cho ngày ${date}`
          }
        });
      } else {
        streakQuestion = streakQuestionArray[0];
      }

      const streakQuestionId = streakQuestion.id;

      // Lấy exercise_types
      const exerciseTypes = await strapi.entityService.findMany('api::exercise-type.exercise-type', {
        filters: {
          type: {
            $in: ['hieu-biet', 'van_dung']
          }
        }
      });

      const hieuBietType = exerciseTypes.find(et => et.type === 'hieu-biet');
      const vanDungType = exerciseTypes.find(et => et.type === 'van_dung');

      // Xóa các câu hỏi cũ nếu có
      const existingQuestions = await strapi.entityService.findMany('api::question.question', {
        filters: { 
          streak_question: {
            id: streakQuestionId
          }
        }
      });

      for (const eq of existingQuestions) {
        await strapi.entityService.delete('api::question.question', eq.id);
      }

      // Tạo 3 câu hỏi mới
      const createdQuestions = [];
      
      for (let i = 0; i < questions.length; i++) {
        const questionData = questions[i];
        
        // Xác định exercise_type (2 câu đầu: hiểu-biết, câu 3: vận dụng)
        const exerciseType = i < 2 ? hieuBietType : vanDungType;
        
        if (!exerciseType) {
          return ctx.badRequest(`Exercise type not found for question ${i + 1}`);
        }

        // Convert content và explain sang KaTeX using OpenAI service
        const openaiService = strapi.service('api::openai-katex.openai-katex');
        const contentResult = await openaiService.convertTextToKaTeX(questionData.content, 'chemistry');
        const convertedContent = contentResult.success ? contentResult.data : questionData.content;
        
        let convertedExplain = '';
        if (questionData.explain) {
          const explainResult = await openaiService.convertTextToKaTeX(questionData.explain, 'chemistry');
          convertedExplain = explainResult.success ? explainResult.data : questionData.explain;
        }

        // Convert correct_answer sang KaTeX
        const correctAnswerResult = await openaiService.convertTextToKaTeX(questionData.correctAnswer || '', 'chemistry');
        const convertedCorrectAnswer = correctAnswerResult.success ? correctAnswerResult.data : questionData.correctAnswer;

        const questionToCreate: any = {
          content: convertedContent,
          explain: convertedExplain,
          type: questionData.type, // TN_4 hoặc TN_2
          question_status: 'approval' as 'approval',
          correct_answer: convertedCorrectAnswer,
          correct_answer_type: questionData.correctAnswerType,
          exercise_type: exerciseType.id,
          streak_question: streakQuestionId,
          grade: gradeEntity?.id || null,
          image_path: questionData.imagePath || null,
          image_explain_path: questionData.imageExplainPath || null
        };

        // Thêm đáp án cho TN_4 và convert chúng sang KaTeX
        if (questionData.type === 'TN_4') {
          // Convert từng đáp án A, B, C, D qua OpenAI-KaTeX
          const optionA = await openaiService.convertTextToKaTeX(questionData.A || '', 'chemistry');
          const optionB = await openaiService.convertTextToKaTeX(questionData.B || '', 'chemistry');
          const optionC = await openaiService.convertTextToKaTeX(questionData.C || '', 'chemistry');
          const optionD = await openaiService.convertTextToKaTeX(questionData.D || '', 'chemistry');
          
          questionToCreate.A = optionA.success ? optionA.data : (questionData.A || '');
          questionToCreate.B = optionB.success ? optionB.data : (questionData.B || '');
          questionToCreate.C = optionC.success ? optionC.data : (questionData.C || '');
          questionToCreate.D = optionD.success ? optionD.data : (questionData.D || '');
        }

        const createdQuestion = await strapi.entityService.create('api::question.question', {
          data: questionToCreate
        });

        createdQuestions.push(createdQuestion);
      }

      return {
        success: true,
        message: 'Questions created/updated successfully',
        data: {
          streakQuestionId,
          questions: createdQuestions,
          date,
          grade
        }
      };

    } catch (error) {
      console.error('Error creating/updating questions:', error);
      return ctx.internalServerError('Internal server error');
    }
  },

  /**
   * Xóa bộ câu hỏi
   */
  async deleteQuestions(ctx) {
    try {
      const { streakQuestionId } = ctx.params;
      
      if (!streakQuestionId) {
        return ctx.badRequest('Streak question ID is required');
      }

      // Kiểm tra ngày không được quá khứ
      const streakQuestion = await strapi.entityService.findOne('api::streak-question.streak-question', streakQuestionId);
      
      if (!streakQuestion) {
        return ctx.notFound('Streak question not found');
      }

      const questionDate = new Date(streakQuestion.value);
      const currentDate = new Date();
      // Sử dụng múi giờ Việt Nam cho việc so sánh
      const vietnamNow = new Date(currentDate.toLocaleString("en-US", {timeZone: "Asia/Ho_Chi_Minh"}));
      const currentDateMidnight = new Date(vietnamNow.setHours(0, 0, 0, 0));
      
      if (questionDate < currentDateMidnight) {
        return ctx.badRequest('Cannot delete questions for past dates');
      }

      // Xóa tất cả câu hỏi liên quan
      const questions = await strapi.entityService.findMany('api::question.question', {
        filters: { 
          streak_question: {
            id: streakQuestionId
          }
        }
      });

      for (const question of questions) {
        await strapi.entityService.delete('api::question.question', question.id);
      }

      // Xóa streak_question
      await strapi.entityService.delete('api::streak-question.streak-question', streakQuestionId);

      return {
        success: true,
        message: 'Questions deleted successfully'
      };

    } catch (error) {
      console.error('Error deleting questions:', error);
      return ctx.internalServerError('Internal server error');
    }
  },


};
