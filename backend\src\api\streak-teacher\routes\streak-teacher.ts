/**
 * streak-teacher router
 */

export default {
  routes: [
    {
      method: 'GET',
      path: '/streak-teacher/calendar/:grade',
      handler: 'api::streak-teacher.streak-teacher.getCalendar',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'GET', 
      path: '/streak-teacher/questions/:date/:grade',
      handler: 'api::streak-teacher.streak-teacher.getQuestionsByDate',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'POST',
      path: '/streak-teacher/questions',
      handler: 'api::streak-teacher.streak-teacher.createOrUpdateQuestions',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'DELETE',
      path: '/streak-teacher/questions/:streakQuestionId',
      handler: 'api::streak-teacher.streak-teacher.deleteQuestions',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
  ],
};
