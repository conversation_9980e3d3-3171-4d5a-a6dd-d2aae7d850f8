/**
 * streak-teacher service
 */

export default {
  /**
   * <PERSON><PERSON><PERSON> danh sách exercise types để sử dụng
   */
  async getExerciseTypes() {
    return await strapi.entityService.findMany('api::exercise-type.exercise-type', {
      filters: {
        type: {
          $in: ['hieu-biet', 'van_dung']
        }
      }
    });
  },

  /**
   * Kiểm tra và tạo exercise types nếu chưa có
   */
  async ensureExerciseTypesExist() {
    const existingTypes = await this.getExerciseTypes();
    
    const hieuBietExists = existingTypes.some(et => et.type === 'hieu-biet');
    const vanDungExists = existingTypes.some(et => et.type === 'van_dung');

    if (!hieuBietExists) {
      await strapi.entityService.create('api::exercise-type.exercise-type', {
        data: {
          name: 'Hiểu - Biết',
          description: 'Câu hỏi kiểm tra mức độ hiểu biết cơ bản',
          type: 'hieu-biet',
          point: 10
        }
      });
    }

    if (!vanDungExists) {
      await strapi.entityService.create('api::exercise-type.exercise-type', {
        data: {
          name: 'Vận dụng',
          description: 'Câu hỏi kiểm tra khả năng vận dụng kiến thức',
          type: 'van_dung',
          point: 20
        }
      });
    }
  },

  /**
   * Validate dữ liệu câu hỏi
   */
  validateQuestionData(questions) {
    if (!Array.isArray(questions) || questions.length !== 3) {
      throw new Error('Phải có đúng 3 câu hỏi');
    }

    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      
      if (!question.content) {
        throw new Error(`Câu hỏi ${i + 1}: Nội dung câu hỏi không được để trống`);
      }

      if (!question.type || !['TN_4', 'TN_2'].includes(question.type)) {
        throw new Error(`Câu hỏi ${i + 1}: Loại câu hỏi phải là TN_4 hoặc TN_2`);
      }

      if (!question.correctAnswer) {
        throw new Error(`Câu hỏi ${i + 1}: Đáp án đúng không được để trống`);
      }

      if (question.type === 'TN_4') {
        if (!question.A || !question.B || !question.C || !question.D) {
          throw new Error(`Câu hỏi ${i + 1}: Câu hỏi trắc nghiệm 4 đáp án phải có đủ 4 lựa chọn A, B, C, D`);
        }
        if (!['A', 'B', 'C', 'D'].includes(question.correctAnswerType)) {
          throw new Error(`Câu hỏi ${i + 1}: Đáp án đúng phải là A, B, C hoặc D`);
        }
      }

      if (question.type === 'TN_2') {
        if (!['True', 'False'].includes(question.correctAnswerType)) {
          throw new Error(`Câu hỏi ${i + 1}: Đáp án đúng phải là ĐÚNG hoặc SAI`);
        }
      }
    }

    return true;
  },

  /**
   * Lấy thống kê câu hỏi theo grade
   */
  async getQuestionStats(grade) {
    try {
      // Lấy course theo grade
      const courses = await strapi.entityService.findMany('api::course.course', {
        filters: { gradeId: grade }
      });

      if (!courses || courses.length === 0) {
        return { total: 0, thisMonth: 0, today: 0 };
      }

      const courseId = courses[0].id;

      // Đếm tổng số ngày có câu hỏi
      const totalStreakQuestions = await strapi.entityService.findMany('api::streak-question.streak-question', {
        filters: { 
          course: {
            id: courseId
          }
        }
      });

      // Đếm số ngày trong tháng hiện tại (theo múi giờ Việt Nam)
      const currentDate = new Date();
      const vietnamNow = new Date(currentDate.toLocaleString("en-US", {timeZone: "Asia/Ho_Chi_Minh"}));
      const startOfMonth = new Date(vietnamNow.getFullYear(), vietnamNow.getMonth(), 1);
      const endOfMonth = new Date(vietnamNow.getFullYear(), vietnamNow.getMonth() + 1, 0);

      const thisMonthStreakQuestions = await strapi.entityService.findMany('api::streak-question.streak-question', {
        filters: { 
          course: {
            id: courseId
          },
          value: {
            $gte: startOfMonth.toISOString().split('T')[0],
            $lte: endOfMonth.toISOString().split('T')[0]
          }
        }
      });

      // Đếm hôm nay (theo múi giờ Việt Nam)
      const today = vietnamNow.toISOString().split('T')[0];
      const todayStreakQuestions = await strapi.entityService.findMany('api::streak-question.streak-question', {
        filters: { 
          course: {
            id: courseId
          },
          value: today
        }
      });

      return {
        total: totalStreakQuestions.length,
        thisMonth: thisMonthStreakQuestions.length,
        today: todayStreakQuestions.length
      };

    } catch (error) {
      console.error('Error getting question stats:', error);
      return { total: 0, thisMonth: 0, today: 0 };
    }
  }
};
