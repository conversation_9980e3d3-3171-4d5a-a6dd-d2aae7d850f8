/**
 * streak controller
 */

import {factories} from '@strapi/strapi'

// Consistent timezone utilities for backend
const getVietnamDateFromUTC = (utcDate: Date) => {
    return new Date(utcDate.getTime() + (7 * 60 * 60 * 1000));
};

const getVietnamDateString = (utcDate: Date) => {
    const vietnamDate = getVietnamDateFromUTC(utcDate);
    return vietnamDate.toISOString().split('T')[0];
};

const getCurrentVietnamDate = () => {
    return getVietnamDateString(new Date());
};

const getVietnamDateRange = (date: Date) => {
    const vietnamDate = getVietnamDateFromUTC(date);
    const startOfDay = new Date(vietnamDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(vietnamDate);
    endOfDay.setHours(23, 59, 59, 999);

    return {
        start: startOfDay,
        end: endOfDay
    };
};

export default factories.createCoreController('api::streak.streak', ({strapi}) => ({
    async rawTotal(ctx) {
        const userIdFromBody = ctx.request.body?.user_id;
        const userIdFromQuery = (ctx.query as any)?.user_id;
        const userId = userIdFromBody ?? userIdFromQuery ?? (ctx.state as any)?.user?.id;
        if (!userId) {
            ctx.badRequest('user_id is required');
            return;
        }
        const rawQuery = `
            SELECT COUNT(*) as total
            FROM streaks
            WHERE user_id = ?
        `;
        const result = await strapi.db.connection.raw(rawQuery, [userId]);
        ctx.body = {
            data: { total: result[0]?.total || 0 },
            meta: {},
        };
    },
    async getStreakByUser(ctx) {
        const params = ctx.request.body;

        if (params && params.user_id) {
            // Get current Vietnam date for consistent timezone handling
            const currentVietnamDate = getCurrentVietnamDate();

            const rawQuery = `WITH RECURSIVE week_days
                                                  AS (SELECT 0 AS day_offset, DATE_SUB(DATE(?), INTERVAL WEEKDAY(DATE(?)) DAY) AS day
                               UNION ALL
                     SELECT day_offset + 1,
                            DATE_ADD(DATE_SUB(DATE(?), INTERVAL WEEKDAY(DATE(?)) DAY), INTERVAL day_offset + 1 DAY)
                     FROM week_days
                     WHERE day_offset < 6 ),
                     min_date_streaks as (
                         select min(DATE(DATE_ADD(created_at, INTERVAL 7 HOUR))) min_created FROM streaks where 1=1 AND user_id = ?
                     ),
                     streak_data AS (
                       SELECT DATE(DATE_ADD(created_at, INTERVAL 7 HOUR)) AS date_only, is_join, document_id, time
                       FROM streaks
                       WHERE DATE(DATE_ADD(created_at, INTERVAL 7 HOUR)) >= DATE_SUB(DATE(?), INTERVAL WEEKDAY(DATE(?)) DAY)
                        AND DATE(DATE_ADD(created_at, INTERVAL 7 HOUR)) < DATE_ADD(DATE_SUB(DATE(?), INTERVAL WEEKDAY(DATE(?)) DAY), INTERVAL 7 DAY)
                        AND user_id = ?
                       )
                     select wd.day         AS dateInWeek,
                            sd.document_id AS documentId,
                            sd.time,
                            CASE DAYOFWEEK(wd.day)
                                WHEN 2 THEN 'T2'
                                WHEN 3 THEN 'T3'
                                WHEN 4 THEN 'T4'
                                WHEN 5 THEN 'T5'
                                WHEN 6 THEN 'T6'
                                WHEN 7 THEN 'T7'
                                WHEN 1 THEN 'CN'
                                END        AS name,
                            m.min_created,
                            case
                                when sd.is_join is not null then sd.is_join
                                when sd.document_id is null and wd.day >= m.min_created and wd.day >= DATE(?) then null
                                when sd.document_id is null and wd.day < m.min_created then null
                                when sd.document_id is null and wd.day >= m.min_created then 0
                                end           isJoin,
                            CASE
                                WHEN wd.day = DATE(?) THEN 1
                                ELSE 0
                                END        AS isActive
                     from week_days wd
                              LEFT JOIN streak_data sd ON DATE (sd.date_only) = wd.day
                         LEFT JOIN min_date_streaks m
                     ON TRUE
                     ORDER BY dateInWeek ASC`
            ;

            // Use consistent Vietnam date for all date parameters
            const result = await strapi.db.connection.raw(rawQuery, [
                currentVietnamDate, currentVietnamDate, // week_days CTE
                currentVietnamDate, currentVietnamDate, // week_days recursive
                params.user_id, // min_date_streaks
                currentVietnamDate, currentVietnamDate, // streak_data start
                currentVietnamDate, currentVietnamDate, // streak_data end
                params.user_id, // streak_data user_id
                currentVietnamDate, // isActive comparison 1
                currentVietnamDate  // isActive comparison 2
            ]);

            ctx.body = {
                data: result[0]
            }
        }
    },

    // New method to validate streak submissions and prevent duplicates
    async validateStreakSubmission(ctx) {
        const params = ctx.request.body;

        console.log('ValidateStreakSubmission called with:', params);

        if (!params || !params.user_id || !params.streak_question_id) {
            ctx.badRequest('user_id and streak_question_id are required');
            return;
        }

        try {
            const userId = params.user_id;
            const streakQuestionId = params.streak_question_id;
            const currentVietnamDate = getCurrentVietnamDate();

            console.log('Current Vietnam date:', currentVietnamDate);
            console.log('Validating for user:', userId, 'streak question:', streakQuestionId);

            // Check if user already has a streak for this question today
            const existingStreakQuery = `
                SELECT id, DATE(DATE_ADD(created_at, INTERVAL 7 HOUR)) as vietnam_date
                FROM streaks
                WHERE user_id = ?
                  AND streak_question_id = ?
                  AND DATE(DATE_ADD(created_at, INTERVAL 7 HOUR)) = DATE(?)
                LIMIT 1
            `;

            const existingStreak = await strapi.db.connection.raw(existingStreakQuery, [
                userId, streakQuestionId, currentVietnamDate
            ]);

            if (existingStreak[0] && existingStreak[0].length > 0) {
                console.log('Found existing streak:', existingStreak[0][0]);
                ctx.body = {
                    data: {
                        valid: false,
                        reason: 'DUPLICATE_STREAK',
                        message: 'User already has streak for this question today',
                        existing_streak: existingStreak[0][0]
                    }
                };
                return;
            }

            // Validate that the streak question date matches current date
            const streakQuestionQuery = `
                SELECT id, value, DATE(value) as intended_date
                FROM streak_questions
                WHERE id = ?
            `;

            const streakQuestion = await strapi.db.connection.raw(streakQuestionQuery, [streakQuestionId]);

            if (!streakQuestion[0] || streakQuestion[0].length === 0) {
                ctx.body = {
                    data: {
                        valid: false,
                        reason: 'INVALID_STREAK_QUESTION',
                        message: 'Streak question not found'
                    }
                };
                return;
            }

            const intendedDate = streakQuestion[0][0].intended_date;
            console.log('Streak question date:', intendedDate, 'Current date:', currentVietnamDate);

            if (intendedDate !== currentVietnamDate) {
                console.log('Date mismatch detected!');
                ctx.body = {
                    data: {
                        valid: false,
                        reason: 'DATE_MISMATCH',
                        message: `Date mismatch: current=${currentVietnamDate}, intended=${intendedDate}`,
                        intended_date: intendedDate,
                        current_date: currentVietnamDate
                    }
                };
                return;
            }

            // All validations passed
            console.log('Validation passed successfully');
            ctx.body = {
                data: {
                    valid: true,
                    message: 'Streak submission is valid',
                    current_date: currentVietnamDate,
                    intended_date: intendedDate
                }
            };

        } catch (error) {
            console.error('Error validating streak submission:', error);
            ctx.body = {
                data: {
                    valid: false,
                    reason: 'VALIDATION_ERROR',
                    message: 'Có lỗi xảy ra khi kiểm tra validation. Vui lòng thử lại sau.',
                    error: error.message
                }
            };
        }
    },

    async getTotalRollup(ctx) {
        const params = ctx.request.body;
        if (params && params.user_id) {
            const rawQuery = `
                select dateJoin,
                       max_streak,
                       round(timeCount / totalJoin)             timeCount,
                       trueCount,
                       totalCount,
                       concat(rankingCount, '/', totalRankuser) rankingCount
                from (select DATE(DATE_ADD(created_at, INTERVAL 7 HOUR)) dateJoin,
                     get_max_is_join_streak(?) max_streak,
                     sum(time) timeCount,

                     (select count(1) from streaks a where a.user_id = ? and is_join = 1) totalJoin,
                     (select count(1)
                      from questions_answers a
                               left join questions_answers_user_lnk b on a.id = b.questions_answer_id
                      where b.user_id = ?
                        and a.is_correct = 1
                        and a.streak_question_id is not null) trueCount,
                     (select count(1)
                      from questions_answers a
                               left join questions_answers_user_lnk b on a.id = b.questions_answer_id
                      where b.user_id = ?
                        and a.streak_question_id is not null) totalCount,
                     get_user_rank('streak', ?) rankingCount,
                     (select count(1) from user_rankings where type = 'streak') totalRankuser from streaks
                where user_id = ?
                  and is_join = 1 group by DATE(DATE_ADD(created_at, INTERVAL 7 HOUR))
                    ) a
            `;

            const result = await strapi.db.connection.raw(rawQuery, [params.user_id,params.user_id,params.user_id,params.user_id,params.user_id,params.user_id]);
            const listStreak = result[0]; // mảng chứa các object { dateJoin: "YYYY-MM-DD" }
            let count = 0;
            let tmp = new Date();
            tmp.setHours(0, 0, 0, 0);
            let localDate = tmp.toLocaleDateString('en-CA');
            if (listStreak.some(item => item.dateJoin === localDate)) {
                count++;
            }
            while (true) {
                const yesterday = new Date(tmp);
                yesterday.setDate(yesterday.getDate() - 1);
                yesterday.setHours(0, 0, 0, 0);
                const formatted = yesterday.toLocaleDateString('en-CA');
                const found = listStreak.some(item => item.dateJoin === formatted);
                if (found) {
                    count++;
                    tmp = yesterday;
                } else {
                    break;
                }
            }
            ctx.body = {
                data: {...listStreak[0],count: count}
            }
        }
    },
    async getDataFinish(ctx) {
        const params = ctx.request.body;
        if (params && params.user_id && params.streak_question_id) {
            const rawQuery = `
                select sum(count) count, sum(is_correct) is_correct ,sum( case when is_star_point = 1 then IFNULL(point_,0) *2 else IFNULL(point_,0) end ) point, time,
                get_max_is_join_streak(?) max_streak
                from (
                    select
                    1 count, qa.*, qaq.question_id, (select a.point from exercise_types a where a.id = qet.exercise_type_id and qa.is_correct = 1 limit 1) point_, (select a.time from streaks a where a.user_id = qau.user_id and a.streak_question_id = qa.streak_question_id limit 1) time
                    from questions_answers qa
                    inner join questions_answers_user_lnk qau on qa.id = qau.questions_answer_id
                    inner join questions_answers_question_lnk qaq on qa.id = qaq.questions_answer_id
                    inner join questions_exercise_type_lnk qet on qaq.question_id = qet.question_id
                    where qa.streak_question_id = ? and qau.user_id = ?
                    ) a
                group by time
            `;
            const result = await strapi.db.connection.raw(rawQuery, [ params.user_id,params.streak_question_id,params.user_id]);
            ctx.body = {
                data: result[0]
            }
        }
    },

    async getDataStreakDashboard(ctx) {
        const params = ctx.request.body;
        if (params && params.user_id) {
            const rawQuery = `
                select get_max_is_join_streak(?) max_streak,
                       sum(a.point_)              point
                from (select case
                                 when qa.is_star_point = 1 then ifnull(et.point, 0) * 2
                                 else ifnull(et.point, 0) end point_
                      from questions_answers qa
                               inner join questions_answers_user_lnk qau on qa.id = qau.questions_answer_id
                               inner join questions_answers_question_lnk qaq on qa.id = qaq.questions_answer_id
                               inner join questions_exercise_type_lnk qet on qaq.question_id = qet.question_id
                               inner join exercise_types et on et.id = qet.exercise_type_id
                      where qa.streak_question_id is not null
                        and qau.user_id = ?
                        and qa.is_correct = 1
                      ) a
            `;
            const result = await strapi.db.connection.raw(rawQuery, [ params.user_id,params.user_id]);
            ctx.body = {
                data: result[0]
            }
        }
    },
    async reset(ctx) {
        const params = ctx.request.body as any;
        const userId = params?.user_id;
        const streakQuestionId = params?.streak_question_id;
        if (!userId) {
            ctx.badRequest('user_id is required');
            return;
        }
        try {
            if (streakQuestionId) {
                await strapi.db.connection('questions_answers')
                    .where({ streak_question_id: streakQuestionId })
                    .andWhere('id', 'in', (qb) => {
                        qb.select('qa.id')
                            .from('questions_answers as qa')
                            .innerJoin('questions_answers_user_lnk as qau', 'qa.id', 'qau.questions_answer_id')
                            .where('qau.user_id', userId)
                            .andWhere('qa.streak_question_id', streakQuestionId);
                    })
                    .del();
            } else {
                await strapi.db.connection('questions_answers')
                    .where('id', 'in', (qb) => {
                        qb.select('qa.id')
                            .from('questions_answers as qa')
                            .innerJoin('questions_answers_user_lnk as qau', 'qa.id', 'qau.questions_answer_id')
                            .where('qau.user_id', userId);
                    })
                    .del();
            }
        } catch (e) {
        }
        try {
            const qb = strapi.db.connection('streaks').where({ user_id: userId });
            if (streakQuestionId) qb.andWhere({ streak_question_id: streakQuestionId });
            await qb.del();
        } catch (e) {
        }
        ctx.body = { data: true };
    },

    async getQuestionByStreak(ctx) {
        const params = ctx.request.body;

        if (!params || !params.streakId) {
            ctx.badRequest('streakId is required');
            return;
        }

        try {
            const streakId = params.streakId;

            // Get questions for this streak
            const questionsQuery = `
                SELECT
                    q.id,
                    q.question_text,
                    q.image_path,
                    q.correct_answer_type,
                    q.streak_question_id,
                    q.created_at,
                    q.updated_at
                FROM questions q
                WHERE q.streak_question_id = ?
                ORDER BY q.id ASC
            `;

            const questions = await strapi.db.connection.raw(questionsQuery, [streakId]);

            // Get answers for each question
            const questionsWithAnswers = await Promise.all(
                questions[0].map(async (question) => {
                    const answersQuery = `
                        SELECT
                            qa.id,
                            qa.answer_text,
                            qa.is_correct,
                            qa.image_path as answer_image_path,
                            qa.created_at,
                            qa.updated_at
                        FROM questions_answers qa
                        WHERE qa.question_id = ?
                        ORDER BY qa.id ASC
                    `;

                    const answers = await strapi.db.connection.raw(answersQuery, [question.id]);

                    return {
                        ...question,
                        answers: answers[0] || []
                    };
                })
            );

            ctx.body = {
                data: questionsWithAnswers
            };

        } catch (error) {
            console.error('Error getting questions by streak:', error);
            ctx.internalServerError('Failed to get questions');
        }
    },

    async getAnswerStreakByUser(ctx) {
        const params = ctx.request.body;

        if (!params || !params.user || !params.streakId) {
            ctx.badRequest('user and streakId are required');
            return;
        }

        try {
            const userId = params.user;
            const streakId = params.streakId;

            // Get user's answers for this streak
            const answersQuery = `
                SELECT
                    qau.id,
                    qau.user_id,
                    qau.questions_answer_id,
                    qa.answer_text,
                    qa.is_correct,
                    qa.is_star_point,
                    qa.question_id,
                    qa.streak_question_id,
                    qau.created_at,
                    qau.updated_at
                FROM questions_answers_user_lnk qau
                INNER JOIN questions_answers qa ON qau.questions_answer_id = qa.id
                WHERE qau.user_id = ?
                  AND qa.streak_question_id = ?
                ORDER BY qau.created_at ASC
            `;

            const answers = await strapi.db.connection.raw(answersQuery, [userId, streakId]);

            ctx.body = {
                data: answers[0] || []
            };

        } catch (error) {
            console.error('Error getting answers by user and streak:', error);
            ctx.internalServerError('Failed to get answers');
        }
    },
}));