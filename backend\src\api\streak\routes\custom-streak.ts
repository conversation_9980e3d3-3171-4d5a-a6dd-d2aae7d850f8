export default {
    routes: [
        {
            method: 'GET',
            path: '/streaks/raw-total',
            handler: 'streak.rawTotal',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-streak-by-user',
            handler: 'streak.getStreakByUser',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-total-rollup',
            handler: 'streak.getTotalRollup',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-data-finish',
            handler: 'streak.getDataFinish',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-data-streak-dashboard',
            handler: 'streak.getDataStreakDashboard',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/validate-streak-submission',
            handler: 'streak.validateStreakSubmission',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-question-by-streak',
            handler: 'streak.getQuestionByStreak',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-answer-streak-by-user',
            handler: 'streak.getAnswerStreakByUser',
            config: {
                policies: [],
                middlewares: [],
            },
        },

    ],
};