/**
 * upload-r2 controller
 */

import crypto from 'crypto';

export default {
  /**
   * Upload file lên <PERSON>flare R2
   */
  async uploadToR2(ctx) {
    try {
      const { files } = ctx.request;
      
      if (!files || !files.file) {
        return ctx.badRequest('No file provided');
      }

      const file = Array.isArray(files.file) ? files.file[0] : files.file;
      
      // Debug logging
      console.log('File object structure:', {
        name: file.name,
        originalFilename: file.originalFilename,
        filename: file.filename,
        mimetype: file.mimetype,
        size: file.size
      });
      
      // Validate file type
      const allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedMimes.includes(file.mimetype)) {
        return ctx.badRequest('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        return ctx.badRequest('File size exceeds 5MB limit');
      }

      // Generate unique filename with robust extension handling
      let fileExtension = 'jpg'; // default extension
      
      // Try to get filename from various possible properties
      const fileName = file.name || file.originalFilename || file.filename;
      
      if (fileName && fileName.includes('.')) {
        fileExtension = fileName.split('.').pop();
      } else {
        // Fallback: determine extension from mimetype
        const mimeToExt = {
          'image/jpeg': 'jpg',
          'image/png': 'png',
          'image/gif': 'gif',
          'image/webp': 'webp'
        };
        fileExtension = mimeToExt[file.mimetype] || 'jpg';
      }
      
      const uniqueFilename = `${crypto.randomUUID()}.${fileExtension}`;
      const folder = ctx.request.body.folder || 'streak-questions';
      const key = `${folder}/${uniqueFilename}`;

      // Upload to R2 using service
      const r2Service = strapi.service('api::upload-r2.upload-r2');
      const uploadResult = await r2Service.uploadFile(
        file.filepath,
        key,
        file.mimetype
      );

      if (!uploadResult.success) {
        return ctx.internalServerError('Failed to upload file to R2');
      }

      return {
        success: true,
        data: {
          url: uploadResult.url,
          key: key,
          filename: uniqueFilename,
          originalName: file.name,
          size: file.size,
          mimetype: file.mimetype
        }
      };

    } catch (error) {
      console.error('Error uploading to R2:', error);
      return ctx.internalServerError('Upload failed');
    }
  },

  /**
   * Xóa file từ Cloudflare R2
   */
  async deleteFromR2(ctx) {
    try {
      const { key } = ctx.request.body;
      
      if (!key) {
        return ctx.badRequest('File key is required');
      }

      const r2Service = strapi.service('api::upload-r2.upload-r2');
      const deleteResult = await r2Service.deleteFile(key);

      if (!deleteResult.success) {
        return ctx.internalServerError('Failed to delete file from R2');
      }

      return {
        success: true,
        message: 'File deleted successfully'
      };

    } catch (error) {
      console.error('Error deleting from R2:', error);
      return ctx.internalServerError('Delete failed');
    }
  },

  /**
   * Lấy signed URL để xem file
   */
  async getSignedUrl(ctx) {
    try {
      const { key } = ctx.params;
      
      if (!key) {
        return ctx.badRequest('File key is required');
      }

      const r2Service = strapi.service('api::upload-r2.upload-r2');
      const signedUrlResult = await r2Service.getSignedUrl(key);

      if (!signedUrlResult.success) {
        return ctx.internalServerError('Failed to generate signed URL');
      }

      return {
        success: true,
        url: signedUrlResult.url,
        expiresIn: signedUrlResult.expiresIn
      };

    } catch (error) {
      console.error('Error getting signed URL:', error);
      return ctx.internalServerError('Failed to get signed URL');
    }
  }
};
