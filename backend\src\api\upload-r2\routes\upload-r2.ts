/**
 * upload-r2 router
 */

export default {
  routes: [
    {
      method: 'POST',
      path: '/upload-r2',
      handler: 'api::upload-r2.upload-r2.uploadToR2',
      config: {
        policies: [],
        middlewares: [],
        // Allow file uploads
        body: {
          multipart: true,
        },
      },
    },
    {
      method: 'DELETE',
      path: '/upload-r2',
      handler: 'api::upload-r2.upload-r2.deleteFromR2',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
    {
      method: 'GET',
      path: '/upload-r2/signed-url/:key',
      handler: 'api::upload-r2.upload-r2.getSignedUrl',
      config: {
        policies: [],
        middlewares: [],
        auth: false,
      },
    },
  ],
};
