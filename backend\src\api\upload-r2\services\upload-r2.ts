/**
 * upload-r2 service
 */

import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { createReadStream } from 'fs';

export default {
  /**
   * Khởi tạo S3Client cho Cloudflare R2
   */
  getR2Client() {
    return new S3Client({
      region: 'auto',
      endpoint: process.env.R2_ENDPOINT,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
      },
    });
  },

  /**
   * Upload file lên Cloudflare R2
   */
  async uploadFile(filePath, key, contentType) {
    try {
      const client = this.getR2Client();
      const fileStream = createReadStream(filePath);

      const command = new PutObjectCommand({
        Bucket: process.env.R2_BUCKET_NAME,
        Key: key,
        Body: fileStream,
        ContentType: contentType,
        // Metadata: {
        //   'uploaded-by': 'streak-teacher'
        // }
      });

      await client.send(command);

      // Tạo public URL
      const publicUrl = `${process.env.R2_PUBLIC_URL}/${key}`;

      return {
        success: true,
        url: publicUrl,
        key: key
      };

    } catch (error) {
      console.error('R2 upload error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Xóa file từ Cloudflare R2
   */
  async deleteFile(key) {
    try {
      const client = this.getR2Client();

      const command = new DeleteObjectCommand({
        Bucket: process.env.R2_BUCKET_NAME,
        Key: key,
      });

      await client.send(command);

      return {
        success: true
      };

    } catch (error) {
      console.error('R2 delete error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Tạo signed URL để truy cập file private
   */
  async getSignedUrl(key, expiresIn = 3600) {
    try {
      const client = this.getR2Client();

      const command = new GetObjectCommand({
        Bucket: process.env.R2_BUCKET_NAME,
        Key: key,
      });

      const signedUrl = await getSignedUrl(client, command, { expiresIn });

      return {
        success: true,
        url: signedUrl,
        expiresIn
      };

    } catch (error) {
      console.error('R2 signed URL error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Validate environment variables
   */
  validateR2Config() {
    const requiredVars = [
      'R2_ENDPOINT',
      'R2_ACCESS_KEY_ID', 
      'R2_SECRET_ACCESS_KEY',
      'R2_BUCKET_NAME',
      'R2_PUBLIC_URL'
    ];

    const missing = requiredVars.filter(varName => !process.env[varName]);
    
    if (missing.length > 0) {
      throw new Error(`Missing R2 environment variables: ${missing.join(', ')}`);
    }

    return true;
  }
};
