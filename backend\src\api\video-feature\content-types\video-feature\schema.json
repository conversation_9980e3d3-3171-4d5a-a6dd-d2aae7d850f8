{"kind": "collectionType", "collectionName": "video_features", "info": {"singularName": "video-feature", "pluralName": "video-features", "displayName": "video-feature"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "video_id": {"type": "string"}, "key_id": {"type": "string"}, "aes_key": {"type": "string"}, "manifest_url": {"type": "string"}, "thumbnail": {"type": "string"}, "description": {"type": "text"}, "document": {"type": "string"}, "grades": {"type": "relation", "relation": "manyToOne", "target": "api::grade.grade"}, "courses": {"type": "relation", "relation": "manyToOne", "target": "api::course.course"}, "chapters": {"type": "relation", "relation": "manyToMany", "target": "api::chapter.chapter"}, "status_video": {"type": "enumeration", "enum": ["processing", "ready"]}, "views": {"type": "biginteger"}}}