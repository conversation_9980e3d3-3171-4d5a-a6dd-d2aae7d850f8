{"kind": "collectionType", "collectionName": "video_progresses", "info": {"singularName": "video-progress", "pluralName": "video-progresses", "displayName": "video-progress"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "video_progresses"}, "video": {"type": "string", "required": true}, "lastPosition": {"type": "decimal", "default": 0}, "duration": {"type": "decimal", "default": 0}, "isCompleted": {"type": "boolean", "default": false}}}