{"kind": "collectionType", "collectionName": "video_views", "info": {"singularName": "video-view", "pluralName": "video-views", "displayName": "Video Views", "description": "Track video view counts separately"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"video_id": {"type": "string", "required": true, "unique": true}, "views": {"type": "biginteger", "default": "0", "min": "0"}}}