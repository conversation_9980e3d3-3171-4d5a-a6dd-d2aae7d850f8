/**
 * video-view controller
 */

import { factories } from "@strapi/strapi";
import { incrementViews, getViews } from "../services/video-view-functions";

export default factories.createCoreController("api::video-view.video-view", ({ strapi }) => ({
  
  // Increment views cho video
  async incrementViews(ctx) {
    try {
      const { videoId } = ctx.params;
      const result = await incrementViews(strapi, videoId);
      
      return { data: result };
    } catch (error) {
      console.error("❌ Controller error:", error);
      return ctx.internalServerError("Failed to increment views");
    }
  },

  // Get views cho video
  async getViews(ctx) {
    try {
      const { videoId } = ctx.params;
      const result = await getViews(strapi, videoId);
      
      return { data: result };
    } catch (error) {
      console.error("❌ Controller error:", error);
      return ctx.internalServerError("Failed to get views");
    }
  },
}));
