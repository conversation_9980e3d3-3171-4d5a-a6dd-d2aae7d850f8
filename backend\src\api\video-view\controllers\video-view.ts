/**
 * video-view controller
 */

import { factories } from "@strapi/strapi";
import { incrementViews, getViews } from "../services/video-view-functions";

export default factories.createCoreController(
  "api::video-view.video-view",
  ({ strapi }) => ({
    // Increment views cho video
    async incrementViews(ctx) {
      try {
        const { videoId } = ctx.params;
        const result = await incrementViews(strapi, videoId);

        return { data: result };
      } catch (error) {
        console.error("❌ Controller error:", error);
        return ctx.internalServerError("Failed to increment views");
      }
    },

    // Get views cho video
    async getViews(ctx) {
      try {
        const { videoId } = ctx.params;
        const result = await getViews(strapi, videoId);

        return { data: result };
      } catch (error) {
        console.error("❌ Controller error:", error);
        return ctx.internalServerError("Failed to get views");
      }
    },

    // Get views cho nhiều video cùng lúc - BATCH ENDPOINT
    async getBatchViews(ctx) {
      try {
        const { videoIds } = ctx.query;

        if (!videoIds) {
          return ctx.badRequest("videoIds query parameter is required");
        }

        // Parse videoIds - có thể là string hoặc array
        const ids = Array.isArray(videoIds)
          ? videoIds
          : String(videoIds).split(",");

        console.log("🚀 Getting batch views for:", ids);

        // Lấy tất cả views trong 1 query duy nhất
        const videoViews = await strapi.entityService.findMany(
          "api::video-view.video-view",
          {
            filters: {
              video_id: {
                $in: ids, // Sử dụng $in operator
              },
            },
          }
        );

        // Tạo map từ kết quả
        const viewsMap = {};

        // Initialize tất cả videos với views = 0
        ids.forEach((id) => {
          viewsMap[id] = 0;
        });

        // Update với views thực tế từ database
        videoViews.forEach((item) => {
          viewsMap[item.video_id] = parseInt(String(item.views || 0));
        });

        console.log("✅ Batch views result:", viewsMap);

        return {
          data: {
            success: true,
            views: viewsMap,
          },
        };
      } catch (error) {
        console.error("❌ Error getting batch views:", error);
        return ctx.internalServerError("Failed to get batch views");
      }
    },
  })
);
