/**
 * video-view router
 */

import { factories } from "@strapi/strapi";

export default {
  routes: [
    {
      method: "POST",
      path: "/video-views/:videoId/increment",
      handler: "video-view.incrementViews",
    },
    {
      method: "GET",
      path: "/video-views/:videoId",
      handler: "video-view.getViews",
    },
    {
      method: "GET",
      path: "/video-views/batch",
      handler: "video-view.getBatchViews",
    },
  ],
};
