/**
 * video-view custom functions
 */

// Increment views cho video
export const incrementViews = async (strapi, videoId) => {
  console.log("🚀 Incrementing views for video:", videoId);

  try {
    // Tìm record views cho video này
    const videoViews = await strapi.db.query("api::video-view.video-view").findMany({
      where: {
        video_id: videoId,
      },
    });

    if (videoViews && videoViews.length > 0) {
      // Đã có record - tăng views
      const currentRecord = videoViews[0];
      const currentViews = parseInt(String(currentRecord.views || 0));
      const newViews = currentViews + 1;
      
      const updatedRecord = await strapi.db.query("api::video-view.video-view").update({
        where: { id: currentRecord.id },
        data: { views: newViews },
      });

      console.log("✅ Views updated from", currentViews, "to", newViews);
      
      return {
        success: true,
        views: newViews,
        videoId: videoId,
      };
    } else {
      // Chưa có record - tạo mới với views = 1
      const newRecord = await strapi.db.query("api::video-view.video-view").create({
        data: {
          video_id: videoId,
          views: 1,
        },
      });

      console.log("✅ New views record created with views: 1");
      
      return {
        success: true,
        views: 1,
        videoId: videoId,
      };
    }
  } catch (error) {
    console.error("❌ Error incrementing views:", error);
    throw error;
  }
};

// Get views cho video
export const getViews = async (strapi, videoId) => {
  try {
    const videoViews = await strapi.db.query("api::video-view.video-view").findMany({
      where: {
        video_id: videoId,
      },
    });

    const views = videoViews && videoViews.length > 0 ? parseInt(String(videoViews[0].views || 0)) : 0;
    
    return {
      success: true,
      views: views,
      videoId: videoId,
    };
  } catch (error) {
    console.error("❌ Error getting views:", error);
    return {
      success: true,
      views: 0,
      videoId: videoId,
    };
  }
};
