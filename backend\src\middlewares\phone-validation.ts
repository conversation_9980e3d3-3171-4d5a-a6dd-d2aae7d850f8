/**
 * Phone validation middleware for Vietnamese phone numbers
 */

export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Only validate on POST and PUT requests
    if (!['POST', 'PUT'].includes(ctx.request.method)) {
      return await next();
    }

    const { body } = ctx.request;

    // Lấy phone từ body.data.phone hoặc body.phone
    const phoneValue = body?.data?.phone ?? body?.phone;

    // Check if phone field exists in the request body
    if (phoneValue !== undefined) {
      const phone = phoneValue;

      // Vietnamese phone number validation regex
      // Accepts numbers starting with 03, 05, 07, 08, 09 followed by 8 digits
      const phoneRegex = /^(0[35789])([0-9]{8})$/;

      if (typeof phone !== 'string' || !phoneRegex.test(phone.trim())) {
        ctx.throw(400, 'Số điện thoại không hợp lệ. Vui lòng nhập số điện thoạ<PERSON> hợ<PERSON> lệ (VD: 0912345678)');
      }
    }

    await next();
  };
};