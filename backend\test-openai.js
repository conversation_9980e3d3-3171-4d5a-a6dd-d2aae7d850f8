// Test script để kiểm tra OpenAI API
require('dotenv').config();

const OpenAI = require('openai');

async function testOpenAI() {
  console.log('🧪 Testing OpenAI API...');
  
  // Kiểm tra API key
  if (!process.env.OPENAI_API_KEY) {
    console.error('❌ OPENAI_API_KEY not found in environment variables');
    return;
  }
  
  if (!process.env.OPENAI_API_KEY.startsWith('sk-')) {
    console.error('❌ OPENAI_API_KEY seems invalid (should start with sk-)');
    return;
  }
  
  console.log('✅ OpenAI API key found');
  
  try {
    const client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    
    console.log('🔄 Testing OpenAI connection...');
    
    const response = await client.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'user',
          content: 'Hello! Just respond with "OpenAI API test successful"'
        }
      ],
      max_tokens: 50,
    });

    if (response.choices[0]?.message?.content) {
      console.log('✅ OpenAI API test successful!');
      console.log('📝 Response:', response.choices[0].message.content);
      
      // Test KaTeX conversion
      console.log('\n🧮 Testing KaTeX conversion...');
      const testResponse = await client.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'Bạn là chuyên gia chuyển đổi công thức hóa học sang KaTeX. Chuyển đổi: "H2O" thành "$H_2O$"'
          },
          {
            role: 'user',
            content: 'Chuyển đổi: "2H2 + O2 → 2H2O"'
          }
        ],
        max_tokens: 100,
      });
      
      console.log('✅ KaTeX conversion test:');
      console.log('📝 Input: "2H2 + O2 → 2H2O"');
      console.log('📝 Output:', testResponse.choices[0]?.message?.content);
      
    } else {
      console.error('❌ OpenAI responded but with empty content');
    }
    
  } catch (error) {
    console.error('❌ OpenAI API test failed:', error.message);
    
    if (error.status === 401) {
      console.error('💡 Tip: Check if your API key is correct and has sufficient credits');
    } else if (error.status === 429) {
      console.error('💡 Tip: Rate limit exceeded, try again later');
    } else if (error.code === 'ENOTFOUND') {
      console.error('💡 Tip: Check your internet connection');
    }
  }
}

testOpenAI();


