const { S3Client, PutObjectCommand, ListObjectsV2Command } = require('@aws-sdk/client-s3');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// R2 Configuration
const r2Config = {
  region: 'auto',
  endpoint: process.env.R2_ENDPOINT,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  },
};

const s3Client = new S3Client(r2Config);
const bucketName = process.env.R2_BUCKET_NAME;

// Test functions
async function testR2Connection() {
  console.log('🔧 Testing Cloudflare R2 Connection...');
  console.log('📋 Configuration:');
  console.log(`   Endpoint: ${process.env.R2_ENDPOINT}`);
  console.log(`   Access Key ID: ${process.env.R2_ACCESS_KEY_ID}`);
  console.log(`   Bucket: ${bucketName}`);
  console.log(`   Public URL: ${process.env.R2_PUBLIC_URL}`);
  console.log('');

  try {
    // Test 1: List objects in bucket
    console.log('📂 Test 1: Listing objects in bucket...');
    const listCommand = new ListObjectsV2Command({
      Bucket: bucketName,
      MaxKeys: 5
    });
    
    const listResult = await s3Client.send(listCommand);
    console.log(`✅ Success! Found ${listResult.KeyCount || 0} objects`);
    
    if (listResult.Contents && listResult.Contents.length > 0) {
      console.log('   Recent objects:');
      listResult.Contents.slice(0, 3).forEach(obj => {
        console.log(`   - ${obj.Key} (${obj.Size} bytes)`);
      });
    }
    console.log('');

    // Test 2: Upload a test file
    console.log('📤 Test 2: Uploading test file...');
    const testContent = `Test file uploaded at ${new Date().toISOString()}`;
    const testKey = `test-${Date.now()}.txt`;
    
    const uploadCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: testKey,
      Body: testContent,
      ContentType: 'text/plain'
    });
    
    const uploadResult = await s3Client.send(uploadCommand);
    console.log(`✅ Upload successful! ETag: ${uploadResult.ETag}`);
    
    const publicUrl = `${process.env.R2_PUBLIC_URL}/${testKey}`;
    console.log(`🌐 Public URL: ${publicUrl}`);
    console.log('');

    console.log('🎉 All tests passed! R2 connection is working properly.');
    
  } catch (error) {
    console.error('❌ R2 Connection Test Failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.Code || 'Unknown'}`);
    
    if (error.$metadata) {
      console.error(`   HTTP Status: ${error.$metadata.httpStatusCode}`);
    }
    
    // Common error suggestions
    if (error.Code === 'AccessDenied') {
      console.error('\n💡 Suggestions:');
      console.error('   - Check if Access Key ID and Secret Access Key are correct');
      console.error('   - Verify bucket permissions in Cloudflare dashboard');
      console.error('   - Ensure the token has R2 read/write permissions');
    } else if (error.Code === 'NoSuchBucket') {
      console.error('\n💡 Suggestions:');
      console.error('   - Check if bucket name is correct');
      console.error('   - Verify bucket exists in Cloudflare R2 dashboard');
    } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      console.error('\n💡 Suggestions:');
      console.error('   - Check if R2_ENDPOINT is correct');
      console.error('   - Verify internet connection');
    }
    
    process.exit(1);
  }
}

// Validate environment variables
function validateConfig() {
  const requiredVars = [
    'R2_ENDPOINT',
    'R2_ACCESS_KEY_ID', 
    'R2_SECRET_ACCESS_KEY',
    'R2_BUCKET_NAME',
    'R2_PUBLIC_URL'
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.error('\nPlease check your .env file.');
    process.exit(1);
  }
}

// Main execution
async function main() {
  console.log('🚀 Cloudflare R2 Connection Test');
  console.log('================================\n');
  
  validateConfig();
  await testR2Connection();
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testR2Connection, validateConfig };