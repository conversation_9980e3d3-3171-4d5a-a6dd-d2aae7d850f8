// Test script để kiểm tra các API streak-teacher
require('dotenv').config();

const axios = require('axios');

const STRAPI_URL = 'http://localhost:1337/api';

async function testStreakTeacherAPIs() {
  console.log('🧪 Testing Streak Teacher APIs...');
  
  try {
    // Test 1: Test OpenAI API
    console.log('\n1️⃣ Testing OpenAI API...');
    try {
      const openaiResponse = await axios.get(`${STRAPI_URL}/openai-katex/test`);
      console.log('✅ OpenAI API test:', openaiResponse.data);
    } catch (error) {
      console.log('❌ OpenAI API test failed:', error.response?.data || error.message);
    }

    // Test 2: Test calendar API
    console.log('\n2️⃣ Testing Calendar API...');
    try {
      const calendarResponse = await axios.get(`${STRAPI_URL}/streak-teacher/calendar/10`);
      console.log('✅ Calendar API test:', calendarResponse.data);
    } catch (error) {
      console.log('❌ Calendar API test failed:', error.response?.data || error.message);
    }

    // Test 3: Test get questions by date
    console.log('\n3️⃣ Testing Get Questions by Date API...');
    try {
      const questionsResponse = await axios.get(`${STRAPI_URL}/streak-teacher/questions/2025-01-20/10`);
      console.log('✅ Get Questions API test:', questionsResponse.data);
    } catch (error) {
      console.log('❌ Get Questions API test failed:', error.response?.data || error.message);
    }

    // Test 4: Test OpenAI KaTeX conversion
    console.log('\n4️⃣ Testing OpenAI KaTeX Conversion...');
    try {
      const conversionResponse = await axios.post(`${STRAPI_URL}/openai-katex/convert`, {
        text: "Phản ứng giữa H2 và O2 tạo ra H2O ở 25°C",
        type: "chemistry"
      });
      console.log('✅ KaTeX Conversion test:');
      console.log('📝 Input:', "Phản ứng giữa H2 và O2 tạo ra H2O ở 25°C");
      console.log('📝 Output:', conversionResponse.data.converted);
    } catch (error) {
      console.log('❌ KaTeX Conversion test failed:', error.response?.data || error.message);
    }

    // Test 5: Test create questions (commented out để không tạo data thật)
    console.log('\n5️⃣ Testing Create Questions API (simulation)...');
    const testQuestions = {
      date: "2025-01-25",
      grade: 10,
      questions: [
        {
          content: "Công thức hóa học của nước là gì?",
          type: "TN_4",
          A: "H2O",
          B: "H2O2", 
          C: "HO",
          D: "H3O",
          correctAnswer: "H2O",
          correctAnswerType: "A",
          explain: "Nước có công thức hóa học là H2O"
        },
        {
          content: "Khí oxygen có công thức là O2",
          type: "TN_2", 
          correctAnswer: "Đúng",
          correctAnswerType: "True",
          explain: "Khí oxygen thật sự có công thức O2"
        },
        {
          content: "Tính khối lượng mol của CaCO3",
          type: "TN_4",
          A: "100 g/mol",
          B: "90 g/mol",
          C: "110 g/mol", 
          D: "120 g/mol",
          correctAnswer: "100 g/mol",
          correctAnswerType: "A",
          explain: "CaCO3 = 40 + 12 + 48 = 100 g/mol"
        }
      ]
    };
    
    console.log('✅ Test questions data prepared:');
    console.log('📝 Date:', testQuestions.date);
    console.log('📝 Grade:', testQuestions.grade);
    console.log('📝 Questions count:', testQuestions.questions.length);
    console.log('💡 To actually create questions, uncomment the API call in the script');

    // Uncomment để thực sự tạo câu hỏi:
    // try {
    //   const createResponse = await axios.post(`${STRAPI_URL}/streak-teacher/questions`, testQuestions);
    //   console.log('✅ Create Questions test:', createResponse.data);
    // } catch (error) {
    //   console.log('❌ Create Questions test failed:', error.response?.data || error.message);
    // }

  } catch (error) {
    console.error('❌ General test error:', error.message);
  }
}

// Chạy test
testStreakTeacherAPIs();

