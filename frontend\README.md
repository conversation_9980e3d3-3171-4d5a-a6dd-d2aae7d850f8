# 🌐 Ông Ba Dạy Hóa - Frontend (Next.js)

[![Next.js](https://img.shields.io/badge/Next.js-15.3.1-black?style=flat&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.0.0-61DAFB?style=flat&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-3178C6?style=flat&logo=typescript)](https://typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.17-38B2AC?style=flat&logo=tailwind-css)](https://tailwindcss.com/)

> **Modern React Frontend** - Giao diện người dùng hiện đại cho nền tảng học hóa học trực tuyến

## 📋 Tổng quan

Frontend của **Ông Ba Dạy Hóa** được xây dựng trên **Next.js 15** với **React 19**, sử dụng **App Router** hiện đại và **TypeScript** để tạo trải nghiệm người dùng mượt mà. Dự án áp dụng **mobile-first design** với **Tailwind CSS** và các component tái sử dụng.

### 🎯 Tính năng chính

| Tính năng | Mô tả | Trạng thái |
|-----------|-------|------------|
| 🌐 **Next.js 15** | App Router, Server Components, SSR | ✅ Hoàn thành |
| 📱 **Responsive Design** | Mobile-first, adaptive layouts | ✅ Hoàn thành |
| 🔐 **Authentication** | JWT, Google OAuth, middleware protection | ✅ Hoàn thành |
| 💳 **Payment Flow** | PayOS integration, order management | ✅ Hoàn thành |
| 🎯 **Learning Dashboard** | Progress tracking, streak system | ✅ Hoàn thành |
| 📊 **Real-time Updates** | Live notifications, progress sync | ✅ Hoàn thành |
| 🎨 **Modern UI** | Tailwind CSS, custom components | ✅ Hoàn thành |

## 🏗️ Kiến trúc hệ thống

### Next.js App Router Architecture

```mermaid
graph TD
    A[🌐 Next.js 15] --> B[📱 App Router]
    B --> C[🏠 layout.js - Root Layout]
    C --> D[🏠 page.js - Homepage]
    C --> E[📄 error.js - Error Boundary]
    C --> F[⏳ loading.js - Loading States]

    G[🔐 Authentication] --> H[dang-nhap/page.jsx]
    G --> I[dang-ky/page.jsx]
    G --> J[xac-thuc/page.jsx]

    K[📚 Content Pages] --> L[khoa-hoc/[slug]/page.jsx]
    K --> M[bai-viet/[id]/page.jsx]

    N[👨‍🎓 Dashboard] --> O[quan-ly/page.jsx]
    N --> P[thanh-toan/page.jsx]
    N --> Q[thong-tin-ca-nhan/page.jsx]
```

### Component Architecture

```mermaid
flowchart TD
    A[🌳 Component Tree] --> B[📐 AppShell]
    B --> C[🧭 Header]
    C --> D[🔐 Auth State]
    C --> E[👤 User Menu]
    C --> F[📱 Mobile Menu]

    B --> G[📄 Page Content]
    B --> H[🦶 Footer]

    I[🔄 Context Providers] --> J[🔐 AuthProvider]
    I --> K[👤 UserProvider]
    I --> L[🔔 NotificationProvider]
    I --> M[📊 DashboardProvider]

    N[🎯 Feature Components] --> O[📚 CourseCard]
    N --> P[🎬 VideoPlayer]
    N --> Q[📝 Quiz Components]
    N --> R[🔥 Streak Components]
```

## 📂 Cấu trúc dự án

```
frontend/
├── 📱 app/                          # Next.js 13+ App Router
│   ├── 🏠 layout.js                 # Root layout với providers
│   ├── 🏠 page.js                   # Trang chủ
│   ├── 📡 api/
│   │   └── 🔗 strapi.js            # Axios client + API methods
│   ├── 🔐 auth/                    # Authentication pages
│   │   ├── 🖊️ dang-ky/             # User registration
│   │   ├── 🔑 dang-nhap/           # Login page
│   │   ├── 📧 quen-mat-khau/       # Password reset
│   │   ├── 🔢 xac-thuc/            # OTP verification
│   │   └── 🔓 mat-khau-moi/        # New password
│   ├── 📚 khoa-hoc/
│   │   └── 📖 [slug]/              # Dynamic course pages
│   ├── 📝 bai-viet/
│   │   └── 📄 [id]/                # Dynamic blog pages
│   ├── 👨‍🎓 quan-ly/                # Learning dashboard
│   ├── 💳 thanh-toan/              # Payment page
│   ├── 📄 hoa-don/                 # Invoice page
│   ├── 👤 thong-tin-ca-nhan/       # Profile page
│   ├── 🔔 thong-bao/               # Notifications
│   ├── 🏦 tai-khoan/               # Account settings
│   ├── 🧑‍🏫 streak-giao-vien/       # Teacher streak
│   └── 📄 not-found.js             # 404 page
├── 🧩 components/                   # Reusable React Components
│   ├── 🧩 AppLoading.jsx           # Loading spinner
│   ├── 📐 AppShell.js              # Main layout wrapper
│   ├── 🔘 Button.jsx               # Custom button component
│   ├── 🔥 ContinueStreak.jsx       # Streak continuation
│   ├── ⏱️ CountdownTimer.jsx       # Timer for quizzes
│   ├── 📢 CtaSection.jsx           # Call-to-action sections
│   ├── 🧭 Header.jsx               # Navigation header
│   ├── 🦶 Footer.jsx               # Site footer
│   ├── 📱 MobileMenu.jsx           # Mobile navigation
│   ├── 📝 Modal.jsx                # Reusable modal
│   ├── 🔔 Notification.jsx        # Notification component
│   ├── 🚫 OutStreakPopup.jsx      # Streak warning
│   ├── 📝 TextField.jsx            # Custom input field
│   ├── 🎬 VideoPlayer.jsx          # Video player component
│   ├── 📱 VideoPopup.jsx           # Video modal
│   ├── 🎯 dashboard/               # Dashboard components
│   ├── 🎨 icons/                   # Custom SVG icons
│   ├── 📐 layouts/                 # Layout components
│   ├── 🧑‍🏫 streak-teacher/        # Teacher-specific components
│   └── 🎨 ui/                      # Base UI components
├── 🔄 context/                     # React Context Providers
│   ├── 🔐 AuthContext.js          # Authentication state
│   ├── 👤 UserProvider.jsx        # User data management
│   ├── 🔔 NotificationContext.jsx # Notification state
│   ├── 🍞 ToastContext.jsx        # Toast notifications
│   └── 📐 DashboardLayoutContext.jsx # Dashboard layout
├── 🗂️ data/                        # Static data
│   └── 🏫 schools/                 # School information
├── 🖼️ public/                      # Static assets
│   ├── 🖼️ images/                  # Image assets
│   ├── 📄 Favi.png                # Favicon
│   └── 📱 site.webmanifest        # PWA manifest
├── 🛠️ utils/                       # Utility functions
│   ├── 🍪 cookieHelper.js         # Cookie management
│   ├── 🔗 CommonUtil.js           # Common utilities
│   └── ✅ validators.js            # Form validation
└── ⚙️ Configuration               # Config files
    ├── 📦 package.json            # Dependencies & scripts
    ├── ⚙️ next.config.mjs          # Next.js configuration
    ├── 🎨 tailwind.config.js       # Tailwind CSS config
    ├── 🛡️ middleware.js            # Route protection
    └── 🔧 jsconfig.json            # JavaScript config
```

## 🛠️ Tech Stack

### Core Technologies

| Công nghệ | Version | Mục đích |
|-----------|---------|----------|
| **Next.js** | 15.3.1 | React Framework |
| **React** | 19.0.0 | UI Library |
| **TypeScript** | 5.x | Type Safety |
| **Tailwind CSS** | 3.4.17 | Styling Framework |

### UI/UX Libraries

| Library | Version | Purpose |
|---------|---------|---------|
| **React Icons** | 5.5.0 | Icon library |
| **React Spinners** | 0.16.1 | Loading indicators |
| **React Hot Toast** | 2.5.2 | Toast notifications |
| **KaTeX** | 0.16.8 | Math formula rendering |
| **Swiper** | 11.2.6 | Carousel/slider |

### State Management

| Library | Purpose |
|---------|---------|
| **React Context** | Global state management |
| **Universal Cookies** | Cookie management |
| **JWT Decode** | Token handling |

## 🎯 Key Features

### 🔄 State Management Architecture

```mermaid
graph TD
    A[🌳 Context Tree] --> B[📐 AppShell]
    B --> C[🍞 ToastProvider]
    C --> D[👤 UserProvider]
    D --> E[🔐 AuthProvider]
    E --> F[🔔 NotificationProvider]
    F --> G[📊 DashboardProvider]

    H[📊 State Flow] --> I[🔐 Auth State]
    H --> J[👤 User Data]
    H --> K[🔔 Notifications]
    H --> L[🍞 Toast Messages]
    H --> M[📐 Layout Config]
```

### 🛡️ Middleware Protection

```javascript
// middleware.js - Route Protection
export function middleware(request) {
  const token = request.cookies.get('access_token')?.value;
  const user_data = request.cookies.get('user_data')?.value;
  const { pathname } = request.nextUrl;

  // Public paths
  const publicPaths = ['/', '/dang-nhap', '/dang-ky', '/khoa-hoc', '/bai-viet'];

  // Private paths requiring authentication
  const privatePaths = ['/quan-ly', '/thanh-toan', '/thong-tin-ca-nhan'];

  // Redirect logic based on authentication and user status
  if (!token && !publicPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.redirect(new URL('/dang-nhap', request.url));
  }

  // Check user profile completion
  if (token && user_data) {
    const userData = JSON.parse(user_data);
    const hasFullPersonalInfo = userData.fullname && userData.date && (userData.gender !== undefined);

    if (!hasFullPersonalInfo && pathname !== '/thong-tin-ca-nhan') {
      return NextResponse.redirect(new URL('/thong-tin-ca-nhan', request.url));
    }
  }

  return NextResponse.next();
}
```

### 📡 API Integration

```javascript
// app/api/strapi.js
const strapiAPI = {
  // Authentication
  auth: {
    login: async (email, password) => {
      const response = await instance.post('/auth/local', {
        identifier: email,
        password
      });
      return response.data;
    },

    signup: async (userData) => {
      const response = await instance.post('/auth/signup', userData);
      return response.data;
    },

    googleAuth: async (googleData) => {
      const response = await instance.post('/auth/login', {
        email: googleData.email,
        provider: 'google',
        googleToken: googleData.credential
      });
      return response.data;
    }
  },

  // Content
  courses: {
    getAllCourses: async () => {
      const response = await instance.get('/courses?populate=*');
      return response.data;
    },

    getCourseBySlug: async (slug) => {
      const response = await instance.get(
        `/courses?filters[slug][$eq]=${slug}&populate=*`
      );
      return response.data.data[0];
    }
  },

  // Payment
  payment: {
    createPaymentLink: async (data) => {
      const response = await instance.post('/payments/create-payment-link', data);
      return response.data;
    },

    verifyPayment: async (orderCode) => {
      const response = await instance.post('/payments/verify', { orderCode });
      return response.data;
    }
  },

  // Learning
  streak: {
    getDataByUser: async (data) => {
      const response = await instance.post('/streaks/get-streak-by-user', data);
      return response.data;
    },

    createStreak: async (data) => {
      const response = await instance.post('/streaks', { data });
      return response.data;
    }
  }
};
```

## 🎨 Design System

### Tailwind Configuration

```javascript
// tailwind.config.js
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}'
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          default: '#198C43',
          hover: '#166B34',
          focused: '#166B34',
          pressed: '#0F4A22',
          disabled: '#F3F4F6'
        },
        secondary: {
          gray: '#6B7280',
          color: '#2563eb'
        }
      },
      spacing: {
        'xs': '4px',
        'sm': '8px',
        'md': '12px',
        'lg': '16px',
        'xl': '24px',
        '2xl': '32px',
        '3xl': '48px'
      },
      fontSize: {
        'xs': ['12px', { lineHeight: '16px' }],
        'sm': ['14px', { lineHeight: '20px' }],
        'md': ['16px', { lineHeight: '24px' }],
        'lg': ['18px', { lineHeight: '28px' }],
        'xl': ['20px', { lineHeight: '28px' }],
        '2xl': ['24px', { lineHeight: '32px' }]
      }
    }
  }
};
```

### Component Variants

```javascript
// Button Component Variants
const variants = {
  primary: "bg-primary-default text-white hover:bg-primary-hover...",
  secondaryGray: "bg-secondary-gray-default hover:bg-secondary-gray-hover...",
  secondaryColor: "bg-secondary-color-default hover:bg-secondary-color-hover..."
};

// Modal Sizes
const sizes = {
  sm: 'max-w-md',
  md: 'max-w-lg',
  lg: 'max-w-2xl',
  xl: 'max-w-4xl'
};
```

## 📱 Pages & Routes

| Route | Component | Purpose | Access | Layout |
|-------|-----------|---------|--------|--------|
| `/` | `page.js` | Homepage với hero section | Public | Default |
| `/khoa-hoc` | `page.jsx` | Danh sách khóa học | Public | Default |
| `/khoa-hoc/[slug]` | `[slug]/page.jsx` | Chi tiết khóa học | Public | Default |
| `/bai-viet` | `page.jsx` | Danh sách blog | Public | Default |
| `/bai-viet/[id]` | `[id]/page.jsx` | Chi tiết bài viết | Public | Default |
| `/dang-nhap` | `page.jsx` | Đăng nhập | Public | Auth |
| `/dang-ky` | `page.jsx` | Đăng ký | Public | Auth |
| `/quen-mat-khau` | `page.jsx` | Quên mật khẩu | Public | Auth |
| `/xac-thuc` | `page.jsx` | OTP verification | Public | Auth |
| `/thong-tin-ca-nhan` | `page.jsx` | Profile completion | Private | Default |
| `/quan-ly` | `page.jsx` | Learning dashboard | Private | Dashboard |
| `/thanh-toan` | `page.jsx` | Payment page | Private | Default |
| `/hoa-don` | `page.jsx` | Invoice page | Private | Default |
| `/thong-bao` | `page.jsx` | Notifications | Private | Default |
| `/tai-khoan` | `page.jsx` | Account settings | Private | Default |

## 🚀 Cài đặt & Chạy

### Prerequisites

- **Node.js**: 18.0.0 - 22.x.x
- **npm** hoặc **yarn**
- **Backend API**: Chạy tại `http://localhost:1337`

### Installation

```bash
# 1. Clone repository
git clone <repository-url>
cd ong-b-a-day-hoa/frontend

# 2. Install dependencies
npm install

# 3. Setup environment
cp .env.example .env.local
# Edit .env.local with your configuration
```

### Development

```bash
# Start development server
npm run dev

# Server sẽ chạy tại: http://localhost:3000
# Với hot reload và TypeScript checking
```

### Production

```bash
# 1. Build application
npm run build

# 2. Start production server
npm run start

# 3. Hoặc deploy lên Vercel/Netlify
npm run build
# Upload thư mục .next và static files
```

## ⚙️ Scripts

| Script | Mô tả |
|--------|-------|
| `npm run dev` | Development server với hot reload |
| `npm run build` | Build production bundle |
| `npm run start` | Production server |
| `npm run lint` | ESLint code checking |
| `npm run test` | Chạy unit tests |
| `npm run test:watch` | Test với watch mode |

## 🎯 Component Categories

### Layout Components

```javascript
// AppShell - Main layout wrapper
<Providers>
  <ToastProvider>
    <UserProvider>
      <AuthProvider>
        <NotificationProvider>
          {showHeaderFooter ? <Header /> : null}
          <main>{children}</main>
          {showHeaderFooter ? <Footer /> : null}
        </NotificationProvider>
      </AuthProvider>
    </UserProvider>
  </ToastProvider>
</Providers>
```

### UI Components

```javascript
// Button with variants
<Button variant="primary" icon={<Icon />} onClick={handleClick}>
  Click me
</Button>

// Modal with custom content
<Modal isOpen={isOpen} onClose={onClose} headerTitle="Title">
  <div>Modal content</div>
</Modal>

// TextField with validation
<TextField
  label="Email"
  type="email"
  value={email}
  onChange={setEmail}
  error={emailError}
  placeholder="Enter your email"
/>
```

### Feature Components

```javascript
// VideoPlayer with controls
<VideoPlayer
  src={videoUrl}
  poster={thumbnail}
  onProgress={handleProgress}
  onComplete={handleComplete}
/>

// CourseCard with actions
<CourseCard
  course={course}
  onEnroll={handleEnroll}
  isEnrolled={isEnrolled}
  progress={progress}
/>
```

## 📊 Performance Optimization

### Next.js Optimizations

- **Server Components**: Reduce bundle size
- **Image Optimization**: Automatic WebP conversion
- **Font Optimization**: Self-hosted Google Fonts
- **Code Splitting**: Automatic route-based splitting

### Bundle Analysis

```bash
# Analyze bundle size
npm run build
# Check .next/analyze folder

# Key metrics to monitor:
# - First Contentful Paint (FCP)
# - Largest Contentful Paint (LCP)
# - First Input Delay (FID)
# - Cumulative Layout Shift (CLS)
```

### Caching Strategy

```javascript
// Browser caching
export default function RootLayout({ children }) {
  return (
    <html lang="vi">
      <head>
        <meta httpEquiv="Cache-Control" content="public, max-age=31536000" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
      </head>
      <body>{children}</body>
    </html>
  );
}
```

## 🛡️ Security Features

### Authentication Flow

```javascript
// Protected route pattern
'use client';

import { useContext } from 'react';
import { UserContext } from '../context/UserProvider';
import { useRouter } from 'next/navigation';

export default function ProtectedPage() {
  const { user, isAuthenticated } = useContext(UserContext);
  const router = useRouter();

  if (!isAuthenticated) {
    router.push('/dang-nhap');
    return null;
  }

  return <div>Protected content</div>;
}
```

### Input Validation

```javascript
// Form validation utilities
export const validators = {
  email: (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? null : 'Email không hợp lệ';
  },

  phone: (value) => {
    const phoneRegex = /^[0-9]{10,11}$/;
    return phoneRegex.test(value) ? null : 'Số điện thoại không hợp lệ';
  },

  required: (value) => {
    return value && value.trim() ? null : 'Trường này là bắt buộc';
  }
};
```

## 🔄 Deployment

### Production Checklist

- [ ] Environment variables configured
- [ ] API endpoints updated
- [ ] SEO meta tags configured
- [ ] Analytics tracking added
- [ ] Error boundaries implemented
- [ ] Loading states optimized

### Deployment Options

```bash
# Vercel (Recommended)
vercel --prod

# Netlify
netlify deploy --prod

# Docker
docker build -t ongbadayhoa-frontend .
docker run -p 3000:3000 ongbadayhoa-frontend

# Manual deployment
npm run build
# Upload .next folder to hosting provider
```

## 📊 Monitoring & Analytics

### Google Analytics Integration

```javascript
// Google Analytics component
import Script from 'next/script';

export default function GoogleAnalytics() {
  return (
    <>
      <Script
        src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'GA_MEASUREMENT_ID');
        `}
      </Script>
    </>
  );
}
```

### Error Tracking

```javascript
// Error boundary component
'use client';

import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error to monitoring service
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div>Something went wrong.</div>;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Follow coding standards and component patterns
4. Test on multiple devices and browsers
5. Commit changes: `git commit -m 'Add new feature'`
6. Push to branch: `git push origin feature/new-feature`
7. Create Pull Request

### Code Standards

- Use TypeScript for type safety
- Follow component naming conventions
- Implement responsive design
- Add proper error handling
- Write meaningful commit messages

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: https://nextjs.org/docs
- **Community**: https://github.com/vercel/next.js/discussions

## 📝 License

This project is licensed under the MIT License.
