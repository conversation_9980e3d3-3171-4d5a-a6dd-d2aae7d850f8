import { NextResponse } from 'next/server';

const STRAPI_BASE_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337/api';

export async function GET(request, { params }) {
  try {
    const { path } = await params;
    const pathString = Array.isArray(path) ? path.join('/') : path;
    const url = new URL(request.url);
    const searchParams = url.searchParams.toString();
    
    const strapiUrl = `${STRAPI_BASE_URL}/${pathString}${searchParams ? `?${searchParams}` : ''}`;
    
    // Forward all relevant headers
    const headers = {
      'Content-Type': 'application/json',
    };

    // Forward authorization header
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    // Forward cookies
    const cookieHeader = request.headers.get('cookie');
    if (cookieHeader) {
      headers['Cookie'] = cookieHeader;
    }

    const response = await fetch(strapiUrl, {
      method: 'GET',
      headers
    });

    const data = await response.json();
    
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Strapi proxy GET error:', error);
    return NextResponse.json(
      { error: 'Proxy error', message: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request, { params }) {
  try {
    const { path } = await params;
    const pathString = Array.isArray(path) ? path.join('/') : path;
    const body = await request.text();
    
    const strapiUrl = `${STRAPI_BASE_URL}/${pathString}`;
    
    // Forward all relevant headers
    const headers = {
      'Content-Type': 'application/json',
    };

    // Forward authorization header
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    // Forward cookies
    const cookieHeader = request.headers.get('cookie');
    if (cookieHeader) {
      headers['Cookie'] = cookieHeader;
    }

    const response = await fetch(strapiUrl, {
      method: 'POST',
      headers,
      body
    });

    const data = await response.json();
    
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Strapi proxy POST error:', error);
    return NextResponse.json(
      { error: 'Proxy error', message: error.message },
      { status: 500 }
    );
  }
}

export async function PUT(request, { params }) {
  try {
    const { path } = await params;
    const pathString = Array.isArray(path) ? path.join('/') : path;
    const body = await request.text();
    
    const strapiUrl = `${STRAPI_BASE_URL}/${pathString}`;
    
    const response = await fetch(strapiUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        // Forward authorization if present
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')
        })
      },
      body
    });

    const data = await response.json();
    
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Strapi proxy PUT error:', error);
    return NextResponse.json(
      { error: 'Proxy error', message: error.message },
      { status: 500 }
    );
  }
}

export async function DELETE(request, { params }) {
  try {
    const { path } = await params;
    const pathString = Array.isArray(path) ? path.join('/') : path;
    
    const strapiUrl = `${STRAPI_BASE_URL}/${pathString}`;
    
    const response = await fetch(strapiUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        // Forward authorization if present
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')
        })
      }
    });

    const data = await response.json();
    
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Strapi proxy DELETE error:', error);
    return NextResponse.json(
      { error: 'Proxy error', message: error.message },
      { status: 500 }
    );
  }
}
