import { NextResponse } from 'next/server';

const STRAPI_BASE_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1337/api';

export async function POST(request) {
  try {
    const formData = await request.formData();
    
    const strapiUrl = `${STRAPI_BASE_URL}/upload-r2`;
    
    const response = await fetch(strapiUrl, {
      method: 'POST',
      body: formData,
      headers: {
        // Don't set Content-Type for FormData, let the browser handle it
        // Forward authorization if present
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')
        })
      }
    });

    const data = await response.json();
    
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Upload proxy error:', error);
    return NextResponse.json(
      { error: 'Upload proxy error', message: error.message },
      { status: 500 }
    );
  }
}

export async function DELETE(request) {
  try {
    const body = await request.text();
    
    const strapiUrl = `${STRAPI_BASE_URL}/upload-r2`;
    
    const response = await fetch(strapiUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        // Forward authorization if present
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')
        })
      },
      body
    });

    const data = await response.json();
    
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Delete proxy error:', error);
    return NextResponse.json(
      { error: 'Delete proxy error', message: error.message },
      { status: 500 }
    );
  }
}

