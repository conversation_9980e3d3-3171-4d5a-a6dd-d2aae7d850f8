import axios from "axios";
import Cookies from "universal-cookie";

// Tạo instance với cấu hình cơ bản
const createStrapiClient = (token = null) => {
  const instance = axios.create({
    baseURL:
      process.env.NEXT_PUBLIC_STRAPI_API_URL ||
      "http://localhost:1337/api" ||
      "http://***********:1337/api",
  });

  // Thêm interceptor để tự động thêm token vào mọi request
  instance.interceptors.request.use(
    (config) => {
      // Nếu token được truyền vào, ưu tiên sử dụng token đó
      let tokenToUse = token;

      // Nếu không có token được truyền vào và đang ở môi trường browser, thử lấy từ cookie
      if (!tokenToUse && typeof window !== "undefined") {
        const cookies = new Cookies();
        // Thử lấy từ cookie access_token hoặc token
        tokenToUse = cookies.get("access_token");
      }
      if (tokenToUse) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${tokenToUse}`,
        };
      }
      //HA comment =>chỉ gán Content-Type = application/json khi data khác formData, nếu mặc định application/json thì ko gửi file đc về BE
      if (!(config.data instanceof FormData)) {
        config.headers["Content-Type"] = "application/json";
      }

      // Không cần xử lý ở phía client, token sẽ được gửi tự động qua cookie
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Log để debug response
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  return instance;
};

// Client-side API functions
const auth = {
  // Local login
  login: async (email, password) => {
    try {
      const instance = createStrapiClient();
      const response = await instance.post("/auth/local", {
        identifier: email,
        password,
        provider: "local",
      });

      if (!response.data) {
        throw new Error("Invalid response from server");
      }

      // Lưu token vào cookie nếu đăng nhập thành công
      if (response.data.jwt) {
        const cookies = new Cookies();
        cookies.set("token", response.data.jwt, {
          path: "/",
          maxAge: 7 * 24 * 60 * 60, // 7 ngày
          secure: process.env.NODE_ENV === "production",
          sameSite: "strict",
        });
      }

      return response.data;
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  },

  // Google login/signup
  googleAuth: async (googleData) => {
    const instance = createStrapiClient();
    const response = await instance.post("/auth/login", {
      email: googleData.email,
      provider: "google",
      provider_id: googleData.sub,
      googleToken: googleData.credential,
    });

    // Lưu token vào cookie nếu đăng nhập thành công
    if (response.data.jwt) {
      const cookies = new Cookies();
      cookies.set("token", response.data.jwt, {
        path: "/",
        maxAge: 7 * 24 * 60 * 60, // 7 ngày
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
      });
    }

    return response.data;
  },

  // Local signup
  signup: async (email, password) => {
    const instance = createStrapiClient();
    const response = await instance.post("/auth/signup", {
      email,
      password,
      provider: "local",
    });

    // Lưu token vào cookie nếu đăng ký thành công
    if (response.data.jwt) {
      const cookies = new Cookies();
      cookies.set("token", response.data.jwt, {
        path: "/",
        maxAge: 7 * 24 * 60 * 60, // 7 ngày
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
      });
    }

    return response.data;
  },

  // Reset password with OTP verification
  resetPassword: async (data) => {
    const instance = createStrapiClient();
    const response = await instance.post("/auth/reset-password", {
      email: data.email,
      code: data.code,
      password: data.password,
      passwordConfirmation: data.passwordConfirmation,
    });
    return response.data;
  },

  // Update user profile
  updateProfile: async (userId, userData, token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.put(`/auth/profile/${userId}`, userData);
    return response.data;
  },

  findUserByEmail: async (email) => {
    const instance = createStrapiClient();
    const response = await instance.get("/auth/find-by-email", {
      params: { email },
    });
    return response.data;
  },

  // Get current user profile
  getCurrentUser: () => {
    if (typeof window !== "undefined") {
      return JSON.parse(localStorage.getItem("user"));
    }
    return null;
  },

  // Lấy token từ cookie
  getToken: () => {
    return getTokenFromCookie();
  },

  // Kiểm tra xem đã đăng nhập chưa
  isAuthenticated: () => {
    const token = getTokenFromCookie();
    return !!token;
  },

  // Đăng xuất
  logout: (token = null) => {
    const cookies = new Cookies();
    cookies.remove("token", { path: "/" });
    cookies.remove("access_token", { path: "/" });
    cookies.remove("completedOrderInfo", { path: "/" });
    cookies.remove("hasCompletedOrder", { path: "/" });
  },

  // Cập nhật mật khẩu người dùng
  updatePassword: async (email, password, passwordConfirmation) => {
    const instance = createStrapiClient();
    const response = await instance.put("/auth/update-password", {
      email,
      password,
      passwordConfirmation,
    });
    return response.data;
  },

  getUserOrders: async (userId) => {
    const instance = createStrapiClient();
    const response = await instance.get(`/orders`, {
      params: {
        "filters[users_permissions_user]": userId,
        populate: "*",
      },
    });
    return response.data;
  },

  // Kiểm tra xem user đã có đơn hàng completed chưa
  checkUserHasCompletedOrder: async (userId, token = null) => {
    try {
      const instance = createStrapiClient(token);
      const response = await instance.get(`/orders`, {
        params: {
          "filters[users_permissions_user]": userId,
          "filters[payment_status]": "completed",
          populate: {
            course: true,
            course_tier: true,
            users_permissions_user: true,
            activation_codes: true,
          },
        },
      });

      const completedOrders = response?.data?.data || [];

      // Trả về true nếu có ít nhất một đơn hàng đã hoàn thành
      // Trả về thông tin đơn hàng đã hoàn thành gần nhất kèm theo
      if (completedOrders.length > 0) {
        // Sắp xếp theo ngày tạo giảm dần (mới nhất trước)
        completedOrders.sort(
          (a, b) =>
            new Date(b.attributes.createdAt) - new Date(a.attributes.createdAt)
        );

        return {
          hasCompletedOrder: true,
          latestOrder: completedOrders[0],
        };
      }

      return { hasCompletedOrder: false, latestOrder: null };
    } catch (error) {
      console.error("Lỗi khi kiểm tra đơn hàng đã hoàn thành:", error);
      return {
        hasCompletedOrder: false,
        latestOrder: null,
        error: error.message,
      };
    }
  },
};

const grade = {
  getGrade: async (token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/grades", {
      params: {
        populate: {
          course: {
            populate: "*",
          },
        },
      },
    });
    return response.data;
  },
  getGradeJustChapter: async (token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/grades", {
      params: {
        populate: {
          course: {
            populate: {
              chapters: true, // chỉ lấy chapters
            },
          },
        },
      },
    });
    return response.data;
  },
};

// Thêm các phương thức API khác
const courses = {
  getCourseById: async (documentId, token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get(`/courses/${documentId}`);
    return response.data;
  },

  getAllCourses: async (token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/courses");
    return response.data;
  },

  // Lấy khóa học theo slug và gradeId
  getCourseBySlug: async (slug, gradeId = null, token = null) => {
    const instance = createStrapiClient(token);

    // Xây dựng filters
    let filters = {
      slug: {
        $eq: slug,
      },
    };

    // Thêm filter theo gradeId nếu có
    if (gradeId) {
      filters.gradeId = {
        $eq: gradeId,
      };
    }

    const response = await instance.get("/courses", {
      params: {
        filters,
        populate: {
          image: true,
          course_tiers: true,
          faqs: true,
          features: true,
          class_schedules: true,
          chapters: {
            populate: ["knowledgeSections", "exercises"],
            sort: "order:asc",
          },
        },
      },
    });

    if (response.data && response.data.data && response.data.data.length > 0) {
      return response.data.data[0];
    }

    return null;
  },

  // Lấy tất cả khóa học theo gradeId
  getCoursesByGrade: async (gradeId, token = null) => {
    const instance = createStrapiClient(token);

    const response = await instance.get("/courses", {
      params: {
        filters: {
          gradeId: {
            $eq: gradeId,
          },
        },
        populate: ["image"],
        sort: "createdAt:desc",
      },
    });

    return response.data;
  },
};

const sendOTP = {
  send: async (email) => {
    const instance = createStrapiClient();
    const response = await instance.post("/send-otp", { email });
    return response.data;
  },
  verify: async (email, code) => {
    const instance = createStrapiClient();
    const response = await instance.post("/verify-otp", {
      email,
      code: code.toString(),
    });
    return response.data;
  },
};

const classSchedules = {
  getAll: async () => {
    const instance = createStrapiClient();
    const response = await instance.get("/class-schedules", {
      params: {
        populate: "*",
      },
    });
    return response.data;
  },

  getByGrade: async (grade) => {
    const instance = createStrapiClient();
    const response = await instance.get("/class-schedules", {
      params: {
        filters: {
          grade: {
            $eq: grade,
          },
        },
        populate: "*",
      },
    });
    return response.data;
  },
};

const offlineForm = {
  send: async (data) => {
    const instance = createStrapiClient();
    const response = await instance.post("/offline-forms", data);
    return response.data;
  },
};

// Video Views APIs
const videoViews = {
  // Tăng views cho video
  incrementViews: async (videoId) => {
    const instance = createStrapiClient();
    const response = await instance.post(`/video-views/${videoId}/increment`);

    // Extract data từ response
    const result = response.data?.data || response.data;

    return {
      success: result.success || true,
      views: result.views || 0,
      videoId: videoId,
    };
  },

  // Lấy views cho video
  getViews: async (videoId) => {
    const instance = createStrapiClient();
    const response = await instance.get(`/video-views/${videoId}`);
    const result = response.data?.data || response.data;

    return {
      success: result.success || true,
      views: result.views || 0,
      videoId: videoId,
    };
  },

  // Lấy views cho nhiều video cùng lúc (sử dụng batch endpoint)
  getMultipleViews: async (videoIds) => {
    try {
      const instance = createStrapiClient();

      // Gọi batch endpoint với tất cả videoIds
      const response = await instance.get("/video-views/batch", {
        params: {
          videoIds: videoIds.join(","),
        },
      });

      const result = response.data?.data || response.data;

      // Convert array thành object map để dễ sử dụng
      const viewsMap = {};

      if (result.views && Array.isArray(result.views)) {
        result.views.forEach(({ videoId, views }) => {
          viewsMap[videoId] = views || 0;
        });
      }

      // Đảm bảo tất cả videoIds đều có value, kể cả những video chưa có record
      videoIds.forEach((videoId) => {
        if (!(videoId in viewsMap)) {
          viewsMap[videoId] = 0;
        }
      });

      return viewsMap;
    } catch (error) {
      console.error("Lỗi khi lấy batch views:", error);

      const viewsMap = {};
      videoIds.forEach((videoId) => {
        viewsMap[videoId] = 0;
      });
      return viewsMap;
    }
  },
};

const videoFeature = {
  // Lấy tất cả video features
  getAllVideoFeatures: async (token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/video-features", {
      params: {
        populate: "*",
        sort: "createdAt:desc",
      },
    });
    return response.data;
  },

  // Lấy video features theo course ID
  getVideoFeaturesByCourse: async (courseId, token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/video-features", {
      params: {
        filters: {
          courses: {
            id: {
              $eq: courseId,
            },
          },
        },
        populate: "*",
        sort: "createdAt:desc",
      },
    });
    return response.data;
  },

  // Lấy video feature theo video ID
  getVideoFeatureByVideoId: async (videoId, token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/video-features", {
      params: {
        filters: {
          video_id: {
            $eq: videoId,
          },
        },
        populate: "*",
      },
    });
    return response.data;
  },
};

// Blog APIs
const blog = {
  // Lấy tất cả bài viết blog với thông tin cơ bản
  getAllPosts: async (token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/blog-posts", {
      params: {
        populate: "featuredImage",
        sort: "date:desc",
      },
    });
    return response.data;
  },

  // Lấy bài viết blog theo slug
  getPostBySlug: async (slug, token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/blog-posts", {
      params: {
        filters: {
          slug: {
            $eq: slug,
          },
        },
        populate: {
          featuredImage: true,
          sections: {
            populate: ["image"],
            sort: "order:asc",
          },
        },
      },
    });

    if (response.data && response.data.data && response.data.data.length > 0) {
      return response.data.data[0];
    }

    return null;
  },

  // Lấy bài viết blog theo category
  getPostsByCategory: async (category, token = null) => {
    const instance = createStrapiClient(token);
    const response = await instance.get("/blog-posts", {
      params: {
        filters: {
          category: {
            $eq: category,
          },
        },
        populate: "featuredImage",
        sort: "date:desc",
      },
    });
    return response.data;
  },
};

// API Payment & Orders
const payment = {
  createPaymentLink: async (paymentData) => {
    try {
      // Đảm bảo amount là số dương và tối thiểu là 2000đ
      const amount = Math.max(2000, Math.round(Number(paymentData.amount)));

      const requestData = {
        orderId: paymentData.orderId.toString(),
        amount: amount,
        description:
          paymentData.description ||
          `Thanh toán đơn hàng #${paymentData.orderId}`,
      };

      console.log("Creating payment link with data:", requestData);

      const instance = createStrapiClient();
      const response = await instance.post(
        "/payments/create-payment-link",
        requestData
      );

      console.log("Payment link response:", response.data);

      // Check cả hai trường hợp
      const paymentUrl = response.data.paymentUrl || response.data.checkoutUrl;
      if (!paymentUrl) {
        throw new Error("Missing payment URL in response");
      }

      return {
        paymentUrl: paymentUrl,
        checkoutUrl: paymentUrl, // Support cả 2 field
      };
    } catch (error) {
      console.error(
        "Error creating payment link:",
        error.response?.data || error.message
      );
      throw error;
    }
  },

  // Verify payment status
  verifyPayment: async (orderCode) => {
    const instance = createStrapiClient();
    const response = await instance.post("/payments/verify", {
      orderCode,
    });
    return response.data;
  },

  // Get payment details
  getPaymentDetails: async (orderId) => {
    const instance = createStrapiClient();
    const response = await instance.get(`/payments/details/${orderId}`);
    return response.data;
  },

  // Complete payment
  completePayment: async (orderCode) => {
    try {
      const instance = createStrapiClient();
      // Đảm bảo orderCode là string và gửi đúng định dạng
      const response = await instance.post("/payments/complete", {
        orderCode: orderCode,
      });

      return response.data;
    } catch (error) {
      console.error(
        "Hoàn tất thanh toán lỗi:",
        error.response?.data || error.message
      );
      throw error;
    }
  },
};
const convertPopulate = (url, populate) => {
  let arr = populate.split(",");
  for (let i = 0; i < arr.length; i++) {
    url = url + `&populate=` + arr[i];
  }
  return url;
};
const orders = {
  createOrder: async (orderData) => {
    try {
      const instance = createStrapiClient();

      // Chuẩn bị dữ liệu để gửi đến API
      const orderPayload = {
        data: {
          order_date: new Date().toISOString(),
          total_amount: Math.round(Number(orderData.amount)),
          payment_status: "pending",
          course: orderData.courseId,
          course_tier: orderData.courseTierId,
          users_permissions_user: orderData.userId,
          delivery_address: orderData.deliveryAddress,
        },
      };

      // Thêm payos_order_code nếu có
      if (orderData.payosOrderCode) {
        orderPayload.data.payos_order_code = orderData.payosOrderCode;
      }

      // Thêm discount_amount nếu có
      if (orderData.discountAmount) {
        orderPayload.data.discount_amount = Math.round(
          Number(orderData.discountAmount)
        );
      }

      // Thêm voucher_code nếu có
      if (orderData.voucherCode) {
        orderPayload.data.voucher_code = orderData.voucherCode;
      }

      const response = await instance.post("/orders", orderPayload);

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getOrderByUser: async (userId, populate = null) => {
    const instance = createStrapiClient();
    let url = `/orders?filters[users_permissions_user][id][$eq]=${userId}`;
    if (populate) {
      url = convertPopulate(url, populate);
    }
    const response = await instance.get(url);
    return response.data;
  },
  // Lấy chi tiết order
  getOrderById: async (orderId) => {
    try {
      const instance = createStrapiClient();
      const response = await instance.get(`/orders/${orderId}`, {
        params: {
          populate: {
            course: true,
            course_tier: true,
            users_permissions_user: true,
            activation_code: true,
          },
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Lấy order theo payos_order_code
  getOrderByOrderCode: async (orderCode) => {
    try {
      const instance = createStrapiClient();
      const response = await instance.get(`/orders`, {
        params: {
          "filters[orderCode]": orderCode,
          populate: {
            course: true,
            course_tier: true,
            users_permissions_user: true,
            activation_codes: true,
          },
        },
      });

      if (
        response.data &&
        response.data.data &&
        response.data.data.length > 0
      ) {
        return { data: response.data.data[0] };
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  // Cập nhật order
  updateOrder: async (orderId, updateData) => {
    try {
      const instance = createStrapiClient();
      const response = await instance.put(`/orders/${orderId}`, {
        data: updateData,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Thêm phương thức gửi email xác nhận
const sendPaymentConfirmation = async (emailDetails) => {
  try {
    const instance = createStrapiClient();

    // Đảm bảo thêm Content-Type header
    const headers = {
      "Content-Type": "application/json",
    };

    const response = await instance.post(
      "/send-payment-confirmation",
      emailDetails,
      { headers }
    );

    return { success: true, data: response.data };
  } catch (error) {
    console.error(
      "Lỗi gửi email xác nhận:",
      error.response?.data || error.message
    );
    console.error("Chi tiết lỗi:", error);
    return { success: false, error: error.message };
  }
};

// Testimonials APIs
const testimonials = {
  // Lấy tất cả testimonials
  getAllTestimonials: async () => {
    const instance = createStrapiClient();
    const response = await instance.get("/testimonials", {
      params: {
        populate: "*",
      },
    });
    return response.data;
  },

  // Lấy testimonials theo role (student hoặc parent)
  getTestimonialsByRole: async (role) => {
    const instance = createStrapiClient();
    const response = await instance.get("/testimonials", {
      params: {
        filters: {
          role: {
            $eq: role,
          },
        },
      },
    });
    return response.data;
  },

  // Lấy testimonial theo ID
  getTestimonialById: async (id) => {
    const instance = createStrapiClient();
    const response = await instance.get(`/testimonials/${id}`);
    return response.data;
  },
};

// Quiz Questions APIs
const quizQuestions = {
  // Lấy tất cả câu hỏi
  getAllQuizQuestions: async () => {
    const instance = createStrapiClient();
    const response = await instance.get("/quiz-questions");
    return response.data;
  },

  // Lấy câu hỏi theo lớp
  getQuizQuestionsByGrade: async (grade) => {
    const instance = createStrapiClient();

    const response = await instance.get("/quiz-questions", {
      params: {
        filters: {
          grade: {
            $eq: grade,
          },
        },
      },
    });

    // Return the data array directly since that's what the component expects
    return response.data.data;
  },

  // Lấy câu hỏi theo lớp và chủ đề
  getQuizQuestionsByGradeAndTopic: async (grade, topic) => {
    const instance = createStrapiClient();
    const response = await instance.get("/quiz-questions", {
      params: {
        filters: {
          grade: {
            $eq: grade,
          },
          topic: {
            $eq: topic,
          },
        },
        populate: "*",
      },
    });
    return response.data;
  },
};

// Thêm API voucher
const vouchers = {
  validateVoucher: async (code, courseId, email = null) => {
    try {
      const instance = createStrapiClient();
      const response = await instance.post("/vouchers/validate", {
        code,
        courseId,
        email, // Thêm email để kiểm tra voucher có áp dụng cho email này không
      });
      return response.data;
    } catch (error) {
      console.error(
        "Error validating voucher:",
        error.response?.data || error.message
      );
      throw error;
    }
  },

  incrementVoucherUses: async (code, userId = null, email = null) => {
    try {
      const instance = createStrapiClient();
      const response = await instance.post("/vouchers/increment-uses", {
        code,
        userId,
        email, // Thêm email để theo dõi email đã sử dụng voucher
      });
      return response.data;
    } catch (error) {
      console.error(
        "Error incrementing voucher uses:",
        error.response?.data || error.message
      );
      throw error;
    }
  },
};
const users = {
  getUserById: async (id) => {
    const instance = createStrapiClient();
    return await instance.get(`/users/${id}`, {
      params: {
        populate: {
          orders: {
            populate: {
              course: true,
              activation_codes: true,
              course_tier: true,
            },
          },
        },
      },
    });
  },
  updateUser: async (id, data) => {
    const instance = createStrapiClient();
    return await instance.put(`/users/${id}`, data);
  },
};
const uploads = {
  uploadFile: async (data) => {
    const instance = createStrapiClient();
    return await instance.post(`/upload`, data);
  },
};
const streak = {
  getDataByUser: async (data) => {
    try {
      const instance = createStrapiClient();
      return await instance.post(`/streaks/get-streak-by-user`, data);
    } catch (err) {
      console.log(err);
    }
  },
  getTotalRollup: async (data) => {
    try {
      const instance = createStrapiClient();
      return await instance.post(`/streaks/get-total-rollup`, data);
    } catch (err) {
      console.log(err);
    }
  },
  createStreak: async (data) => {
    try {
      const instance = createStrapiClient();
      return await instance.post(`/streaks`, { data: data });
    } catch (err) {
      console.log(err);
    }
  },
  getStreak: async (date, courseId) => {
    const instance = createStrapiClient();
    const date_ = encodeURIComponent(date);
    return await instance.get(
      `/streak-questions?filters[value][$eq]=${date_}&filters[course][id][$eq]=${courseId}`
    );
  },
  getAnswerStreakByUser: async (req) => {
    const instance = createStrapiClient();
    return await instance.get(
      `/questions-answers?filters[user][id][$eq]=${req.user}&filters[streak_question_id][$eq]=${req.streakId}`
    );
  },
  saveQuestionAnswer: async (data) => {
    const instance = createStrapiClient();
    return await instance.post(`/questions-answers`, { data: data });
  },
  finishStreak: async (data) => {
    const instance = createStrapiClient();
    return await instance.put(`/streaks/${data.documentId}`, {
      data: { isJoin: data.isJoin, time: data.time },
    });
  },
  updateTime: async (data) => {
    const instance = createStrapiClient();
    return await instance.put(`/streaks/${data.documentId}`, {
      data: { time: data.time },
    });
  },
  getDataFinish: async (data) => {
    const instance = createStrapiClient();
    return await instance.post(`/streaks/get-data-finish`, data);
  },
  getDataStreakDashboard: async (data) => {
    const instance = createStrapiClient();
    return await instance.post(`/streaks/get-data-streak-dashboard`, data);
  },

  getQuestionByStreak: async (streakId) => {
    const instance = createStrapiClient();
    return await instance.get(`/questions`, {
      params: {
        filters: {
          streak_question: { id: { $eq: streakId } },
          question_status: { $eq: "approval" },
        },
        sort: ["exercise_type.point:asc"],
        populate: {
          exercise_type: {
            fields: ["point"],
          },
        },
        fields: ["*"], // Populate tất cả các trường bao gồm image_explain_path
      },
    });
  },
  saveReportStreak: async (data) => {
    const instance = createStrapiClient();
    return await instance.post(`/reports`, { data: data });
  },
  resetForUser: async ({ userId, streakQuestionId = null }) => {
    const instance = createStrapiClient();
    // 1) Xóa answers của user (lọc theo streakQuestionId nếu có)
    const answerParams = {
      params: {
        "filters[user][id][$eq]": userId,
        ...(streakQuestionId
          ? { "filters[streak_question_id][$eq]": streakQuestionId }
          : {}),
        "pagination[pageSize]": 1000,
      },
    };
    try {
      const answersRes = await instance.get(`/questions-answers`, answerParams);
      const answers = answersRes?.data?.data || [];
      for (const item of answers) {
        const docId = item.documentId || item.id;
        if (docId) {
          await instance.delete(`/questions-answers/${docId}`);
        }
      }
    } catch (e) {
      // continue
    }

    // 2) Xóa streaks của user (lọc theo streakQuestionId nếu có)
    const streakParams = {
      params: {
        "filters[user_id][$eq]": userId,
        ...(streakQuestionId
          ? { "filters[streak_question_id][$eq]": streakQuestionId }
          : {}),
        "pagination[pageSize]": 1000,
      },
    };
    try {
      const streaksRes = await instance.get(`/streaks`, streakParams);
      const streaks = streaksRes?.data?.data || [];
      for (const item of streaks) {
        const docId = item.documentId || item.id;
        if (docId) {
          await instance.delete(`/streaks/${docId}`);
        }
      }
    } catch (e) {
      // continue
    }

    return { success: true };
  },

  validateStreakSubmission: async (data) => {
    try {
      const instance = createStrapiClient();
      const response = await instance.post(
        `/streaks/validate-streak-submission`,
        data
      );
      return response;
    } catch (err) {
      console.error("Validation API error:", err);
      // Return a proper error response structure
      return {
        data: {
          valid: false,
          reason: "VALIDATION_API_ERROR",
          message: "Không thể kết nối đến máy chủ để kiểm tra validation",
        },
      };
    }
  },
};

// Video Progress API
const videoProgress = {
  // Lấy tiến trình video của user
  getProgress: async (userId, videoId, token = null) => {
    try {
      const instance = createStrapiClient(token);
      const response = await instance.get("/video-progresses", {
        params: {
          "filters[user]": userId,
          "filters[video]": videoId,
          populate: "*",
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Lưu/cập nhật tiến trình video
  saveProgress: async (userId, videoId, progressData, token = null) => {
    try {
      const instance = createStrapiClient(token);

      // Kiểm tra xem đã có record chưa
      const existing = await instance.get("/video-progresses", {
        params: {
          "filters[user]": userId,
          "filters[video]": videoId,
        },
      });

      const data = {
        user: userId,
        video: videoId,
        lastPosition: Number(progressData.lastPosition) || 0,
        duration: Number(progressData.duration) || 0,
        isCompleted: Boolean(progressData.isCompleted),
      };

      let response;
      if (existing.data?.data?.length > 0) {
        const record = existing.data.data[0];
        const documentId = record.documentId || record.id;
        response = await instance.put(`/video-progresses/${documentId}`, {
          data,
        });
      } else {
        // Tạo record mới
        response = await instance.post("/video-progresses", {
          data,
        });
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Lấy tất cả tiến trình video của user
  getUserProgress: async (userId, token = null) => {
    try {
      const instance = createStrapiClient(token);
      const response = await instance.get("/video-progresses", {
        params: {
          "filters[user]": userId,
          populate: {
            video: true,
          },
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Đánh dấu video hoàn thành
  markCompleted: async (userId, videoId, token = null) => {
    try {
      // Lấy tiến trình hiện tại trước
      const currentProgress = await videoProgress.getProgress(
        userId,
        videoId,
        token
      );
      let lastPosition = 0;
      let duration = 0;

      if (currentProgress?.data?.length > 0) {
        const current = currentProgress.data[0].attributes;
        lastPosition = current.duration || current.lastPosition; // Set về cuối video
        duration = current.duration;
      }

      const progressData = {
        lastPosition,
        duration,
        isCompleted: true,
      };
      return await videoProgress.saveProgress(
        userId,
        videoId,
        progressData,
        token
      );
    } catch (error) {
      throw error;
    }
  },

  // Xóa tiến trình video
  deleteProgress: async (userId, videoId, token = null) => {
    try {
      const instance = createStrapiClient(token);

      // Tìm record
      const existing = await instance.get("/video-progresses", {
        params: {
          "filters[user]": userId,
          "filters[video]": videoId,
        },
      });

      if (existing.data?.data?.length > 0) {
        const record = existing.data.data[0];
        const documentId = record.documentId || record.id;
        const response = await instance.delete(
          `/video-progresses/${documentId}`
        );
        return response.data;
      }
    } catch (error) {
      throw error;
    }
  },
};

// Xuất các phương thức API
const strapiAPI = {
  auth,
  createStrapiClient,
  courses,
  sendOTP,
  offlineForm,
  streak,
  grade,
  blog,
  payment,
  orders,
  sendPaymentConfirmation,
  testimonials,
  quizQuestions,
  classSchedules,
  vouchers,
  videoFeature,
  videoViews,
  users,
  uploads,
  videoProgress,
};

export default strapiAPI;
