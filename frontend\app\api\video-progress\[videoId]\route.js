import { NextResponse } from "next/server";
import strapiAP<PERSON> from "../../strapi";
import { cookies } from "next/headers";

export async function POST(request, { params }) {
  try {
    const resolvedParams = await params;
    if (!resolvedParams || !resolvedParams.videoId) {
      return NextResponse.json(
        { error: "VideoId parameter is required" },
        { status: 400 }
      );
    }

    const { videoId } = resolvedParams;

    let body;
    try {
      body = await request.json();
    } catch (jsonError) {
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    const { userId, lastPosition, duration, isCompleted } = body || {};

    // Validate required fields
    if (!userId || !videoId) {
      return NextResponse.json(
        { error: "Missing userId or videoId" },
        { status: 400 }
      );
    }

    // Validate position data
    if (isNaN(lastPosition) || lastPosition < 0) {
      return NextResponse.json(
        { error: "Invalid lastPosition" },
        { status: 400 }
      );
    }

    // Get token from cookies only
    let authToken;
    try {
      const cookieStore = await cookies();
      authToken =
        cookieStore.get("token")?.value ||
        cookieStore.get("access_token")?.value;
    } catch (cookieError) {
      console.warn("Could not access cookies:", cookieError);
    }

    if (!authToken) {
      return NextResponse.json(
        { error: "Authentication token required" },
        { status: 401 }
      );
    }

    // Prepare progress data
    const progressData = {
      lastPosition: Number(lastPosition) || 0,
      duration: Number(duration) || 0,
      isCompleted: Boolean(isCompleted),
    };

    // Save progress using existing Strapi API with token
    const result = await strapiAPI.videoProgress.saveProgress(
      userId,
      videoId,
      progressData,
      authToken
    );

    return NextResponse.json({
      success: true,
      data: result,
      message: "Video progress saved successfully",
    });
  } catch (error) {
    console.error("Error saving video progress:", error);

    return NextResponse.json(
      {
        error: "Failed to save video progress",
        details: error.message,
      },
      { status: 500 }
    );
  }
}

export async function GET(request, { params }) {
  try {
    // Await params first
    const resolvedParams = await params;

    if (!resolvedParams || !resolvedParams.videoId) {
      return NextResponse.json(
        { error: "VideoId parameter is required" },
        { status: 400 }
      );
    }

    const { videoId } = resolvedParams;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");

    if (!userId || !videoId) {
      return NextResponse.json(
        { error: "Missing userId or videoId" },
        { status: 400 }
      );
    }

    // Get token from cookies
    let authToken;
    try {
      const cookieStore = await cookies();
      authToken =
        cookieStore.get("token")?.value ||
        cookieStore.get("access_token")?.value;
    } catch (cookieError) {
      console.warn("Could not access cookies:", cookieError);
    }

    if (!authToken) {
      return NextResponse.json(
        { error: "Authentication token required" },
        { status: 401 }
      );
    }

    const result = await strapiAPI.videoProgress.getProgress(
      userId,
      videoId,
      authToken
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("Error getting video progress:", error);

    return NextResponse.json(
      {
        error: "Failed to get video progress",
        details: error.message,
      },
      { status: 500 }
    );
  }
}
