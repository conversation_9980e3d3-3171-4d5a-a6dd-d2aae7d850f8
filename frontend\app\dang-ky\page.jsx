"use client";

import React, {useState, useContext, useEffect, Suspense} from "react";
import Image from "next/image";
import Link from "next/link";
import {GoogleLogin} from "@react-oauth/google";
import {jwtDecode} from "jwt-decode";
import {useRouter, useSearchParams} from "next/navigation";
import {useAuth} from "../../context/AuthContext";
import Button from "../../components/Button";
import TextField from "../../components/TextField";
import {UserContext} from "../../context/UserProvider";
import { isValidGmail } from "../../utils/validators";

function RegisterForm() {
    const {setVerifyEmail} = useAuth();
    const [passwordVisible, setPasswordVisible] = useState(false);
    const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
    const [error, setError] = useState("");
    const [errors, setErrors] = useState({});
    const [isAgreed, setIsAgreed] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [inputValues, setInputValues] = useState({
        email: "",
        password: "",
        confirmpassword: "",
    });
    const router = useRouter();
    const searchParams = useSearchParams();
    const {user, loginWithGoogle, register, sendOTP} = useContext(UserContext);

    useEffect(() => {
        if (user) {
            router.push("/");
        }
    }, [user, router]);

    const handleAgreementChange = (e) => {
        setIsAgreed(e.target.checked);
    };

    const handleInputChange = (e) => {
        const {name, value} = e.target;
        setInputValues((prev) => ({...prev, [name]: value}));
        let currentErrors = {...errors};

        if (name === "email") {
            if (!value) {
                currentErrors.email = "Vui lòng nhập tên đăng nhập";
            } else {
                currentErrors.email = isValidGmail(value) ? "" : "Vui lòng sử dụng địa chỉ Gmail để đăng ký";
            }
        }
        if (name === "password") {
            const passwordRegex = /^(?=.*[A-Z])(?=.*\d).{8,}$/;
            currentErrors.password =value && passwordRegex.test(value) ? "" : "Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ và số";
            if (
                inputValues.confirmpassword &&
                inputValues.confirmpassword !== value
            ) {
                currentErrors.confirmpassword = "Mật khẩu xác nhận không khớp";
            }
        }

        if (name === "confirmpassword") {
            currentErrors.confirmpassword = value === inputValues.password ? "" : "Mật khẩu xác nhận không khớp";
        }

        setErrors(currentErrors);
    };

    const handleGoogleSuccess = async (credentialResponse) => {
        try {
            setIsLoading(true);
            setError("");
            const decoded = jwtDecode(credentialResponse.credential);
            const response = await loginWithGoogle(credentialResponse);
            if (response.success) {
                if (response.needsProfileCompletion) {
                    // Đăng nhập Google thành công nhưng cần bổ sung thông tin cá nhân
                    router.push("/thong-tin-ca-nhan");
                } else {
                    router.push("/");
                }
            } else {
                setError(response.error || "Đăng nhập bằng Google thất bại");
            }
        } catch (err) {
            console.error("Google login error:", err);
            setError("Đăng nhập bằng Google thất bại");
        } finally {
            setIsLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!isAgreed) {
            setError("Vui lòng đồng ý với điều khoản và điều kiện");
            return;
        }
        // Kiểm tra lỗi form
        const hasErrors = Object.values(errors).some((error) => error);
        if (hasErrors) {
            setError("Vui lòng kiểm tra lại thông tin đăng ký");
            return;
        }

        try {
            setIsLoading(true);
            setError("");
            const email = inputValues.email;
            // Đăng ký tài khoản sử dụng phương thức từ context
            const registerResponse = await register(email, inputValues.password);

            if (registerResponse.success) {
                // Gửi mã OTP sau khi đăng ký thành công
                try {
                    const otpResponse = await sendOTP(email);
                    if (!otpResponse.success) {
                        console.error("Không thể gửi OTP:", otpResponse.error);
                    }
                    if (otpResponse.success) {
                        router.push(`/xac-thuc?from=dang-ky&email=${email}`);
                    }
                } catch (otpErr) {
                    console.error("Lỗi gửi OTP:", otpErr);
                }
            } else {
                setError(registerResponse.error || "Đăng ký thất bại");
            }
        } catch (err) {
            console.error("Registration error:", err);
            setError(err.message || "Đăng ký thất bại");
        } finally {
            setIsLoading(false);
        }
    };

    if (user) {
        return null;
    }

    return (
        <div className="md:flex md:h-screen">
            <div className="w-full bg-[#FFFFFF] flex items-center justify-center my-8">
                <div className="max-w-[360px] w-full max-[390px]:px-4">
                    <div className="flex justify-center mb-3">
                        <Image
                            src="/images/Logo.png"
                            alt="Logo"
                            width={120}
                            height={48}
                            className="h-12 w-auto"
                            onClick={() => router.push("/")}
                        />
                    </div>
                    <div className="text-center">
                        <h2 className="text-[30px] leading-[38px] font-semibold text-[#181D27]">
                            Đăng ký
                        </h2>
                        <p className="mt-3 font-normal text-base text-[#535862]">
                            Tạo tài khoản để có thể đăng ký khoá học
                        </p>
                    </div>
                    <div className="mt-8">
                        <div style={{display: "flex", justifyContent: "center"}}>
                            <GoogleLogin
                                onSuccess={handleGoogleSuccess}
                                onError={() => {
                                    setError("Đăng nhập Google thất bại");
                                }}
                                useOneTap
                                type="standard"
                                theme="outline"
                                size="large"
                                text="continue_with"
                                shape="rectangular"
                                style={{width: "100%"}}
                                disabled={isLoading}
                            />
                        </div>
                    </div>
                    <div className="my-6 flex items-center justify-center text-[#535862]">
                        <span className="mx-2 text-sm">hoặc</span>
                    </div>
                    <form className="mt-6" onSubmit={handleSubmit}>
                        {error && <div className="text-[#D92D20] text-center">{error}</div>}
                        <div>
                            <TextField
                                id="email"
                                name="email"
                                type="email"
                                label="Email"
                                required
                                value={inputValues.email}
                                onChange={handleInputChange}
                                placeholder="Nhập Email của bạn"
                                error={errors.email}
                                helperText={errors.email}
                                disabled={isLoading}
                            />
                        </div>
                        <div className="mt-4">
                            <TextField
                                id="password"
                                name="password"
                                type={passwordVisible ? "text" : "password"}
                                label="Mật khẩu"
                                required
                                value={inputValues.password}
                                onChange={handleInputChange}
                                placeholder="Nhập mật khẩu"
                                error={errors.password}
                                helperText={errors.password}
                                disabled={isLoading}
                            />
                        </div>
                        <div className="mt-4">
                            <TextField
                                id="confirmpassword"
                                name="confirmpassword"
                                type={confirmPasswordVisible ? "text" : "password"}
                                label="Xác nhận mật khẩu"
                                required
                                value={inputValues.confirmpassword}
                                onChange={handleInputChange}
                                placeholder="Nhập lại mật khẩu"
                                error={errors.confirmpassword}
                                helperText={errors.confirmpassword}
                                disabled={isLoading}
                            />
                        </div>
                        <div className="mt-6 flex items-center">
                            <input
                                id="agreewith"
                                name="agreewith"
                                type="checkbox"
                                checked={isAgreed}
                                onChange={handleAgreementChange}
                                className="h-4 w-4 border border-[#D5D7DA] rounded bg-white focus:outline-none checked:bg-[#299D55] checked:border-[#299D55] accent-[#299D55]"
                                disabled={isLoading}
                            />
                            <label
                                htmlFor="agreewith"
                                className="ml-2 block text-sm font-medium text-[#414651]"
                            >
                                Tôi đồng ý với{" "}
                                <Link href="#" className="text-[#299D55]">
                                    Điều khoản
                                </Link>
                            </label>
                        </div>
                        <div className="mt-6">
                            <Button
                                type="submit"
                                variant="primary"
                                disabled={!isAgreed || isLoading}
                                className="w-full"
                            >
                                {isLoading ? "Đang xử lý..." : "Đăng ký"}
                            </Button>
                        </div>
                    </form>
                    <div className="mt-8 text-center">
                        <p className="text-sm text-[#535862] font-normal">
                            Bạn đã có tài khoản?{" "}
                            <Link href="/dang-nhap" className="text-[#198C43] font-semibold">
                                Đăng nhập ngay
                            </Link>
                        </p>
                    </div>
                    <div className="mt-8 text-center">
                        <Link href="/" className="text-sm font-normal text-[#535862]">
                            ← Quay lại trang chủ
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default function RegisterPage() {
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <RegisterForm/>
        </Suspense>
    );
}
