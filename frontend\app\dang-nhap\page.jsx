"use client";

import React, {
  useState,
  useContext,
  useEffect,
  useCallback,
  Suspense,
} from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { GoogleLogin } from "@react-oauth/google";
import { jwtDecode } from "jwt-decode";
import { UserContext } from "../../context/UserProvider";
import TextField from "../../components/TextField";
import { strapi } from "../../app/api/strapi";

function LoginForm() {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/";
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const { user, login, loginWithGoogle } = useContext(UserContext);

  // Helper function to handle redirect after successful login
  const handleLoginRedirect = useCallback((result) => {
    router.refresh(); // Refresh để cập nhật state từ cookie

    // LOGIC MỚI: Ưu tiên callbackUrl nếu có, bất kể user đã mua khóa học hay chưa
    // Validate callbackUrl để tránh open redirect vulnerability
    const isSafeCallbackUrl = callbackUrl && callbackUrl.startsWith('/') && !callbackUrl.startsWith('//');

    if (isSafeCallbackUrl && callbackUrl !== '/') {
      // Nếu có callbackUrl cụ thể và an toàn, redirect về đó
      router.push(callbackUrl);
    } else if (result.hasCompletedOrder) {
      // Nếu không có callbackUrl và user đã mua khóa học, redirect về /quan-ly
      router.push("/quan-ly");
    } else {
      // Nếu không có callbackUrl và user chưa mua khóa học, về trang chủ
      router.push("/");
    }
  }, [router, callbackUrl]);

  const handleLogin = useCallback(
    async (e) => {
      if (e) e.preventDefault();

      try {
        setError("");
        setLoading(true);

        const result = await login(email, password, rememberMe);

        if (result.success) {
          // Kiểm tra nếu cần hoàn tất thông tin cá nhân
          if (result.needsProfileCompletion) {
            router.push("/thong-tin-ca-nhan");
          } else {
            handleLoginRedirect(result);
          }
        } else {
          setError(result.error || "Đăng nhập thất bại");
        }
      } catch (error) {
        console.error("Lỗi khi đăng nhập:", error);
        setError("Đăng nhập thất bại. Vui lòng thử lại sau.");
      } finally {
        setLoading(false);
      }
    },
    [login, email, password, rememberMe, router, callbackUrl]
  );

  const handleGoogleSuccess = async (credentialResponse) => {
    setError("");
    setLoading(true);

    try {
      const decoded = jwtDecode(credentialResponse.credential);

      // Sử dụng loginWithGoogle từ context thay vì fetch trực tiếp
      const result = await loginWithGoogle({
        credential: credentialResponse.credential,
        email: decoded.email,
        sub: decoded.sub,
      });

      if (result.success) {
        // Kiểm tra nếu cần hoàn tất thông tin cá nhân
        if (result.needsProfileCompletion) {
          router.push("/thong-tin-ca-nhan");
        } else {
          handleLoginRedirect(result);
        }
      } else {
        setError(
          result.error || "Đăng nhập với Google thất bại. Vui lòng thử lại."
        );
      }
    } catch (err) {
      console.error("Google login error:", err);
      setError("Đăng nhập với Google thất bại. Vui lòng thử lại sau.");
    } finally {
      setLoading(false);
    }
  };

  const onClickForgotPassword = useCallback(() => {
    const callbackUrl = searchParams.get("callbackUrl");
    let forgotPasswordUrl = "/quen-mat-khau";
    if (callbackUrl) {
      forgotPasswordUrl += `?redirectUrl=${encodeURIComponent(callbackUrl)}`;
    }
    router.push(forgotPasswordUrl);
  }, [router, searchParams]);

  const onClickRegister = useCallback(() => {
    const callbackUrl = searchParams.get("callbackUrl");
    let registerUrl = "/dang-ky";
    if (callbackUrl) {
      registerUrl += `?callbackUrl=${encodeURIComponent(callbackUrl)}`;
    }
    router.push(registerUrl);
  }, [router, searchParams]);

  // Nếu đang chuyển hướng, trả về null hoặc trang loading
  if (user) {
    return null; // hoặc có thể return <div>Đang chuyển hướng...</div>;
  }

  return (
    <div className="md:flex h-screen ">
      <div className="w-full bg-[#FFFFFF] flex items-center justify-center my-8">
        <div className="max-w-[360px] w-full max-[390px]:px-4">
          <div className="flex justify-center mb-6">
            <Image
              src="/images/Logo.png"
              alt="Logo"
              width={120}
              height={48}
              className="h-12 w-auto"
              onClick={() => router.push("/")}
            />
          </div>
          <div className="text-center">
            <h2 className="text-[30px] leading-[38px] font-semibold text-[#181D27]">
              Đăng Nhập
            </h2>
            <p className="mt-2 font-normal text-base text-[#535862]">
              Vui lòng nhập thông tin để đăng nhập!
            </p>
          </div>

          <div className="mt-8">
            <div style={{ display: "flex", justifyContent: "center" }}>
              <GoogleLogin
                onSuccess={handleGoogleSuccess}
                onError={() => {
                  setError("Đăng nhập với Google thất bại");
                }}
                useOneTap
                type="standard"
                theme="outline"
                size="large"
                text="continue_with"
                shape="rectangular"
                style={{ width: "100%" }}
              />
            </div>
          </div>
          <div className="my-6 flex items-center justify-center text-[#535862]">
            <span className="mx-2 text-sm">hoặc</span>
          </div>
          <form className="mt-6" onSubmit={handleLogin}>
            {error && (
              <div className="mb-6 bg-[#FEF3F2] text-sm text-[#414651] font-normal flex gap-4 p-4 rounded-xl">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                >
                  <g clipPath="url(#clip0_3814_10675)">
                    <path
                      d="M10.0001 6.66666V9.99999M10.0001 13.3333H10.0084M18.3334 9.99999C18.3334 14.6024 14.6025 18.3333 10.0001 18.3333C5.39771 18.3333 1.66675 14.6024 1.66675 9.99999C1.66675 5.39762 5.39771 1.66666 10.0001 1.66666C14.6025 1.66666 18.3334 5.39762 18.3334 9.99999Z"
                      stroke="#D92D20"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_3814_10675">
                      <rect width="20" height="20" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                {error}
              </div>
            )}
            <div>
              <TextField
                id="email"
                name="email"
                type="email"
                label="Tên đăng nhập"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Nhập Email của bạn"
                error={error ? true : false}
              />
            </div>

            <div className="mt-4">
              <TextField
                id="password"
                name="password"
                type={passwordVisible ? "text" : "password"}
                label="Mật khẩu"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Nhập mật khẩu"
                error={error ? true : false}
              />
            </div>

            {/* Remember Me and Forgot Password */}
            <div className="mt-6 flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-primary border-gray-300 rounded cursor-pointer"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                />
                <label
                  htmlFor="remember-me"
                  className="ml-2 block text-sm text-gray-900 cursor-pointer"
                >
                  Nhớ mật khẩu
                </label>
              </div>

              <a
                className="text-sm font-medium text-primary hover:text-primary-dark cursor-pointer"
                onClick={onClickForgotPassword}
              >
                Quên mật khẩu?
              </a>
            </div>

            {/* Login Button */}
            <div className="mt-6">
              <button
                type="submit"
                disabled={!email || !password || loading}
                className={`w-full flex justify-center py-[10px] px-4 rounded-lg text-base font-semibold ${
                  !email || !password || loading
                    ? "bg-[#F5F5F5] text-[#A4A7AE] cursor-not-allowed"
                    : "bg-[#299D55] text-[#FFF] hover:bg-[#198C43]"
                }`}
              >
                {loading ? "Đang xử lý..." : "Đăng nhập"}
              </button>
            </div>
          </form>
          {/* Register Link */}
          <div className="mt-8 text-center">
            <p className="text-sm text-[#535862] font-normal">
              Bạn chưa có tài khoản?{" "}
              <Link
                onClick={onClickRegister}
                href="/dang-ky"
                className="text-sm text-[#198C43] font-semibold"
              >
                Đăng ký ngay
              </Link>
            </p>
          </div>
          <div className="mt-8 text-center">
            <Link
              href="/"
              className="text-sm font-normal text-[#535862] text-center"
            >
              ← Quay lại trang chủ
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginForm />
    </Suspense>
  );
}
