"use client";

import DashboardLayout from "../../components/layouts/DashboardLayout";
import CountdownWidget from "../../components/dashboard/CountdownWidget";
import {useDashboardLayout} from "@/context/DashboardLayoutContext";
import React, {useEffect, useContext, useState} from "react";
import {useRouter} from "next/navigation";
import Image from "next/image";
import strapi from "@/app/api/strapi";
import {UserContext} from "@/context/UserProvider";
import VideoIcon from "@/components/icons/VideoIcon";

const targetDate = new Date("2025-07-01");
export default function Dashboard() {
    const {setTitle, keySearch, setKeySearch, setIsSearch, setIsDetail, setIsTurnLive} = useDashboardLayout();
    const router = useRouter();
    const { getUser } = useContext(UserContext);
    const [isResetting, setIsResetting] = useState(false);

    useEffect(() => {
        setTitle("Trang chủ");
        setIsSearch(false);
        setIsDetail(false);
        setIsTurnLive(false);
        setKeySearch(keySearch);
        return () => {
            setIsSearch(false);
            setIsTurnLive(false);
        }
    }, []);
    return (
        <div className="flex flex-col items-center justify-center h-full min-h-[calc(100vh-160px)]">
            <div className="bg-white p-8 max-w-md w-full flex flex-col justify-center items-center gap-6">
                {/* Mascot Image */}
                <div className="flex justify-center">
                    <Image
                        src="https://img.ongbadayhoa.com/ong-ba-mascot-he-he.png"
                        alt="Ông Ba Mascot"
                        width={280}
                        height={280}
                        className="w-48 h-48 xs:w-56 xs:h-56 sm:w-64 sm:h-64 md:w-[280px] md:h-[280px] object-contain"
                        priority
                    />
                </div>
                
                {/* Buttons Container */}
                <div className="flex flex-col w-full gap-3">
                    {/* Tham gia Streak Button */}
                    <button
                        disabled
                        className="w-full py-3 rounded-lg bg-[#F5F5F5] text-[#A4A7AE] font-semibold cursor-not-allowed"
                    >
                        Streak đang sửa chữa
                    </button>
                    
                    {/* Xem lại video Button */}
                    <button
                        onClick={() => router.push('/quan-ly/xem-video')}
                        className="w-full py-3 px-4 rounded-lg border border-primary-default bg-secondary-color-default hover:bg-secondary-color-hover text-secondary-color-default font-semibold transition-colors duration-200 flex items-center justify-center gap-2"
                    >
                        <VideoIcon width={20} height={20} />
                        <span>Xem lại video</span>
                    </button>
                </div>
            </div>
        </div>
    );
}
