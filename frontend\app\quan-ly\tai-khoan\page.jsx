"use client";

import DashboardLayout from "../../../components/layouts/DashboardLayout";
import AvatarDropdown from "@/components/dashboard/AvatarDropdown";
import React, {useContext, useEffect, useState} from "react";
import {useRouter} from "next/navigation";
import ProfileModal from "@/components/dashboard/ProfileModal";
import InvoiceDetailsModal from "@/components/dashboard/InvoiceDetailsModal";
import PasswordChangeModal from "@/components/dashboard/PasswordChangeModal";
import {UserContext} from "@/context/UserProvider";
import {useDashboardLayout} from "@/context/DashboardLayoutContext";
import ConfirmDialog from "@/components/ui/ConfirmDialog";

export default function QuanLyTaiKhoan() {
    const [showProfileModal, setShowProfileModal] = useState(false);
    const [showInvoiceModal, setShowInvoiceModal] = useState(false);
    const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
    const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
    const { user, logout, logoutLoading } = useContext(UserContext);

    const {setTitle, keySearch, setKeySearch, setIsSearch, setIsDetail, setIsTurnLive} = useDashboardLayout();

    useEffect(() => {
        setTitle("Tài khoản");
        setIsSearch(true);
        setIsDetail(false);
        setIsTurnLive(false);
        setKeySearch(keySearch);
        return () => {
            setIsSearch(false);
            setIsTurnLive(false);
        }
    }, []);

    const handleCloseProfileModal = () => {
        setShowProfileModal(false);
    };
    const handleCloseInvoiceModal = () => {
        setShowInvoiceModal(false);
    }
    const handleClosePasswordModal = () => {
        setShowChangePasswordModal(false);
    };
    const handleLogout = () => {
        setShowLogoutConfirm(true);
    };

    const handleConfirmLogout = async () => {
        await logout(false);
    };

    const handleCancelLogout = () => {
        setShowLogoutConfirm(false);
    };
    return (
        // <DashboardLayout title="Tài khoản">
        //
        // </DashboardLayout>

        <div className="gap-y-[8px] flex flex-col w-full">
            <button type="button" className="item_ca_nhan flex flex-row gap-lg py-lg px-[16px] w-full text-left hover:bg-[#FAFAFA] active:bg-[#F0F0F0] transition-colors touch-manipulation tap-highlight-transparent" onClick={() => setShowProfileModal(true)}>
                <div className="icon items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12.0018 15C8.83173 15 6.0126 16.5306 4.2178 18.906C3.83151 19.4172 3.63836 19.6728 3.64468 20.0183C3.64956 20.2852 3.81716 20.6219 4.02717 20.7867C4.29899 21 4.67567 21 5.42904 21H18.5746C19.3279 21 19.7046 21 19.9764 20.7867C20.1864 20.6219 20.354 20.2852 20.3589 20.0183C20.3652 19.6728 20.1721 19.4172 19.7858 18.906C17.991 16.5306 15.1719 15 12.0018 15Z" stroke="#717680" strokeWidth="2" strokeLinejoin="round"/>
                        <path d="M12.0018 12C14.4871 12 16.5018 9.98528 16.5018 7.5C16.5018 5.01472 14.4871 3 12.0018 3C9.51652 3 7.5018 5.01472 7.5018 7.5C7.5018 9.98528 9.51652 12 12.0018 12Z" stroke="#717680" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </div>
                <div className="icon items-center text-left">
                    <p className="text-secondary-700 text-md leading-md font-semibold">Thông tin cá nhân</p>
                </div>
            </button>
            <button type="button" className="item_xem_hoa_don flex flex-row gap-lg py-lg px-[16px] w-full text-left hover:bg-[#FAFAFA] active:bg-[#F0F0F0] transition-colors touch-manipulation tap-highlight-transparent" onClick={() => setShowInvoiceModal(true)}>
                <div className="icon items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M22.7 11.5L20.7005 13.5L18.7 11.5M20.9451 13C20.9814 12.6717 21 12.338 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C14.8273 21 17.35 19.6963 19 17.6573M12 7V12L15 14" stroke="#717680" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </div>
                <div className="icon items-center text-left">
                    <p className="text-secondary-700 text-md leading-md font-semibold">Xem hoá đơn</p>
                </div>
            </button>
            <button type="button" className="item_doi_mk flex flex-row gap-lg py-lg px-[16px] w-full text-left hover:bg-[#FAFAFA] active:bg-[#F0F0F0] transition-colors touch-manipulation tap-highlight-transparent" onClick={() => setShowChangePasswordModal(true)}>
                <div className="icon items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M17 10V8C17 5.23858 14.7614 3 12 3C9.23858 3 7 5.23858 7 8V10M12 14.5V16.5M8.8 21H15.2C16.8802 21 17.7202 21 18.362 20.673C18.9265 20.3854 19.3854 19.9265 19.673 19.362C20 18.7202 20 17.8802 20 16.2V14.8C20 13.1198 20 12.2798 19.673 11.638C19.3854 11.0735 18.9265 10.6146 18.362 10.327C17.7202 10 16.8802 10 15.2 10H8.8C7.11984 10 6.27976 10 5.63803 10.327C5.07354 10.6146 4.6146 11.0735 4.32698 11.638C4 12.2798 4 13.1198 4 14.8V16.2C4 17.8802 4 18.7202 4.32698 19.362C4.6146 19.9265 5.07354 20.3854 5.63803 20.673C6.27976 21 7.11984 21 8.8 21Z" stroke="#717680" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </div>
                <div className="icon items-center text-left">
                    <p className="text-secondary-700 text-md leading-md font-semibold">Đổi mật khẩu</p>
                </div>
            </button>
            <button type="button" className="item_dang_xuat flex flex-row gap-lg py-lg px-[16px] w-full text-left hover:bg-[#FAFAFA] active:bg-[#F0F0F0] transition-colors touch-manipulation tap-highlight-transparent" onClick={handleLogout}>
                <div className="icon items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M15 3H16.2C17.8802 3 18.7202 3 19.362 3.32698C19.9265 3.6146 20.3854 4.07354 20.673 4.63803C21 5.27976 21 6.11985 21 7.8V16.2C21 17.8802 21 18.7202 20.673 19.362C20.3854 19.9265 19.9265 20.3854 19.362 20.673C18.7202 21 17.8802 21 16.2 21H15M10 7L15 12M15 12L10 17M15 12L3 12" stroke="#717680" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </div>
                <div className="icon items-center text-left">
                    <p className="text-secondary-700 text-md leading-md font-semibold">Đăng xuất</p>
                </div>
            </button>

            <ProfileModal isOpen={showProfileModal} onClose={handleCloseProfileModal}/>
            <InvoiceDetailsModal isOpen={showInvoiceModal} onClose={handleCloseInvoiceModal}></InvoiceDetailsModal>
            <PasswordChangeModal isOpen={showChangePasswordModal} onClose={handleClosePasswordModal}/>
            
            {/* Logout Confirmation Dialog */}
            <ConfirmDialog
                isOpen={showLogoutConfirm}
                onClose={handleCancelLogout}
                onConfirm={handleConfirmLogout}
                title="Xác nhận đăng xuất"
                message="Bạn có chắc chắn muốn đăng xuất khỏi tài khoản không?"
                confirmText="Đăng xuất"
                cancelText="Hủy"
                type="warning"
                loading={logoutLoading}
            />
        </div>

    );
}
