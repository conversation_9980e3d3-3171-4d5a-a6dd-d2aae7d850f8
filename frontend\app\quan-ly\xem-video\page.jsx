"use client";

import React, { useEffect, useRef, useState } from "react";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import { FaPlay } from "react-icons/fa";
import { useRouter } from "next/navigation";
import { useDashboardLayout } from "@/context/DashboardLayoutContext";

export default function ViewVideo() {
  const router = useRouter();
  const [videos, setListVideos] = useState([]);
  const [videoOriginal, setListVideoOriginal] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [videoViews, setVideoViews] = useState({});
  const timeoutRef = useRef(null);
  const {
    setTitle,
    keySearch,
    setKeySearch,
    setIsSearch,
    setIsDetail,
    setIsTurnLive,
  } = useDashboardLayout();
  const [courseId, setCourseId] = useState(null);

  // Helper function để format số lượt xem
  const formatViews = (count) => {
    if (!count || count === 0) return "0";
    if (count < 1000) return count.toString();
    if (count < 1000000)
      return `${(count / 1000)
        .toFixed(1)
        .replace(".0", "")
        .replace(".", ",")} N`;
    if (count < 1000000000)
      return `${(count / 1000000)
        .toFixed(1)
        .replace(".0", "")
        .replace(".", ",")} Tr`;
    return `${(count / 1000000000)
      .toFixed(1)
      .replace(".0", "")
      .replace(".", ",")} T`;
  };

  // Helper function để format thời gian
  const formatTimeAgo = (dateString) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return "Vừa xong";
    if (diffInSeconds < 3600)
      return `${Math.floor(diffInSeconds / 60)} phút trước`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
    if (diffInSeconds < 2592000)
      return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
    if (diffInSeconds < 31536000)
      return `${Math.floor(diffInSeconds / 2592000)} tháng trước`;
    return `${Math.floor(diffInSeconds / 31536000)} năm trước`;
  };

  useEffect(() => {
    setTitle("Xem video");
    setIsSearch(true);
    setIsDetail(false);
    setIsTurnLive(false);
    setKeySearch(keySearch);
    return () => {
      setIsSearch(false);
      setIsTurnLive(false);
    };
  }, []);

  useEffect(() => {
    const cookies = new Cookies();
    const user_data = cookies.get("user_data");
    const getListVideo = async () => {
      try {
        if (!user_data || !user_data.id) {
          setIsLoading(false);
          return;
        }

        const res = await strapi.users.getUserById(user_data.id);

        if (
          res &&
          res.data &&
          res.data.orders &&
          res.data.orders.length > 0 &&
          res.data.orders[0].course &&
          res.data.orders[0].course.id
        ) {
          const courseId = res.data.orders[0].course.id;
          setCourseId(courseId);

          const videoRes = await strapi.videoFeature.getVideoFeaturesByCourse(
            courseId
          );

          if (videoRes.data && videoRes.data.length > 0) {
            // Thêm course_id vào mỗi video
            const videosWithCourseId = videoRes.data.map((video) => ({
              ...video,
              course_id: courseId,
            }));

            setListVideos(videosWithCourseId);
            setListVideoOriginal(videosWithCourseId);

            // Lấy views từ video-views API
            try {
              const videoIds = videosWithCourseId.map(video => video.video_id);
              const viewsMap = await strapi.videoViews.getMultipleViews(videoIds);
        
              setVideoViews(viewsMap);
            } catch (error) {
              
              // Nếu lỗi, set views = 0 cho tất cả
              const viewsMap = {};
              videosWithCourseId.forEach((video) => {
                viewsMap[video.video_id] = 0;
              });
              setVideoViews(viewsMap);
            }
          } else {
            setListVideos([]);
            setListVideoOriginal([]);
          }
        } else {
          setListVideos([]);
          setListVideoOriginal([]);
        }
      } catch (error) {
      } finally {
        setIsLoading(false);
      }
    };
    getListVideo();
  }, []);

  const onClickVideo = (video) => {
    router.push(
      window.location.href +
        "/chi-tiet?videoId=" +
        video.video_id +
        "&courseId=" +
        video.course_id
    );
  };

  useEffect(() => {
    if (!videoOriginal || videoOriginal.length === 0) return;
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      if (keySearch !== "") {
        const keySearch_ = keySearch.trim().toLowerCase();
        const matchedVideos = videoOriginal.filter(
          (video) =>
            video.title.toLowerCase().includes(keySearch_) ||
            video.description.toLowerCase().includes(keySearch_)
        );
        setListVideos(matchedVideos);
      } else {
        setListVideos(videoOriginal);
      }
    }, 300);
    return () => clearTimeout(timeoutRef.current);
  }, [keySearch, videoOriginal]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-160px)]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full min-h-[calc(100vh-160px)]">
      <div className="bg-white w-full sm:gap-4xl flex flex-col xs:gap-y-3xl">
        {/* Video đầu tiên */}
        {videos.length > 0 && (
          <div
            className="flex flex-col xs:gap-lg sm:flex-row sm:gap-xl cursor-pointer"
            onClick={() => onClickVideo(videos[0])}
          >
            <div className="relative w-full md:w-1/2 rounded-md">
              <img
                src={videos[0].thumbnail}
                alt="Video Thumbnail"
                className="w-full h-full object-cover rounded-md aspect-[296.5/185.31]"
              />
              <div className="absolute inset-0 flex flex-col justify-center items-center bg-black/30">
                <button className="text-white/65 text-4xl">
                  <FaPlay className="color-fa-play" />
                </button>
              </div>
              <div className="absolute top-2 left-2 py-xs px-lg rounded-full border bg-utility-brand-50 border-utility-brand-200">
                <p className="text-utility-brand-700 text-sm font-medium">
                  Mới nhất
                </p>
              </div>
            </div>
            <div className="flex flex-col gap-md w-full md:w-1/2">
              <p className="text-primary-900 font-semibold xs:text-sm xs:leading-sm sm:text-md sm:leading-md md:text-lg md:leading-lg">
                {videos[0].title}
              </p>
              {/* Hiển thị views và thời gian cho video trên mobile tablet */}
              <div className="md:hidden flex items-center gap-2 text-tertiary-600 text-sm font-normal">
                <span>
                  {formatViews(videoViews[videos[0].video_id])} lượt xem
                </span>
                <span>•</span>
                <span>{formatTimeAgo(videos[0].updatedAt)}</span>
              </div>

              <p className="text-tertiary-600 xs:text-sm font-normal xs:hidden sm:block md:text-md md:leading-md">
                {videos[0].description}
              </p>

              {/* Hiển thị views và thời gian trên desktop */}
              <div className="xs:hidden md:block flex items-center gap-2 text-tertiary-600 text-sm font-normal">
                <span>
                  {formatViews(videoViews[videos[0].video_id])} lượt xem
                </span>
                <span> • </span>
                <span>{formatTimeAgo(videos[0].updatedAt)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Các video còn lại */}
        <div className="grid cursor-pointer xs:gap-x-xl xs:gap-y-3xl xs:grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 sm:gap-y-4xl">
          {videos.slice(1).map((video, index) => (
            <div
              key={index}
              onClick={() => onClickVideo(video)}
              className="rounded-md overflow-hidde"
            >
              <div className="relative w-full mb-lg">
                <img
                  src={video.thumbnail}
                  alt="Video Thumbnail"
                  className="w-full h-full object-cover rounded-md aspect-[8/5]"
                />
                <div className="absolute inset-0 flex flex-col justify-center items-center bg-black/30">
                  <button className="text-white/65 text-4xl">
                    <FaPlay className="color-fa-play" />
                  </button>
                </div>
              </div>
              <div className="pl-xs">
                <p className="text-primary-900 font-semibold line-clamp-2 xs:text-sm xs:leading-sm xs:mb-xs">
                  {video.title}
                </p>
                {/* Hiển thị views và thời gian cho các video khác */}
                <div className="flex items-center gap-2 text-tertiary-600 font-normal xs:text-sm xs:leading-sm">
                  <span>
                    {formatViews(videoViews[video.video_id])} lượt xem
                  </span>
                  <span>•</span>
                  <span>{formatTimeAgo(video.updatedAt)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {videos.length === 0 && !isLoading && (
          <div className="flex flex-col items-center justify-center py-10">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="96"
              height="120"
              viewBox="0 0 96 120"
              fill="none"
            >
              {/* SVG path data */}
            </svg>
            <p className="text-lg text-gray-500 mt-4">
              Không tìm thấy video nào
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
