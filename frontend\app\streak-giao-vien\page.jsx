'use client';

import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/navigation';
import { UserContext } from '@/context/UserProvider';
import StreakTeacherCalendar from '@/components/streak-teacher/StreakTeacherCalendar';
import QuestionForm from '@/components/streak-teacher/QuestionForm';
import TeacherCodeVerification from '@/components/streak-teacher/TeacherCodeVerification';
import toast from 'react-hot-toast';
import Image from 'next/image';

export default function StreakGiaoVienPage() {
  const { user, isAuthenticated } = useContext(UserContext);
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedGrade, setSelectedGrade] = useState(10);
  const [showQuestionForm, setShowQuestionForm] = useState(false);
  const [existingQuestions, setExistingQuestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isTeacherVerified, setIsTeacherVerified] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Kiểm tra xác thực khi component mount
  useEffect(() => {
    const checkAuthentication = () => {
      // Kiểm tra đăng nhập
      if (!isAuthenticated) {
        setIsCheckingAuth(false);
        return;
      }

      // Kiểm tra xác thực giáo viên
      const teacherVerified = localStorage.getItem('teacher_verified');
      const verifyTime = localStorage.getItem('teacher_verified_time');
      
      if (teacherVerified === 'true' && verifyTime) {
        // Kiểm tra thời gian xác thực (hết hạn sau 24 giờ)
        const timeElapsed = Date.now() - parseInt(verifyTime);
        const twentyFourHours = 24 * 60 * 60 * 1000;
        
        if (timeElapsed < twentyFourHours) {
          setIsTeacherVerified(true);
        } else {
          // Xóa xác thực hết hạn
          localStorage.removeItem('teacher_verified');
          localStorage.removeItem('teacher_verified_time');
          setIsTeacherVerified(false);
        }
      }
      
      setIsCheckingAuth(false);
    };

    checkAuthentication();
  }, [isAuthenticated]);

  const handleDateSelect = async (date) => {
    setSelectedDate(date);
    setIsLoading(true);
    
    try {
      // Gọi API để lấy câu hỏi theo ngày và lớp
      const response = await fetch(`/api/strapi-proxy/streak-teacher/questions/${date}/${selectedGrade}`);
      const data = await response.json();
      
      if (data.exists) {
        setExistingQuestions(data.questions);
      } else {
        setExistingQuestions([]);
      }
      
      setShowQuestionForm(true);
    } catch (error) {
      console.error('Error fetching questions:', error);
      toast.error('Có lỗi khi tải câu hỏi');
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuestionsSubmit = async (questionsData) => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/strapi-proxy/streak-teacher/questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date: selectedDate,
          grade: selectedGrade,
          questions: questionsData
        })
      });
      
      if (response.ok) {
        toast.success(existingQuestions.length > 0 ? 'Cập nhật câu hỏi thành công!' : 'Tạo câu hỏi thành công!');
        setShowQuestionForm(false);
        setSelectedDate(null);
        setExistingQuestions([]);
      } else {
        throw new Error('Failed to create questions');
      }
    } catch (error) {
      console.error('Error creating questions:', error);
      toast.error('Có lỗi khi tạo câu hỏi');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTeacherVerifySuccess = () => {
    setIsTeacherVerified(true);
  };

  const handleTeacherVerifyCancel = () => {
    router.push('/');
  };

  const handleLogout = () => {
    // Xóa xác thực giáo viên
    localStorage.removeItem('teacher_verified');
    localStorage.removeItem('teacher_verified_time');
    setIsTeacherVerified(false);
    router.push('/');
  };

  // Loading state
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-utility-brand-50">
        <div className="text-center">
          <div className="w-[60px] h-[60px] rounded-full bg-utility-brand-50 flex items-center justify-center mx-auto mb-6xl">
            <div className="w-8xl h-8xl border-4 border-primary-default border-t-transparent rounded-full animate-spin"></div>
          </div>
          <h2 className="text-lg font-semibold text-primary-900 mb-md">Đang kiểm tra quyền truy cập</h2>
          <p className="text-md text-secondary-700">Vui lòng chờ trong giây lát...</p>
        </div>
      </div>
    );
  }

  // Yêu cầu đăng nhập
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-utility-brand-50">
        <div className="bg-white rounded-2xl shadow-xs p-8xl max-w-md w-full mx-lg">
          <div className="text-center">
            {/* Logo */}
            <div className="flex justify-center mb-6xl">
              <Image
                src="/images/Logo.png"
                alt="Logo"
                width={120}
                height={48}
                className="h-12 w-auto"
              />
            </div>
            
            {/* Icon */}
            <div className="w-[80px] h-[80px] rounded-full bg-utility-brand-50 flex items-center justify-center mx-auto mb-6xl">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                <path d="M18 8C18 6.4087 17.3679 4.88258 16.2426 3.75736C15.1174 2.63214 13.5913 2 12 2C10.4087 2 8.88258 2.63214 7.75736 3.75736C6.63214 4.88258 6 6.4087 6 8C6 9.5913 6.63214 11.1174 7.75736 12.2426C8.88258 13.3679 10.4087 14 12 14C13.5913 14 15.1174 13.3679 16.2426 12.2426C17.3679 11.1174 18 9.5913 18 8Z" stroke="#299D55" strokeWidth="2"/>
                <path d="M19 21C19 17.134 16.2091 14 12.5 14C8.79086 14 6 17.134 6 21" stroke="#299D55" strokeWidth="2"/>
              </svg>
            </div>
            
            <h1 className="text-display-xs font-semibold text-primary-900 mb-md">
              Vui lòng đăng nhập
            </h1>
            <p className="text-md text-secondary-700 mb-8xl">
              Bạn cần đăng nhập để truy cập trang quản lý câu hỏi streak dành cho giáo viên
            </p>
            
            <button
              onClick={() => router.push('/dang-nhap?callbackUrl=/streak-giao-vien')}
              className="w-full bg-primary-default text-primary-default py-lg px-xl rounded-lg font-semibold hover:bg-primary-hover transition-colors duration-200"
            >
              Đăng nhập ngay
            </button>
            
            <p className="text-sm text-secondary-700 mt-6xl">
              Chỉ dành cho giáo viên được ủy quyền
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Yêu cầu xác thực mã giáo viên
  if (!isTeacherVerified) {
    return (
      <TeacherCodeVerification
        onVerifySuccess={handleTeacherVerifySuccess}
        onCancel={handleTeacherVerifyCancel}
      />
    );
  }

  // Main content - không có header/footer
  return (
    <div className="min-h-screen bg-utility-brand-50">
      {/* Header riêng cho trang giáo viên */}
      <header className="bg-white border-b border-secondary shadow-xs">
        <div className="max-w-7xl mx-auto px-md sm:px-lg py-lg sm:py-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-md sm:space-x-xl">
              {/* Logo */}
              <div className="flex items-center space-x-md sm:space-x-lg">
                <Image
                  src="/images/Logo.png"
                  alt="Logo"
                  width={40}
                  height={16}
                  className="h-3 sm:h-4 w-auto"
                />
                <div className="w-px h-4 sm:h-6 bg-secondary"></div>
              </div>
              
              {/* Icon và title */}
              <div className="flex items-center space-x-md sm:space-x-lg">
                <div className="w-[40px] h-[40px] sm:w-[48px] sm:h-[48px] rounded-full bg-utility-brand-50 flex items-center justify-center">
                  <svg width="20" height="20" className="sm:w-6 sm:h-6" viewBox="0 0 24 24" fill="none">
                    <path d="M12 14C16.4183 14 20 10.4183 20 6C20 1.58172 16.4183 -2 12 -2C7.58172 -2 4 1.58172 4 6C4 10.4183 7.58172 14 12 14Z" stroke="#299D55" strokeWidth="2"/>
                    <path d="M2 21C2 17.134 6.47715 14 12 14C17.5228 14 22 17.134 22 21" stroke="#299D55" strokeWidth="2"/>
                  </svg>
                </div>
                <div className="hidden sm:block">
                  <h1 className="text-md sm:text-lg font-semibold text-primary-900">
                    Quản lý câu hỏi Streak
                  </h1>
                  <p className="text-xs sm:text-sm text-secondary-700">
                    Dành cho giáo viên
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-sm sm:space-x-lg">
              <div className="text-right hidden lg:block">
                <p className="text-sm font-medium text-primary-900">
                  {user?.fullname || user?.email}
                </p>
                <p className="text-xs text-secondary-700">Giáo viên</p>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center space-x-sm px-md sm:px-lg py-sm sm:py-md rounded-lg text-sm font-medium text-secondary-700 hover:text-error-primary-600 hover:bg-utility-gray-100 transition-all duration-200"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M16 17L21 12L16 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M21 12H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="hidden sm:inline">Đăng xuất</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="max-w-7xl mx-auto px-md sm:px-lg py-lg sm:py-8xl">
        {!showQuestionForm ? (
          <div className="space-y-lg sm:space-y-6xl">
            {/* Page title và description */}
            <div className="bg-white rounded-xl sm:rounded-2xl shadow-xs p-lg sm:p-6xl">
              <div className="flex flex-col gap-lg sm:gap-6xl lg:flex-row lg:items-center lg:justify-between">
                <div className="flex-1">
                  <h2 className="text-lg sm:text-display-xs font-semibold text-primary-900 mb-sm sm:mb-md">
                    Lịch quản lý câu hỏi
                  </h2>
                  <p className="text-sm sm:text-md text-secondary-700">
                    Tạo và quản lý câu hỏi streak hàng ngày cho học sinh
                  </p>
                </div>
                
                {/* Grade selector */}
                <div className="flex flex-col lg:items-end">
                  <label className="block text-sm font-medium text-primary-900 mb-sm sm:mb-md">
                    Chọn lớp học:
                  </label>
                  <select
                    value={selectedGrade}
                    onChange={(e) => setSelectedGrade(parseInt(e.target.value))}
                    className="w-full sm:min-w-[140px] border border-secondary-gray-default rounded-lg px-md sm:px-xl py-md sm:py-lg text-sm sm:text-md focus:border-primary-default focus:outline-none focus:ring-2 focus:ring-primary-default focus:ring-opacity-20 transition-all duration-200"
                  >
                    <option value={10}>Lớp 10</option>
                    <option value={11}>Lớp 11</option>
                    <option value={12}>Lớp 12</option>
                  </select>
                </div>
              </div>
            </div>
            
            {/* Calendar */}
            <StreakTeacherCalendar
              selectedGrade={selectedGrade}
              onDateSelect={handleDateSelect}
              isLoading={isLoading}
            />
          </div>
        ) : (
          <div className="bg-white rounded-xl sm:rounded-2xl shadow-xs">
            <QuestionForm
              selectedDate={selectedDate}
              selectedGrade={selectedGrade}
              existingQuestions={existingQuestions}
              onSubmit={handleQuestionsSubmit}
              onCancel={() => {
                setShowQuestionForm(false);
                setSelectedDate(null);
                setExistingQuestions([]);
              }}
              isLoading={isLoading}
            />
          </div>
        )}
      </main>
    </div>
  );
}