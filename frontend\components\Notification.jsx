import React, { useEffect } from "react";
import SuccessIcon from "./icons/SuccessIcon";
import ErrorIcon from "./icons/ErrorIcon";
import WarningIcon from "./icons/WarningIcon";
import InfoIcon from "./icons/InfoIcon";

const ICONS = {
  success: <SuccessIcon width={24} height={24} />,
  error: <ErrorIcon width={24} height={24} />,
  warning: <WarningIcon width={24} height={24} />,
  info: <InfoIcon width={24} height={24} />,
};

const COLORS = {
  success: {
    bg: "bg-noti-success",
    bgOpacity: "bg-opacity-10",
    text: "text-primary-900",
    iconColor: "text-noti-success",
  },
  error: {
    bg: "bg-noti-error",
    bgOpacity: "bg-opacity-10",
    text: "text-noti-error",
    iconColor: "text-noti-error",
  },
  warning: {
    bg: "bg-noti-warning",
    bgOpacity: "bg-opacity-10",
    text: "text-noti-warning",
    iconColor: "text-noti-warning",
  },
  info: {
    bg: "bg-noti-info",
    bgOpacity: "bg-opacity-10",
    text: "text-noti-info",
    iconColor: "text-noti-info",
  },
};

const Notification = ({
  title,
  message,
  type = "success",
  duration,
  onClose,
  onAction,
  actionText,
}) => {
  useEffect(() => {
    if (duration) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  const colors = COLORS[type];

  return (
    <div className="fixed inset-0 flex items-center justify-center"
      style={{zIndex: 999}}
    >
      <div
        className="fixed inset-0 bg-[#000000] bg-opacity-50 backdrop-blur-[4px] flex items-center justify-center"
        onClick={onClose}
      ></div>
      <div className="bg-white rounded-lg p-6 relative z-10 max-w-[400px] w-full mx-4">
        <div className="flex items-center justify-center mb-4">
          <div
            className={`w-12 h-12 rounded-full ${colors.bg} ${colors.bgOpacity} flex items-center justify-center ${colors.iconColor}`}
          >
            {ICONS[type]}
          </div>
        </div>
        <h3
          className={`text-center text-lg font-semibold mb-1 ${colors.text} `}
        >
          {title}
        </h3>
        <p className="text-center text-secondary-700 text-sm font-normal mb-8">
          {message}
        </p>
        {actionText && onAction && (
          <div className="flex justify-center mt-4">
            <button
              onClick={() => {
                onAction();
                onClose();
              }}
              className={`w-full px-xl py-[10px] rounded-md ${colors.bg} text-white hover:opacity-90 transition-opacity`}
            >
              {actionText}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Notification;
