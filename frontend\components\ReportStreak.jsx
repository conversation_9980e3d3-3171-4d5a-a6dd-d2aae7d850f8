"use client";

import React, {useContext, useEffect, useRef, useState} from "react";
import clsx from "clsx";
import LineDivider from "@/components/icons/LineDivider";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import {CommonUtil} from "@/utils/CommonUtil";
import {useScreenSize} from "@/hooks/useScreenSize";
import TextError from "@/components/TextError";
import {UserContext} from "@/context/UserProvider";

/**
 * Popup hóa đơn mua khóa học
 * @param isOpen
 * @param onClose
 * @constructor
 */
const ReportStreak = ({isOpen, onClose, onSendData, data}) => {
    if (!isOpen) return;
    const [isUse, setIsUse] = useState(false);
    const {getUser} = useContext(UserContext);
    const screenSize = useScreenSize();
    const [formData, setFormData] = useState({reason: "" , description: ""});
    const [errors, setErrors] = useState({});
    const [reasons, setReasons] = useState([
        {checked: false, name: "Câu hỏi có vấn đề"},
        {checked: false, name: "Thiếu hình ảnh"},
        {checked: false, name: "Sai hình ảnh"},
        {checked: false, name: "Hình ảnh có vấn đề về mặt chất lượng"},
        {checked: false, name: "Đáp án có vấn đề"},
        {checked: false, name: "Trùng một hoặc nhiều đáp án"},
        {checked: false, name: "Đã xảy ra lỗi khác"},
    ]);

    const handleSend = () => {
        onSendData?.(true);
        onClose?.();
    };
    const handleFocusInDescription = () => {
        // const rs = reasons.filter((value, index) => value.name !== 'Đã xảy ra lỗi khác');
        // rs.push({checked: true, name: "Đã xảy ra lỗi khác"});
        // setReasons(rs);
    }
    const handleDescription = (e) => {
        const { name, value } = e.target;
        const a = {}
        if (value && value.length > 200) {
           a.description = "Lố số kí tự rồi kìa";
        }
        setErrors(a);
        const d = {...formData,description: value};
        setFormData(d);
        // debugger
        // const rs = reasons.filter((value, index) => value.name !== 'Đã xảy ra lỗi khác');
        // setReasons([...rs,{checked: true, name: "Đã xảy ra lỗi khác"}]);
    }
    const clickInput = (e) => {
        const { name, checked } = e.target;
        let index = name.split("_")[1];
        reasons[Number(index)].checked = checked;
    }
    const sendReportStreak = async () => {
        const  a = reasons.filter((value, index) => value.name === 'Đã xảy ra lỗi khác');
        if (a[0].checked && !formData.description) {
           setErrors({description: "Bắt buộc nhập nếu lý do khác"})
            return;
        }

        const user = getUser();
        //gọi api để lưu report
        const d = reasons.filter((value) => value.checked);
        const reason = d.map((value) => value.name).join(", ");
        setFormData({...formData,reason: reason, question: data?.id});
        const res = await strapi.streak.saveReportStreak({reason: reason, description: formData.description, question: data?.id,user: user.id});
        if (res?.data?.data) {
            onSendData?.(true);
            onClose?.();
        }else {
            onSendData?.(false);
            onClose?.();
        }
    }
    return (
        <div className="fixed inset-0  flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
             style={{zIndex: 120,backdropFilter: 'blur(8px)',WebkitBackdropFilter: 'blur(8px)', }}
            tabIndex={-1}
            aria-modal="true"
            role="dialog">
            <div className="bg-[#FFFFFF] rounded-2xl shadow-xl p-3xl w-[490px] relative max-h-screen overflow-y-auto gap-y-3xl flex flex-col">
                <div className="header_ text-center w-full">
                    <p className="text-lg leading-lg text-primary-900 font-semibold">Câu này đang gặp lỗi gì nhỉ?</p>
                </div>
                <div className="content_ gap-y-xl w-full flex flex-col">
                    {
                        reasons.map((value, index) => {
                            return (
                                <label className="flex items-center gap-2 cursor-pointer">
                                    <input
                                        type="checkbox"
                                        className="peer hidden"
                                        name={`input_` + index}
                                        onClick={clickInput}
                                        // checked={value.checked}
                                    />
                                    <div className="w-[16px] h-[16px] rounded border border-primary bg-white
                                          peer-checked:bg-brand-solid peer-checked:border-brand-solid
                                          flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
                                            <path d="M10.5 3L5 8.5L2.5 6" stroke="white" strokeWidth="1.6666" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <p className="text-sm leading-sm font-medium text-secondary-700">{value.name}</p>
                                </label>

                            )
                        })
                    }
                    <div className={clsx(
                        "description_ items-center w-full h-[120px] rounded-md border border-primary  flex flex-col gap-sm",
                        errors.description? 'border-error': 'focus-within:border-brand'
                    )}>
                        <textarea
                            name="description"
                            onInput={handleDescription}
                            onFocus={handleFocusInDescription}
                            className="w-full h-full py-lg px-[14px] text-md  rounded-md leading-md text-placeholder font-normal outline-none resize-none"
                            placeholder="Tao sai chỗ nào zị tụ bây?"
                        ></textarea>
                        <p className="w-full flex justify-end text-sm leading-sm text-quaternary-500 font-normal">{formData.description.length}/200</p>
                    </div>
                    <TextError error={errors.description}></TextError>
                </div>
                <div className={clsx("footer_ flex flex-row gap-lg",screenSize?.lte960 ? "justify-center": "justify-end ")}>
                    <button className={clsx(
                        "rounded-md gap-md text-center px-xl py-[10px] border border-secondary",
                        screenSize?.lte960 ? "w-1/2": "w-[67px]"
                    )} onClick={onClose}>
                        <p className=" text-md leading-md text-button-secondary-fg font-semibold">Hủy</p>
                    </button>
                    {
                        Object.keys(errors).length !== 0 ?
                            <button className={clsx(
                                "rounded-md gap-md text-center px-xl py-[10px] border border-secondary",
                                screenSize?.lte960 ? "w-1/2": "w-[130px] "
                            )}>
                                <p className="text-md leading-md text-[#A4A7AE] font-semibold">Gửi báo cáo</p>
                            </button>
                            :
                            <button className={clsx(
                                "rounded-md gap-md text-center px-xl py-[10px] border border-secondary bg-brand-solid",
                                screenSize?.lte960 ? "w-1/2": "w-[130px] "
                                )} onClick={sendReportStreak}>
                                <p className="text-md leading-md text-white font-semibold">Gửi báo cáo</p>
                            </button>
                    }

                </div>
            </div>
        </div>
    );
};

export default ReportStreak;
