"use client";

import React, {useEffect, useRef, useState} from "react";
import clsx from "clsx";
import LineDivider from "@/components/icons/LineDivider";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import {CommonUtil} from "@/utils/CommonUtil";
import {useScreenSize} from "@/hooks/useScreenSize";

/**
 * Popup hóa đơn mua khóa học
 * @param isOpen
 * @param onClose
 * @constructor
 */
const StarPointPopup = ({isOpen, onClose, onSendData}) => {
    if (!isOpen) return;
    const [isUse, setIsUse] = useState(false);
    const screenSize = useScreenSize();
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === "Escape") onClose();
        };
        window.addEventListener("keydown", handleKeyDown);
        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [isOpen, onClose]);
    const handleSend = () => {
        onSendData?.(true);
        onClose?.();
    };
    return (
        <div className="fixed inset-0  flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
             style={{zIndex: 120,backdropFilter: 'blur(8px)',WebkitBackdropFilter: 'blur(8px)', }}
            tabIndex={-1}
            aria-modal="true"
            role="dialog">
            <div className="bg-[#FFFFFF] rounded-2xl px-3xl pt-3xl shadow-xl w-full max-w-[400px] relative max-h-screen overflow-y-auto">
                <div className="header_model mb-xl">
                    <div className="w-full h-full relative flex justify-center">
                        <div className="w-[48px] h-[48px] p-[12px] flex justify-center items-center rounded-full bg-[#DA251D]">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M11.2827 3.45258C11.5131 2.98564 11.6284 2.75218 11.7848 2.67758C11.9209 2.61268 12.0791 2.61268 12.2152 2.67758C12.3717 2.75218 12.4869 2.98564 12.7174 3.45258L14.9041 7.88255C14.9721 8.0204 15.0061 8.08933 15.0558 8.14284C15.0999 8.19023 15.1527 8.22862 15.2113 8.25589C15.2776 8.28669 15.3536 8.29781 15.5057 8.32004L20.397 9.03497C20.9121 9.11026 21.1696 9.1479 21.2888 9.2737C21.3925 9.38316 21.4412 9.53356 21.4215 9.68304C21.3988 9.85485 21.2124 10.0364 20.8395 10.3996L17.3014 13.8457C17.1912 13.9531 17.136 14.0068 17.1004 14.0707C17.0689 14.1273 17.0487 14.1895 17.0409 14.2538C17.0321 14.3264 17.0451 14.4023 17.0711 14.554L17.906 19.4214C17.994 19.9348 18.038 20.1914 17.9553 20.3438C17.8833 20.4763 17.7554 20.5693 17.6071 20.5967C17.4366 20.6283 17.2061 20.5071 16.7451 20.2647L12.3724 17.9651C12.2361 17.8935 12.168 17.8576 12.0962 17.8436C12.0327 17.8311 11.9673 17.8311 11.9038 17.8436C11.832 17.8576 11.7639 17.8935 11.6277 17.9651L7.25492 20.2647C6.79392 20.5071 6.56341 20.6283 6.39297 20.5967C6.24468 20.5693 6.11672 20.4763 6.04474 20.3438C5.962 20.1914 6.00603 19.9348 6.09407 19.4214L6.92889 14.554C6.95491 14.4023 6.96793 14.3264 6.95912 14.2538C6.95132 14.1895 6.93111 14.1273 6.89961 14.0707C6.86402 14.0068 6.80888 13.9531 6.69859 13.8457L3.16056 10.3996C2.78766 10.0364 2.60121 9.85485 2.57853 9.68304C2.55879 9.53356 2.60755 9.38316 2.71125 9.2737C2.83044 9.1479 3.08797 9.11026 3.60304 9.03497L8.49431 8.32004C8.64642 8.29781 8.72248 8.28669 8.78872 8.25589C8.84736 8.22862 8.90016 8.19023 8.94419 8.14284C8.99391 8.08933 9.02793 8.0204 9.09597 7.88255L11.2827 3.45258Z" fill="#FFF600" stroke="#FFF600" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </div>
                        <div className="button_close absolute right-0 top-0 cursor-pointer" onClick={onClose}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="#A4A7AE" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div className="content_ flex flex-col text-center w-full">
                    <p className=" text-primary-900 text-lg leading-lg font-semibold w-full">
                        Mại dzô, đặt sao hy vọng đi nè!
                    </p>
                    <p className="text-tertiary-600 text-sm leading-sm font-normal">
                        Sao hy vọng giúp bạn x2 số điểm của câu hỏi này và bạn chỉ được sử dụng 1 LẦN duy nhất trong streak hôm nay thôi nhé
                    </p>
                </div>
                <div className={clsx(
                    "footer_  gap-lg flex flex-row justify-center",
                    screenSize?.lte960 ? "px-none py-3xl" : "p-3xl"
                )}>
                    <button onClick={onClose} className={clsx(
                        "px-xl py-[10px]  rounded-md border border-secondary",
                        screenSize?.lte960? "w-1/2":"w-[170px]"
                    )}>
                        <p className="text-[#414651] text-md leading-md font-semibold "> Hủy bỏ</p>
                    </button>
                    <button onClick={handleSend} className={clsx(
                        "px-xl py-[10px]  bg-[#299D55] rounded-md",
                        screenSize?.lte960 ? "w-1/2":"w-[170px] "
                    )}>
                        <p className="text-white text-md leading-md font-semibold">Sử dụng</p>
                    </button>
                </div>
            </div>
        </div>
    );
};

export default StarPointPopup;
