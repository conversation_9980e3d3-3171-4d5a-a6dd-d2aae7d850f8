"use client";

import React, { useRef, useEffect } from "react";
import "@vidstack/react/player/styles/default/theme.css";
import "@vidstack/react/player/styles/default/layouts/video.css";

import { MediaPlayer, MediaProvider, Poster } from "@vidstack/react";
import {
  DefaultVideoLayout,
  defaultLayoutIcons,
} from "@vidstack/react/player/layouts/default";
import strapiAPI from "../app/api/strapi";

const VideoPlayer = ({
  src,
  poster,
  autoplay = false,
  muted = false,
  title = "",
  userId,
  videoId,
  onPlay, // Thêm prop onPlay
}) => {
  const playerRef = useRef(null);

  useEffect(() => {
    const fetchProgress = async () => {
      if (!userId || !videoId) return;

      try {
        const response = await strapiAPI.videoProgress.getProgress(
          userId,
          videoId
        );

        if (response?.data?.length > 0) {
          const progressRecord = response.data[0];

          const savedTime =
            progressRecord.attributes?.lastPosition ||
            progressRecord.lastPosition;

          if (playerRef.current && savedTime && savedTime > 0) {
            // Đợi video load xong trước khi set time
            setTimeout(() => {
              if (playerRef.current && playerRef.current.duration) {
                playerRef.current.currentTime = savedTime;
              }
            }, 1000);
          }
        } else {
        }
      } catch (err) {}
    };

    fetchProgress();
  }, [userId, videoId]);

  // 🚀 Hàm lưu tiến trình vào Strapi
  const saveProgress = async () => {
    if (!playerRef.current || !userId || !videoId) return;

    const currentTime = playerRef.current.currentTime;
    const duration = playerRef.current.duration || 0;

    // Kiểm tra nếu currentTime không hợp lệ
    if (isNaN(currentTime) || currentTime < 0 || currentTime === 0) return;

    const isCompleted = duration > 0 && currentTime >= duration - 5; // Coi như hoàn thành nếu còn lại 5s

    try {
      await strapiAPI.videoProgress.saveProgress(userId, videoId, {
        lastPosition: currentTime,
        duration,
        isCompleted,
      });
    } catch (err) {}
  };

  // 🚀 Hàm lưu tiến trình bằng sendBeacon (cho beforeunload)
  const saveProgressBeacon = (time) => {
    if (!userId || !videoId || isNaN(time) || time <= 0) return;

    const duration = playerRef.current?.duration || 0;
    const isCompleted = duration > 0 && time >= duration - 5;

    navigator.sendBeacon(
      `/api/video-progress/${videoId}`,
      JSON.stringify({
        userId,
        lastPosition: time,
        duration,
        isCompleted,
      })
    );
  };

  // 🚀 Handle beforeunload event
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (playerRef.current && playerRef.current.currentTime > 0) {
        saveProgressBeacon(playerRef.current.currentTime);
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [videoId, userId]);

  useEffect(() => {
    return () => {
      // Lưu tiến trình cuối cùng khi thoát (cho route navigation)
      if (playerRef.current && userId && videoId) {
        const currentTime = playerRef.current.currentTime;
        const duration = playerRef.current.duration || 0;

        if (!isNaN(currentTime) && currentTime > 0) {
          // Thử sendBeacon trước (nhanh hơn và reliable cho page unload)
          saveProgressBeacon(currentTime);

          // Backup với async call cho route navigation
          const finalSave = async () => {
            try {
              await strapiAPI.videoProgress.saveProgress(userId, videoId, {
                lastPosition: currentTime,
                duration,
                isCompleted: duration > 0 && currentTime >= duration - 5,
              });
            } catch (err) {}
          };

          finalSave();
        }
      }
    };
  }, [userId, videoId]);

  return (
    <>
      <MediaPlayer
        ref={playerRef}
        src={src}
        viewType="video"
        streamType="on-demand"
        crossOrigin
        playsInline
        title={title}
        poster={poster}
        autoPlay={autoplay}
        muted={muted}
        onPause={saveProgress}
        onEnded={saveProgress}
        onPlay={() => {
          console.log("Video player onPlay event triggered");
          // Gọi callback onPlay nếu có
          if (onPlay) {
            console.log("Calling onPlay callback");
            onPlay();
          }
        }}
        onTimeUpdate={() => {}}
      >
        <MediaProvider>
          <Poster className="vds-poster" />
        </MediaProvider>
        <DefaultVideoLayout
          icons={defaultLayoutIcons}
          translations={{
            Play: "Phát",
            Pause: "Tạm dừng",
            Replay: "Phát lại",
            Mute: "Tắt tiếng",
            Unmute: "Bật tiếng",
            Volume: "Âm lượng",
            "Enter Fullscreen": "Toàn màn hình",
            "Exit Fullscreen": "Thoát toàn màn hình",
            Settings: "Cài đặt",
            Fullscreen: "Toàn màn hình",
            Audio: "Âm thanh",
            Playback: "Tốc độ phát",
            Accessibility: "Truy cập",
            Loop: "Lặp lại",
            Boost: "Tăng cường âm lượng",
            Speed: "Tốc độ",
            Normal: "Bình thường",
            "Enter PiP": "Chế độ PiP",
            "Exit PiP": "Thoát PiP",
            Announcements: "Thông báo",
            "Keyboard Animations": "Bàn phím hoạt hình",
          }}
        />
      </MediaPlayer>

      <style jsx global>{`
        .vds-slider-track-fill {
          background: #00d4aa !important;
        }

        .vds-button:hover {
          background: #00d4aa !important;
        }

        .vds-volume-slider .vds-slider-track-fill {
          background: #00d4aa !important;
        }

        .vds-controls {
          background: transparent;
        }
      `}</style>
    </>
  );
};

export default VideoPlayer;
