import React from 'react';
import { CommonUtil } from "@/utils/CommonUtil";

const InfoIcon = ({ width = 24, height = 24 }) => {
    width = Number(width) || 24;
    height = Number(height) || 24;

    let strokeWidth = CommonUtil.getStrokeWidth({ width: width, height: height });
    let viewBox = `0 0 ${width} ${height}`;

    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={width}
            height={height}
            viewBox={viewBox}
            fill="none"
            className="transition-colors duration-200"
        >
            <path
                d="M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                stroke="currentColor"
                strokeWidth={strokeWidth}
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
};

export default InfoIcon;