import React from 'react';
import {CommonUtil} from "@/utils/CommonUtil";

const PointIcon = ( {width = 24, height = 24,...props}) => {
    let viewBox = `0 0 ${width} ${height}`;
    let strokeWidth = CommonUtil.getStrokeWidth({width: width,height: height});
    return (
        // <svg
        //     xmlns="http://www.w3.org/2000/svg"
        //     width={width}
        //     height={height}
        //     viewBox={viewBox}
        //     fill="none"
        //     {...props}
        // >
        //     <path
        //         fillRule="evenodd"
        //         clipRule="evenodd"
        //         d="M18.369 2.00183C19.3628 1.97414 20.343 2.22004 21.0614 2.93836C21.7798 3.65675 22.0256 4.63693 21.9979 5.63074C21.9703 6.62171 21.6724 7.72225 21.1893 8.84949C20.7588 9.85415 20.1627 10.9182 19.4266 11.9999C20.1627 13.0815 20.7588 14.1456 21.1893 15.1503C21.6724 16.2775 21.9703 17.3781 21.9979 18.369C22.0256 19.3629 21.7798 20.343 21.0614 21.0614C20.343 21.7798 19.3629 22.0256 18.369 21.9979C17.3781 21.9703 16.2775 21.6724 15.1503 21.1893C14.1456 20.7588 13.0815 20.1627 11.9999 19.4266C10.9182 20.1627 9.85415 20.7588 8.84949 21.1893C7.72225 21.6724 6.62171 21.9703 5.63074 21.9979C4.63693 22.0256 3.65675 21.7798 2.93836 21.0614C2.22004 20.343 1.97414 19.3628 2.00183 18.369C2.02951 17.3781 2.32734 16.2775 2.81043 15.1503C3.24094 14.1458 3.83619 13.0814 4.57215 11.9999C3.83622 10.9184 3.24092 9.85397 2.81043 8.84949C2.32736 7.72229 2.02949 6.62169 2.00183 5.63074C1.97416 4.63694 2.21997 3.65674 2.93836 2.93836C3.65674 2.21997 4.63694 1.97416 5.63074 2.00183C6.62169 2.02949 7.72229 2.32736 8.84949 2.81043C9.85397 3.24092 10.9184 3.83622 11.9999 4.57215C13.0814 3.83619 14.1458 3.24094 15.1503 2.81043C16.2775 2.32734 17.3781 2.02951 18.369 2.00183ZM5.84265 13.7069C5.35056 14.4844 4.9503 15.236 4.64929 15.9384C4.22538 16.9275 4.01913 17.7707 4.00086 18.4247C3.9827 19.0756 4.14916 19.442 4.3534 19.6464C4.55768 19.8506 4.92406 20.017 5.57508 19.9989C6.2291 19.9807 7.07223 19.7744 8.0614 19.3505C8.76367 19.0495 9.51443 18.6482 10.2919 18.1561C9.50317 17.5189 8.7166 16.8143 7.95105 16.0487C7.18516 15.2828 6.48015 14.496 5.84265 13.7069ZM18.1561 13.7069C17.5187 14.4959 16.8145 15.2829 16.0487 16.0487C15.2829 16.8145 14.4959 17.5187 13.7069 18.1561C14.4846 18.6483 15.2359 19.0494 15.9384 19.3505C16.9275 19.7744 17.7707 19.9806 18.4247 19.9989C19.0758 20.0171 19.4421 19.8507 19.6464 19.6464C19.8507 19.4421 20.0171 19.0758 19.9989 18.4247C19.9806 17.7707 19.7744 16.9275 19.3505 15.9384C19.0494 15.2359 18.6483 14.4846 18.1561 13.7069ZM5.57508 4.00086C4.92406 3.98273 4.55768 4.14911 4.3534 4.3534C4.14911 4.55768 3.98273 4.92406 4.00086 5.57508C4.01911 6.22908 4.2254 7.07227 4.64929 8.0614C4.95019 8.7635 5.35079 9.51463 5.84265 10.2919C6.48006 9.50296 7.18531 8.71679 7.95105 7.95105C8.71679 7.18531 9.50296 6.48006 10.2919 5.84265C9.51463 5.35079 8.7635 4.95019 8.0614 4.64929C7.07227 4.2254 6.22908 4.01911 5.57508 4.00086ZM18.4247 4.00086C17.7707 4.01913 16.9275 4.22538 15.9384 4.64929C15.236 4.9503 14.4844 5.35056 13.7069 5.84265C14.496 6.48015 15.2828 7.18516 16.0487 7.95105C16.8143 8.7166 17.5189 9.50317 18.1561 10.2919C18.6482 9.51443 19.0495 8.76367 19.3505 8.0614C19.7744 7.07223 19.9807 6.2291 19.9989 5.57508C20.017 4.92406 19.8506 4.55768 19.6464 4.3534C19.442 4.14916 19.0756 3.9827 18.4247 4.00086Z"
        //         fill="#7A5AF8"
        //     />
        //     <circle cx="12" cy="12" r="2" fill="#EBE9FE" />
        // </svg>

        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path fillRule="evenodd" clipRule="evenodd" d="M10.8453 1.30416C11.5598 0.898612 12.4402 0.898612 13.1547 1.30416L20.8453 5.66852C21.5598 6.07407 22 6.82398 22 7.63509L22 16.3649C22 17.176 21.5598 17.9259 20.8453 18.3315L13.1547 22.6958C12.4402 23.1014 11.5598 23.1014 10.8453 22.6958L3.15471 18.3315C2.4402 17.9259 2.00003 17.176 2 16.3649L2 7.63509C2 6.82398 2.44017 6.07407 3.15471 5.66852L10.8453 1.30416ZM12.3394 5.43462C12.2362 5.10688 11.7638 5.10689 11.6606 5.43462L11.0167 7.48654C10.4846 9.18256 9.13456 10.5108 7.40934 11.0339L5.32206 11.6658C4.98817 11.767 4.9883 12.2318 5.32206 12.3331L7.40934 12.9661C9.13451 13.4892 10.4847 14.8175 11.0167 16.5135L11.6606 18.5643C11.7636 18.8926 12.2364 18.8926 12.3394 18.5643L12.9833 16.5135C13.5153 14.8175 14.8655 13.4892 16.5907 12.9661L18.6779 12.3331C19.0117 12.2318 19.0118 11.767 18.6779 11.6658L16.5907 11.0339C14.8654 10.5108 13.5154 9.18256 12.9833 7.48654L12.3394 5.43462Z" fill="#7A5AF8"/>
        </svg>
    );
};

export default PointIcon;
