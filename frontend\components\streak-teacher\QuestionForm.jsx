'use client';

import { useState, useEffect } from 'react';
import { PhotoIcon, XMarkIcon, EyeIcon } from '@heroicons/react/24/outline';
import KaTeXRenderer from '@/components/katex-renderer';
import { getToken } from '@/utils/cookieHelper';

export default function QuestionForm({ existingQuestions = [], onSubmit, onCancel, isLoading, selectedDate }) {
  const [questions, setQuestions] = useState([
    {
      content: '',
      type: 'TN_4',
      A: '',
      B: '',
      C: '',
      D: '',
      correctAnswer: '',
      correctAnswerType: 'A',
      explain: '',
      imagePath: '',
      imageExplainPath: '',
      imageFile: null,
      imageExplainFile: null,
      questionType: 'hiểu-biết' // hiểu-biết cho câu 1, 2 và vận dụng cho câu 3
    },
    {
      content: '',
      type: 'TN_4',
      A: '',
      B: '',
      C: '',
      D: '',
      correctAnswer: '',
      correctAnswerType: 'A',
      explain: '',
      imagePath: '',
      imageExplainPath: '',
      imageFile: null,
      imageExplainFile: null,
      questionType: 'hiểu-biết'
    },
    {
      content: '',
      type: 'TN_4',
      A: '',
      B: '',
      C: '',
      D: '',
      correctAnswer: '',
      correctAnswerType: 'A',
      explain: '',
      imagePath: '',
      imageExplainPath: '',
      imageFile: null,
      imageExplainFile: null,
      questionType: 'vận dụng'
    }
  ]);

  const [errors, setErrors] = useState({});
  const [uploading, setUploading] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  
  // Check if user can edit questions (for today and future dates)
  const canEdit = () => {
    if (!selectedDate) return false;
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    const selectedDateObj = new Date(selectedDate);
    selectedDateObj.setHours(0, 0, 0, 0); // Reset time to start of day
    
    // Can edit questions for today and future dates (A and A+n)
    return selectedDateObj >= today;
  };
  
  const isReadOnly = !canEdit();

  // Load existing questions if available
  useEffect(() => {
    if (existingQuestions.length > 0) {
      const loadedQuestions = existingQuestions.map((eq, index) => ({
        content: eq.content || '',
        type: eq.type || 'TN_4',
        A: eq.A || '',
        B: eq.B || '',
        C: eq.C || '',
        D: eq.D || '',
        correctAnswer: eq.correct_answer || '',
        correctAnswerType: eq.correct_answer_type || 'A',
        explain: eq.explain || '',
        imagePath: eq.image_path || '',
        imageExplainPath: eq.image_explain_path || '',
        imageFile: null,
        imageExplainFile: null,
        questionType: index < 2 ? 'hiểu-biết' : 'vận dụng'
      }));
      
      setQuestions(loadedQuestions);
    }
  }, [existingQuestions]);

  // Handle form field changes
  const handleQuestionChange = (index, field, value) => {
    const newQuestions = [...questions];
    newQuestions[index][field] = value;
    
    // Auto-update correctAnswer when type changes
    if (field === 'type') {
      if (value === 'TN_2') {
        newQuestions[index].correctAnswerType = 'True';
      newQuestions[index].correctAnswer = 'ĐÚNG';
        newQuestions[index].A = '';
        newQuestions[index].B = '';
        newQuestions[index].C = '';
        newQuestions[index].D = '';
      } else {
        newQuestions[index].correctAnswerType = 'A';
        newQuestions[index].correctAnswer = newQuestions[index].A;
      }
    }
    
    // Update correctAnswer when answer options change
    if (['A', 'B', 'C', 'D'].includes(field) && newQuestions[index].type === 'TN_4') {
      if (newQuestions[index].correctAnswerType === field) {
        newQuestions[index].correctAnswer = value;
      }
    }
    
    // Update correctAnswer when correctAnswerType changes
    if (field === 'correctAnswerType') {
      if (newQuestions[index].type === 'TN_4') {
        newQuestions[index].correctAnswer = newQuestions[index][value];
      } else {
        newQuestions[index].correctAnswer = value === 'True' ? 'ĐÚNG' : 'SAI';
      }
    }
    
    setQuestions(newQuestions);
    
    // Clear errors for this field
    if (errors[`${index}_${field}`]) {
      const newErrors = { ...errors };
      delete newErrors[`${index}_${field}`];
      setErrors(newErrors);
    }
  };

  // Handle file upload
  const handleFileUpload = async (index, fileType, file) => {
    if (!file) return;
    
    setUploading(true);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'streak-questions');
      
      // Lấy token từ cookies để authentication
      const token = getToken();
      const headers = {};
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const response = await fetch('/api/strapi-proxy/upload-r2', {
        method: 'POST',
        headers,
        body: formData
      });
      
      const result = await response.json();
      
      if (result.success) {
        const newQuestions = [...questions];
        newQuestions[index][fileType] = result.data.url;
        newQuestions[index][`${fileType.replace('Path', 'File')}`] = file;
        setQuestions(newQuestions);
      } else {
        alert('Upload failed: ' + (result.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  // Remove uploaded image
  const removeImage = (index, fileType) => {
    const newQuestions = [...questions];
    newQuestions[index][fileType] = '';
    newQuestions[index][`${fileType.replace('Path', 'File')}`] = null;
    setQuestions(newQuestions);
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    questions.forEach((question, index) => {
      if (!question.content.trim()) {
        newErrors[`${index}_content`] = 'Nội dung câu hỏi không được để trống';
      }
      
      if (question.type === 'TN_4') {
        ['A', 'B', 'C', 'D'].forEach(option => {
          if (!question[option].trim()) {
            newErrors[`${index}_${option}`] = `Đáp án ${option} không được để trống`;
          }
        });
      }
      
      if (!question.correctAnswer.trim()) {
        newErrors[`${index}_correctAnswer`] = 'Đáp án đúng không được để trống';
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    // Prepare data for submission
    const questionsData = questions.map(q => ({
      content: q.content,
      type: q.type,
      A: q.A,
      B: q.B,
      C: q.C,
      D: q.D,
      correctAnswer: q.correctAnswer,
      correctAnswerType: q.correctAnswerType,
      explain: q.explain,
      imagePath: q.imagePath,
      imageExplainPath: q.imageExplainPath
    }));
    
    onSubmit(questionsData);
  };

  const renderQuestionCard = (question, index) => {
    const questionNumber = index + 1;
    const isVanDung = question.questionType === 'vận dụng';
    
    return (
      <div key={index} className="bg-white border border-secondary rounded-xl p-6xl space-y-5xl shadow-xs hover:shadow-sm transition-shadow duration-200">
        {/* Question Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-lg">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
              isVanDung 
                ? 'bg-utility-orange-50 text-utility-orange-700' 
                : 'bg-utility-blue-50 text-utility-blue-700'
            }`}>
              {questionNumber}
            </div>
            <h3 className="text-lg font-semibold text-primary-900">
              {isVanDung ? 'Câu hỏi vận dụng' : 'Câu hỏi hiểu biết'}
            </h3>
            <span className={`px-lg py-sm rounded-full text-sm font-medium border ${
              isVanDung 
                ? 'bg-utility-orange-50 text-utility-orange-700 border-utility-orange-200' 
                : 'bg-utility-blue-50 text-utility-blue-700 border-utility-blue-200'
            }`}>
              {question.questionType} ({isVanDung ? '20 điểm' : '10 điểm'})
            </span>
          </div>
          
          {/* Question Type Selector */}
          <select
            value={question.type}
            onChange={(e) => handleQuestionChange(index, 'type', e.target.value)}
            disabled={isReadOnly}
            className={`px-md py-sm border rounded-lg text-sm font-medium transition-all duration-200 ${
              isReadOnly 
                ? 'border-secondary-gray-200 bg-utility-gray-50 text-secondary-gray-600 cursor-not-allowed'
                : 'border-secondary-gray-default bg-white text-primary-900 focus:ring-2 focus:ring-primary-default focus:ring-opacity-20 focus:border-primary-default'
            }`}
          >
            <option value="TN_4">Trắc nghiệm 4 đáp án</option>
            <option value="TN_2">Đúng/Sai</option>
          </select>
        </div>

        {/* Question Content */}
        <div>
          <label className="block text-sm font-medium text-primary-900 mb-lg">
            Nội dung câu hỏi *
          </label>
          {previewMode ? (
            <div className="min-h-[120px] p-lg border border-secondary-gray-default rounded-lg bg-white">
                      <div className="text-primary-900">
                        <KaTeXRenderer content={question.content} />
                      </div>
            </div>
          ) : (
            <textarea
              value={question.content}
              onChange={(e) => handleQuestionChange(index, 'content', e.target.value)}
              rows={4}
              readOnly={isReadOnly}
              className={`w-full px-lg py-lg border rounded-lg transition-all duration-200 resize-none ${
                isReadOnly 
                  ? 'border-secondary-gray-200 bg-utility-gray-50 text-secondary-gray-600 cursor-not-allowed'
                  : errors[`${index}_content`] 
                    ? 'border-error-primary-600 bg-error-primary-50 text-primary-900 focus:ring-2 focus:ring-primary-default focus:ring-opacity-20 focus:border-primary-default' 
                    : 'border-secondary-gray-default bg-white text-primary-900 focus:ring-2 focus:ring-primary-default focus:ring-opacity-20 focus:border-primary-default'
              }`}
              placeholder={isReadOnly ? "Chỉ được xem, không thể chỉnh sửa" : "Nhập nội dung câu hỏi (hỗ trợ LaTeX)..."}
            />
          )}
          {errors[`${index}_content`] && (
            <div className="mt-sm flex items-center space-x-sm">
              <svg className="w-4 h-4 text-error-primary-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-sm text-error-primary-600">{errors[`${index}_content`]}</p>
            </div>
          )}
        </div>

        {/* Question Image Upload */}
        <div>
          <label className="block text-sm font-medium text-primary-900 mb-lg">
            Hình ảnh câu hỏi (tùy chọn)
          </label>
          <div className="flex items-center space-x-lg">
            {question.imagePath ? (
              <div className="relative group">
                <img
                  src={question.imagePath}
                  alt="Question"
                  className="w-24 h-24 object-cover rounded-lg border border-secondary-gray-default shadow-xs"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index, 'imagePath')}
                  className="absolute -top-sm -right-sm bg-error-primary-600 text-white rounded-full p-sm shadow-sm hover:bg-error-primary-700 transition-colors duration-200 opacity-0 group-hover:opacity-100"
                >
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </div>
            ) : (
              <label className="flex items-center justify-center w-24 h-24 border-2 border-dashed border-secondary-gray-300 rounded-lg cursor-pointer hover:bg-utility-gray-25 hover:border-primary-default transition-all duration-200">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) {
                      handleFileUpload(index, 'imagePath', file);
                    }
                  }}
                  className="hidden"
                />
                <PhotoIcon className="w-8 h-8 text-secondary-gray-400" />
              </label>
            )}
          </div>
        </div>

        {/* Answer Options */}
        {question.type === 'TN_4' ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-lg">
            {['A', 'B', 'C', 'D'].map((option) => (
              <div key={option} className="space-y-sm">
                <label className="block text-sm font-medium text-primary-900">
                  Đáp án {option} *
                </label>
                <div className="flex items-center space-x-md">
                  <input
                    type="radio"
                    name={`correct_${index}`}
                    checked={question.correctAnswerType === option}
                    onChange={() => handleQuestionChange(index, 'correctAnswerType', option)}
                    disabled={isReadOnly}
                    className={`w-4 h-4 border-secondary-gray-300 ${
                      isReadOnly 
                        ? 'text-secondary-gray-400 cursor-not-allowed'
                        : 'text-primary-600 focus:ring-primary-default focus:ring-2 focus:ring-opacity-20'
                    }`}
                  />
                  {previewMode ? (
                    <div className="flex-1 p-md border border-secondary-gray-default rounded-lg bg-white">
                      <div className="text-primary-900">
                        <KaTeXRenderer content={question[option]} />
                      </div>
                    </div>
                  ) : (
                    <input
                      type="text"
                      value={question[option]}
                      onChange={(e) => handleQuestionChange(index, option, e.target.value)}
                      readOnly={isReadOnly}
                      className={`flex-1 px-md py-md border rounded-lg transition-all duration-200 ${
                        isReadOnly 
                          ? 'border-secondary-gray-200 bg-utility-gray-50 text-secondary-gray-600 cursor-not-allowed'
                          : errors[`${index}_${option}`] 
                            ? 'border-error-primary-600 bg-error-primary-50 text-primary-900 focus:ring-2 focus:ring-primary-default focus:ring-opacity-20 focus:border-primary-default' 
                            : 'border-secondary-gray-default bg-white text-primary-900 focus:ring-2 focus:ring-primary-default focus:ring-opacity-20 focus:border-primary-default'
                      }`}
                      placeholder={isReadOnly ? "Chỉ được xem" : `Nhập đáp án ${option} (hỗ trợ LaTeX)`}
                    />
                  )}
                </div>
                {errors[`${index}_${option}`] && (
                  <div className="flex items-center space-x-sm">
                    <svg className="w-4 h-4 text-error-primary-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <p className="text-sm text-error-primary-600">{errors[`${index}_${option}`]}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-md">
            <label className="block text-sm font-medium text-primary-900">
              Đáp án đúng:
            </label>
            <div className="flex items-center space-x-6xl">
              <label className="flex items-center space-x-md cursor-pointer">
                <input
                  type="radio"
                  name={`correct_${index}`}
                  checked={question.correctAnswerType === 'True'}
                  onChange={() => handleQuestionChange(index, 'correctAnswerType', 'True')}
                  disabled={isReadOnly}
                  className={`w-4 h-4 border-secondary-gray-300 ${
                    isReadOnly 
                      ? 'text-secondary-gray-400 cursor-not-allowed'
                      : 'text-primary-600 focus:ring-primary-default focus:ring-2 focus:ring-opacity-20'
                  }`}
                />
                <span className="text-sm font-medium text-primary-900">ĐÚNG</span>
              </label>
              <label className="flex items-center space-x-md cursor-pointer">
                <input
                  type="radio"
                  name={`correct_${index}`}
                  checked={question.correctAnswerType === 'False'}
                  onChange={() => handleQuestionChange(index, 'correctAnswerType', 'False')}
                  disabled={isReadOnly}
                  className={`w-4 h-4 border-secondary-gray-300 ${
                    isReadOnly 
                      ? 'text-secondary-gray-400 cursor-not-allowed'
                      : 'text-primary-600 focus:ring-primary-default focus:ring-2 focus:ring-opacity-20'
                  }`}
                />
                <span className="text-sm font-medium text-primary-900">SAI</span>
              </label>
            </div>
          </div>
        )}

        {/* Explanation */}
        <div>
          <label className="block text-sm font-medium text-primary-900 mb-lg">
            Giải thích (tùy chọn)
          </label>
          {previewMode ? (
            <div className="min-h-[100px] p-lg border border-secondary-gray-default rounded-lg bg-white">
              <div className="text-primary-900">
                <KaTeXRenderer content={question.explain} />
              </div>
            </div>
          ) : (
            <textarea
              value={question.explain}
              onChange={(e) => handleQuestionChange(index, 'explain', e.target.value)}
              rows={3}
              readOnly={isReadOnly}
              className={`w-full px-lg py-lg border rounded-lg transition-all duration-200 resize-none ${
                isReadOnly 
                  ? 'border-secondary-gray-200 bg-utility-gray-50 text-secondary-gray-600 cursor-not-allowed'
                  : 'border-secondary-gray-default bg-white text-primary-900 focus:ring-2 focus:ring-primary-default focus:ring-opacity-20 focus:border-primary-default'
              }`}
              placeholder={isReadOnly ? "Chỉ được xem" : "Nhập giải thích (hỗ trợ LaTeX)..."}
            />
          )}
        </div>

        {/* Explanation Image Upload */}
        <div>
          <label className="block text-sm font-medium text-primary-900 mb-lg">
            Hình ảnh giải thích (tùy chọn)
          </label>
          <div className="flex items-center space-x-lg">
            {question.imageExplainPath ? (
              <div className="relative group">
                <img
                  src={question.imageExplainPath}
                  alt="Explanation"
                  className="w-24 h-24 object-cover rounded-lg border border-secondary-gray-default shadow-xs"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index, 'imageExplainPath')}
                  className="absolute -top-sm -right-sm bg-error-primary-600 text-white rounded-full p-sm shadow-sm hover:bg-error-primary-700 transition-colors duration-200 opacity-0 group-hover:opacity-100"
                >
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </div>
            ) : (
              <label className="flex items-center justify-center w-24 h-24 border-2 border-dashed border-secondary-gray-300 rounded-lg cursor-pointer hover:bg-utility-gray-25 hover:border-primary-default transition-all duration-200">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) {
                      handleFileUpload(index, 'imageExplainPath', file);
                    }
                  }}
                  className="hidden"
                />
                <PhotoIcon className="w-8 h-8 text-secondary-gray-400" />
              </label>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6xl">
      {/* Header */}
      <div className="border-b border-secondary pb-6xl mb-6xl">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6xl">
          <div>
            <div className="flex items-center space-x-lg mb-md">
              <div className="w-10 h-10 rounded-full bg-utility-brand-50 flex items-center justify-center">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#299D55" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <h2 className="text-display-xs font-semibold text-primary-900">
                {existingQuestions?.length > 0 ? 'Chỉnh sửa' : 'Tạo'} câu hỏi streak
              </h2>
            </div>
            <div className="flex flex-wrap items-center gap-lg text-sm">
              <div className="text-secondary-700">
                * Nội dung sẽ được tự động chuyển sang định dạng KaTeX khi lưu
              </div>
              {isReadOnly && (
                <div className="flex items-center gap-sm px-md py-sm bg-utility-orange-50 border border-utility-orange-200 rounded-lg">
                  <svg className="w-4 h-4 text-utility-orange-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <span className="text-utility-orange-700 font-medium">
                     Chỉ được chỉnh sửa câu hỏi cho các ngày từ hôm nay trở đi
                   </span>
                </div>
              )}
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-md">
            <button
              type="button"
              onClick={() => setPreviewMode(!previewMode)}
              className="inline-flex items-center justify-center px-lg py-md border border-secondary-gray-default shadow-xs text-sm font-medium rounded-lg text-secondary-700 bg-white hover:bg-utility-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-default transition-all duration-200"
            >
              <EyeIcon className="h-4 w-4 mr-sm" />
              {previewMode ? 'Chế độ sửa' : 'Xem trước'}
            </button>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">

      {/* Questions */}
      <div className="space-y-6">
        {questions.map((question, index) => renderQuestionCard(question, index))}
      </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="inline-flex items-center justify-center px-lg py-md border border-secondary-gray-default shadow-xs text-sm font-semibold rounded-lg text-secondary-700 bg-white hover:bg-utility-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-default transition-all duration-200"
            disabled={isLoading || uploading}
          >
            <svg className="h-4 w-4 mr-sm" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Hủy
          </button>
          <button
            type="submit"
            disabled={isLoading || uploading || isReadOnly}
            className={`inline-flex items-center justify-center px-lg py-md rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 shadow-xs ${
              isReadOnly 
                ? 'bg-utility-gray-100 text-secondary-gray-default cursor-not-allowed border border-secondary-gray-default'
                : 'bg-primary-default text-primary-default hover:bg-primary-hover focus:ring-primary-default disabled:opacity-50 disabled:cursor-not-allowed border border-transparent'
            }`}
          >
            {(isLoading || uploading) && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-sm"></div>
            )}
            <svg className="h-4 w-4 mr-sm" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>
              {isReadOnly 
                ? 'Không thể chỉnh sửa' 
                : uploading 
                  ? 'Đang upload...' 
                  : (isLoading ? 'Đang lưu...' : 'Lưu câu hỏi')
              }
            </span>
          </button>
        </div>
      </form>
    </div>
  );
}

