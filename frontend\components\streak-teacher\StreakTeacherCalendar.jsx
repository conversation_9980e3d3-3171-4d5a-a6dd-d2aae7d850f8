'use client';

import { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { getVietnamDate, formatVietnamDate } from '@/utils/vietnamTimezone';

export default function StreakTeacherCalendar({ selectedGrade, onDateSelect, isLoading }) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [calendarData, setCalendarData] = useState([]);
  const [loading, setLoading] = useState(false);

  // Lấy dữ liệu calendar từ API
  const fetchCalendarData = async () => {
    if (!selectedGrade) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/strapi-proxy/streak-teacher/calendar/${selectedGrade}`);
      const data = await response.json();
      
      if (data.calendar) {
        setCalendarData(data.calendar);
      }
    } catch (error) {
      console.error('Error fetching calendar data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCalendarData();
  }, [selectedGrade]);

  // Helper functions để làm việc với ngày tháng
  const getDaysInMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const formatDate = (year, month, day) => {
    return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  };

  const hasQuestions = (dateStr) => {
    return calendarData.some(item => item.date === dateStr);
  };

  const isPastDate = (year, month, day) => {
    const vietnamToday = getVietnamDate();
    const checkDate = new Date(year, month, day);
    const vietnamTodayMidnight = new Date(vietnamToday.getFullYear(), vietnamToday.getMonth(), vietnamToday.getDate());
    return checkDate < vietnamTodayMidnight;
  };

  const isFutureDate = (year, month, day) => {
    const vietnamToday = getVietnamDate();
    const checkDate = new Date(year, month, day);
    const vietnamTodayEndOfDay = new Date(vietnamToday.getFullYear(), vietnamToday.getMonth(), vietnamToday.getDate(), 23, 59, 59, 999);
    return checkDate > vietnamTodayEndOfDay;
  };

  const isToday = (year, month, day) => {
    const vietnamToday = getVietnamDate();
    return year === vietnamToday.getFullYear() && 
           month === vietnamToday.getMonth() && 
           day === vietnamToday.getDate();
  };

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  const goToToday = () => {
    const vietnamToday = getVietnamDate();
    setCurrentDate(new Date(vietnamToday.getFullYear(), vietnamToday.getMonth(), 1));
  };

  // Render calendar
  const renderCalendar = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDay = getFirstDayOfMonth(currentDate);
    
    const days = [];
    
    // Empty cells for days before the first day of month
    for (let i = 0; i < firstDay; i++) {
      days.push(
        <div key={`empty-${i}`} className="h-[64px] border border-secondary bg-utility-gray-25"></div>
      );
    }
    
    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = formatDate(year, month, day);
      const hasQ = hasQuestions(dateStr);
      const isPast = isPastDate(year, month, day);
      const isFuture = isFutureDate(year, month, day);
      const isCurrentDay = isToday(year, month, day);
      
      let cellClasses = 'h-[64px] border border-secondary flex flex-col items-center justify-center text-md cursor-pointer transition-all duration-200 relative group ';
      let textClasses = 'font-semibold';
      let indicatorClass = '';
      
      if (isPast) {
        if (hasQ) {
          cellClasses += 'bg-utility-success-50 hover:bg-utility-success-100 hover:shadow-sm ';
          textClasses += ' text-utility-success-700';
          indicatorClass = 'bg-utility-success-600';
        } else {
          cellClasses += 'bg-error-primary-50 hover:bg-error-primary-100 hover:shadow-sm ';
          textClasses += ' text-error-primary-700';
        }
      } else if (isFuture) {
        if (hasQ) {
          cellClasses += 'bg-utility-success-50 hover:bg-utility-success-100 hover:shadow-sm ';
          textClasses += ' text-utility-success-700';
          indicatorClass = 'bg-utility-success-600';
        } else {
          cellClasses += 'bg-white hover:bg-utility-gray-50 hover:shadow-sm ';
          textClasses += ' text-primary-900';
        }
      } else {
        // Today
        if (hasQ) {
          cellClasses += 'bg-utility-success-50 hover:bg-utility-success-100 hover:shadow-md ';
          textClasses += ' text-utility-success-700';
          indicatorClass = 'bg-utility-success-600';
        } else {
          cellClasses += 'bg-utility-blue-50 hover:bg-utility-blue-100 hover:shadow-md cursor-pointer ';
          textClasses += ' text-primary-900';
        }
      }
      
      if (isCurrentDay) {
        cellClasses += 'ring-2 ring-primary-default ';
      }
      
      days.push(
        <div
          key={day}
          className={cellClasses}
          onClick={() => {
            if (!isPast || hasQ || isCurrentDay) {
              onDateSelect(dateStr);
            }
          }}
        >
          <span className={textClasses}>{day}</span>
          {hasQ && (
            <div className={`absolute top-2 right-2 w-2 h-2 ${indicatorClass} rounded-full`}></div>
          )}
          {/* Hover tooltip */}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-primary-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
            {hasQ ? 'Đã có câu hỏi - Click để chỉnh sửa' : (isCurrentDay ? 'Hôm nay - Click để tạo hoặc chỉnh sửa câu hỏi' : 'Chưa có câu hỏi - Click để tạo mới')}
          </div>
        </div>
      );
    }
    
    return days;
  };

  const monthNames = [
    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
  ];

  const dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];

  return (
    <div className="bg-white rounded-2xl shadow-xs border border-secondary">
      {/* Header */}
      <div className="p-6xl border-b border-secondary">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6xl">
          <div>
            <h2 className="text-lg font-semibold text-primary-900 mb-sm">
              Lịch quản lý câu hỏi - Lớp {selectedGrade}
            </h2>
            <p className="text-sm text-secondary-700">
              Chọn ngày để tạo hoặc chỉnh sửa câu hỏi streak
            </p>
          </div>
          
          <div className="flex items-center justify-between sm:justify-end gap-lg">
            {/* Navigation */}
            <div className="flex items-center space-x-md">
              <button
                onClick={goToPreviousMonth}
                className="p-md hover:bg-utility-gray-100 rounded-lg transition-colors duration-200"
              >
                <ChevronLeftIcon className="h-5 w-5 text-secondary-700" />
              </button>
              <span className="text-md font-medium text-primary-900 min-w-[140px] text-center">
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </span>
              <button
                onClick={goToNextMonth}
                className="p-md hover:bg-utility-gray-100 rounded-lg transition-colors duration-200"
              >
                <ChevronRightIcon className="h-5 w-5 text-secondary-700" />
              </button>
            </div>
            
            {/* Today button */}
            <button
              onClick={goToToday}
              className="px-lg py-md text-sm font-medium bg-primary-default text-white rounded-lg hover:bg-primary-600 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              Hôm nay
            </button>
          </div>
        </div>
      </div>

      {/* Calendar Content */}
      <div className="p-6xl">
        {/* Loading indicator */}
        {(loading || isLoading) && (
          <div className="flex justify-center mb-6xl">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-default"></div>
          </div>
        )}

        {/* Calendar Grid */}
        <div className="border border-secondary rounded-lg overflow-hidden mb-6xl">
          {/* Day headers */}
          <div className="grid grid-cols-7 bg-utility-gray-50">
            {dayNames.map((day) => (
              <div key={day} className="p-lg text-center text-sm font-medium text-secondary-700 border-r border-secondary last:border-r-0">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar days */}
          <div className="grid grid-cols-7">
            {renderCalendar()}
          </div>
        </div>

        {/* Legend */}
        <div className="grid grid-cols-2 gap-xl text-xs">
          <div className="space-y-md">
            <div className="flex items-center space-x-sm">
              <div className="w-md h-md bg-utility-success-50 rounded"></div>
              <span className="text-secondary-700">Đã có câu hỏi</span>
            </div>
            <div className="flex items-center space-x-sm">
              <div className="w-md h-md bg-white border border-secondary rounded"></div>
              <span className="text-secondary-700">Chưa có câu hỏi</span>
            </div>
          </div>
          <div className="space-y-md">
            <div className="flex items-center space-x-sm">
              <div className="w-md h-md bg-utility-blue-50 rounded ring-2 ring-primary-default"></div>
              <span className="text-secondary-700">Hôm nay</span>
            </div>
            <div className="flex items-center space-x-sm">
              <div className="w-md h-md bg-error-primary rounded"></div>
              <span className="text-secondary-700">Quá hạn</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
