'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';

export default function TeacherCodeVerification({ onVerifySuccess, onCancel }) {
  const [code, setCode] = useState(new Array(6).fill(''));
  const [activeIndex, setActiveIndex] = useState(-1);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  
  const inputRefs = useRef([]);
  
  // Mã giáo viên mặc định
  const TEACHER_CODE = '999999';

  const handleSubmit = async (e) => {
    e.preventDefault();
    const codeString = code.join('');
    
    if (codeString.length !== 6) {
      setError('Vui lòng nhập đủ 6 số');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Kiểm tra mã giáo viên
      if (codeString === TEACHER_CODE) {
        // Lưu vào localStorage để nhớ trạng thái đã xác thực
        localStorage.setItem('teacher_verified', 'true');
        localStorage.setItem('teacher_verified_time', Date.now().toString());
        onVerifySuccess();
      } else {
        setError('Mã giáo viên không đúng. Vui lòng thử lại.');
      }
    } catch (err) {
      setError('Có lỗi xảy ra. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  const handleFocus = (index) => {
    setActiveIndex(index);
  };

  const handleBlur = () => {
    setActiveIndex(-1);
  };

  const handlePaste = (e, index) => {
    const pastedData = e.clipboardData.getData('Text');
    if (/^\d{6}$/.test(pastedData)) {
      const newCode = pastedData.split('');
      setCode(newCode);
      newCode.forEach((_, i) => {
        if (inputRefs.current[i]) {
          inputRefs.current[i].value = newCode[i] || '';
        }
      });
      setActiveIndex(newCode.length);
      setError('');
    }
  };

  const handleChange = (e, index) => {
    const value = e.target.value;
    if (isNaN(value)) return;

    const newCode = [...code];
    newCode[index] = value.substring(value.length - 1);
    setCode(newCode);

    if (error) {
      setError('');
    }

    if (value && index < 5) {
      inputRefs.current[index + 1].focus();
      setActiveIndex(index + 1);
    } else if (index === 5) {
      inputRefs.current[index].blur();
      setActiveIndex(-1);
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === 'Backspace') {
      const newCode = [...code];
      if (index > 0 && !code[index]) {
        newCode[index - 1] = '';
        setCode(newCode);
        inputRefs.current[index - 1].focus();
        setActiveIndex(index - 1);
      } else {
        newCode[index] = '';
        setCode(newCode);
        inputRefs.current[index].focus();
        setActiveIndex(index);
      }
    }
  };

  return (
    <div className="min-h-screen bg-utility-brand-50 flex items-center justify-center p-lg">
      <div className="w-full max-w-[400px] px-4">
        {/* Logo và icon */}
        <div className="flex justify-center mb-6xl">
          <div className="w-[80px] h-[80px] rounded-full bg-white shadow-lg flex items-center justify-center">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 14C16.4183 14 20 10.4183 20 6C20 1.58172 16.4183 -2 12 -2C7.58172 -2 4 1.58172 4 6C4 10.4183 7.58172 14 12 14Z" stroke="#299D55" strokeWidth="2"/>
              <path d="M2 21C2 17.134 6.47715 14 12 14C17.5228 14 22 17.134 22 21" stroke="#299D55" strokeWidth="2"/>
              <path d="M12 10C13.1046 10 14 9.10457 14 8C14 6.89543 13.1046 6 12 6C10.8954 6 10 6.89543 10 8C10 9.10457 10.8954 10 12 10Z" stroke="#299D55" strokeWidth="2"/>
            </svg>
          </div>
        </div>

        {/* Tiêu đề */}
        <div className="text-center mb-8xl">
          <h1 className="text-display-sm font-semibold text-primary-900 mb-lg">
            Xác thực mã giáo viên
          </h1>
          <p className="text-md font-normal text-secondary-700">
            Nhập mã giáo viên để truy cập trang quản lý câu hỏi streak
          </p>
        </div>

        {/* Form nhập mã */}
        <div className="bg-white rounded-2xl shadow-lg p-6xl mb-6xl">
          <form onSubmit={handleSubmit} className="w-full max-w-[360px] mx-auto">
            <div className="mb-lg">
              <label className="block text-sm font-medium text-primary-900 mb-lg">
                Mã xác thực (6 chữ số)
              </label>
            </div>
            
            <div className="flex gap-2 mb-xl w-full justify-between">
              {code.map((digit, index) => (
                <input
                  key={index}
                  type="text"
                  ref={(ref) => (inputRefs.current[index] = ref)}
                  value={code[index]}
                  onChange={(e) => handleChange(e, index)}
                  onKeyDown={(e) => handleKeyDown(e, index)}
                  onFocus={() => handleFocus(index)}
                  onBlur={handleBlur}
                  onPaste={(e) => handlePaste(e, index)}
                  className={`w-full h-[51px] rounded-[10px] text-center text-3xl font-medium transition-all duration-200 ${
                    error
                      ? 'border-[#FDA29B] border-2 text-[#D92D20]'
                      : activeIndex === index
                      ? 'border-[#299D55] border-2 text-[#181D27]'
                      : 'border-[#D5D7DA] border text-[#181D27]'
                  } focus:border-[#299D55] focus:outline-none focus:border-2`}
                  maxLength={1}
                  inputMode="numeric"
                  pattern="[0-9]*"
                />
              ))}
            </div>

            {/* Error message */}
            {error && (
              <div className="bg-error-primary-50 border border-error-primary-200 rounded-lg p-lg mb-xl">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-error-primary-600 mr-md" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-error-primary-700">{error}</span>
                </div>
              </div>
            )}

            {/* Buttons */}
            <div className="space-y-md">
              <button
                type="submit"
                disabled={code.some((digit) => digit === '') || loading}
                className={`w-full flex justify-center items-center py-lg px-xl rounded-lg font-semibold text-md transition-all duration-200 ${
                  code.some((digit) => digit === '') || loading
                    ? 'cursor-not-allowed text-utility-gray-400 bg-utility-gray-100 border border-utility-gray-200'
                    : 'bg-primary-default text-white hover:bg-primary-600 shadow-sm hover:shadow-md'
                }`}
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-md h-5 w-5 text-current" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Đang xử lý...
                  </>
                ) : (
                  'Xác nhận'
                )}
              </button>

              <button
                type="button"
                onClick={onCancel}
                className="w-full flex justify-center items-center py-lg px-xl rounded-lg font-semibold text-md border border-secondary-gray-default text-secondary-700 bg-white hover:bg-utility-gray-50 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                Hủy bỏ
              </button>
            </div>
          </form>
        </div>

        {/* Hướng dẫn */}
        <div className="text-center">
          <p className="text-sm text-secondary-600">
            Liên hệ quản trị viên để nhận mã giáo viên
          </p>
        </div>
      </div>
    </div>
  );
}

