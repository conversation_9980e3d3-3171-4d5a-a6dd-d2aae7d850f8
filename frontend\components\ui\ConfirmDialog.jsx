"use client";

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import Button from '../Button';

const ConfirmDialog = ({
  isOpen,
  onClose,
  onConfirm,
  title = "<PERSON>á<PERSON> nhận",
  message = "Bạn có chắc chắn muốn thực hiện hành động này?",
  confirmText = "Xác nhận",
  cancelText = "Hủy",
  type = "default", // default, danger, warning
  loading = false
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleConfirm = () => {
    onConfirm();
  };

  const handleCancel = () => {
    if (!loading) {
      onClose();
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && !loading) {
      onClose();
    }
  };

  const getConfirmButtonVariant = () => {
    // Sử dụng primary cho tất cả cases vì Button component chỉ có primary, secondaryGray, secondaryColor
    return 'primary';
  };

  const getConfirmButtonClass = () => {
    // Custom styling cho danger và warning cases
    switch (type) {
      case 'danger':
        return 'bg-error-primary-600 text-white hover:bg-error-primary-700 border-error-primary-600';
      case 'warning':
        return 'bg-utility-warning-700 text-white hover:bg-utility-warning-800 border-utility-warning-700';
      default:
        return '';
    }
  };

  if (!mounted || !isOpen) return null;

  return createPortal(
    <div
      className="fixed inset-0 bg-[#000000] bg-opacity-50 backdrop-blur-[2px] flex items-center justify-center"
      style={{zIndex: 60}}
      onClick={handleBackdropClick}
    >
      <div className="bg-[#FFFFFF] rounded-2xl max-w-[400px] w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between px-6 pt-6 pb-5 border-b border-[#E9EAEB]">
          <h2 className="text-xl font-semibold text-[#181D27]">
            {title}
          </h2>
          <button
            onClick={handleCancel}
            disabled={loading}
            className="text-[#717680] hover:text-[#181D27] transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
              <path d="M18 6.5L6 18.5M6 6.5L18 18.5" stroke="#A4A7AE" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="px-6 py-6 pointer-events-none">
          <div className="text-center mb-6">
            {/* Icon */}
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-[#FEF3C7] mb-4">
              <svg className="h-8 w-8 text-[#D97706]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>

            <p className="text-[#535862] text-base">
              {message}
            </p>
          </div>

          {/* Actions */}
          <div className="flex flex-col-reverse sm:flex-row gap-3 w-full pointer-events-auto">
            <Button
              variant="secondaryGray"
              onClick={handleCancel}
              disabled={loading}
              className="flex-1"
            >
              {cancelText}
            </Button>
            <Button
              variant={getConfirmButtonVariant()}
              onClick={handleConfirm}
              disabled={loading}
              className={`flex-1 ${getConfirmButtonClass()}`}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Đang xử lý...
                </div>
              ) : (
                confirmText
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default ConfirmDialog;
