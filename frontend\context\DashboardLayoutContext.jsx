"use client";
import React, {createContext, useContext, useEffect, useState} from "react";
import strapi from "@/app/api/strapi";
import {UserContext} from "@/context/UserProvider";

const DashboardLayoutContext = createContext();

export function DashboardLayoutProvider({children}) {
    // Các state dùng chung cho toàn layout
    const [title, setTitle] = useState("");
    const [keySearch, setKeySearch] = useState("");
    const [isSearch, setIsSearch] = useState(false);
    const [isDetail, setIsDetail] = useState(false);
    const [isTurnLive, setIsTurnLive] = useState(false);
    const {user, completedOrderInfo,getUser} = useContext(UserContext);
    const [streakNumber,setStreakNumber] = useState(0);
    const [chemPoint, setChemPoint] = useState(0);

    useEffect(() => {
        getStreak();
    }, []);
    const getStreak = async () => {
        const user = getUser();
        
        // 1. Lấy current streak từ getTotalRollup
        const streakRes = await strapi.streak.getTotalRollup({user_id: user.id});
        let currentStreak = 0;
        if (streakRes && streakRes.data) {
            const streakData = {...streakRes.data.data};
            currentStreak = streakData.count || 0;
            setStreakNumber(currentStreak);
        }
        
        // 2. Lấy total points từ getDataStreakDashboard
        const pointsRes = await strapi.streak.getDataStreakDashboard({user_id: user.id});
        if (pointsRes && pointsRes.data) {
            const pointsData = {...pointsRes.data.data[0]};
            // Hiển thị total points từ tất cả câu hỏi đã làm đúng
            setChemPoint(pointsData.point || 0);
        }
    }


    return (
        <DashboardLayoutContext.Provider value={{
            title, setTitle,
            keySearch, setKeySearch,
            isSearch, setIsSearch,
            isDetail, setIsDetail,
            isTurnLive, setIsTurnLive,streakNumber,chemPoint,getStreak
        }}>
            {children}
        </DashboardLayoutContext.Provider>
    );
}

export function useDashboardLayout() {
    return useContext(DashboardLayoutContext);
}