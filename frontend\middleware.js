import { NextResponse } from 'next/server'

// Helper function to check if error is authentication related
function isAuthenticationError(error) {
    const authErrorMessages = [
        'unauthorized',
        'authentication failed',
        'token expired',
        'invalid token',
        'access denied',
        'jwt',
        'cookie'
    ];

    const errorMessage = error?.message?.toLowerCase() || '';
    return authErrorMessages.some(msg => errorMessage.includes(msg));
}

// Helper function to log middleware actions (only in development)
function logMiddlewareAction(action, pathname, details = {}) {
    if (process.env.NODE_ENV === 'development') {
        console.log(`🛡️ Middleware [${action}]:`, pathname, details);
    }
}

export function middleware(request) {
    try {
        // Lấy cookie token - support both token names for backward compatibility
        const token = request.cookies.get('access_token')?.value || request.cookies.get('token')?.value
        const user_data = request.cookies.get('user_data')?.value

    // Lấy đường dẫn hiện tại
    const { pathname } = request.nextUrl

    // Xử lý riêng cho URL có dấu $ ở cuối
    if (pathname === '/$') {
        return NextResponse.redirect(new URL('/', request.url))
    }

    // Thêm điều kiện kiểm tra các đường dẫn API
    const isApiPath = pathname.startsWith('/api/');

    // Các trang không cần xác thực
    const publicPaths = [
        '/dang-nhap',
        '/dang-ky',
        '/quen-mat-khau',
        '/',
        '/khoa-hoc',
        '/bai-viet',
        '/hoc-tai-trung-tam',
        '/thanh-toan',
        '/hoa-don',
        '/xac-thuc',
        '/mat-khau-moi',
        '/chinh-sach-bao-mat',
        '/dieu-khoan',
        '/sitemap.xml', // Cho phép truy cập sitemap
        '/robots.txt', // Cho phép truy cập robots.txt
        '/streak-giao-vien',
    ]

    // Các trang auth luôn được phép truy cập
    const authPaths = [
        '/dang-nhap',
        '/dang-ky',
        '/quen-mat-khau',
        '/xac-thuc',
        '/mat-khau-moi'
    ]

    // Kiểm tra xem có phải là trang public không 

    const isPublicPath = publicPaths.some(path =>
        pathname === path ||
        pathname.startsWith('/api/auth/') ||  // Thêm điều kiện này
        pathname.startsWith('/khoa-hoc/') ||
        pathname.startsWith('/bai-viet/')
    )

    // Kiểm tra xem có phải là trang auth không
    const isAuthPath = authPaths.some(path => pathname === path)

    // Kiểm tra xem có phải là trang quản lý không
    const isManagementPath = pathname === '/quan-ly' || pathname.startsWith('/quan-ly/') || pathname === '/quan-ly/streak'

    // Kiểm tra xem có phải là trang thông tin cá nhân không
    const isPersonalInfoPath = pathname === '/thong-tin-ca-nhan'

    // Kiểm tra xem có phải là trang tài khoản hoặc thông báo không
    const isAccountPath = pathname === '/tai-khoan' || pathname === '/thong-bao'

    // Kiểm tra xem có phải là trang static không (cho các tài nguyên tĩnh)
    const isStaticResource = /\.(jpg|jpeg|png|gif|svg|css|js|webmanifest)$/.test(pathname)

    // Nếu là API request, cho phép đi qua mà không can thiệp
    if (isApiPath) {
        return NextResponse.next();
    }

    // Nếu không có token và đang truy cập trang cần xác thực
    if (!token && !isPublicPath && !isStaticResource && pathname !== '/sitemap.xml' && pathname !== '/robots.txt') {
        // Tạo đường dẫn đầy đủ (bao gồm cả query string) để làm callback
        const url = new URL('/dang-nhap', request.url)
        const fullPathWithQuery = `${request.nextUrl.pathname}${request.nextUrl.search}`
        url.searchParams.set('callbackUrl', fullPathWithQuery)
        return NextResponse.redirect(url)
    }

    // Kiểm tra thông tin cá nhân nếu có token và cookie user_data
    if (token && user_data && !isPersonalInfoPath && !isAuthPath && !isStaticResource) {
        try {
            // Parse user_data từ cookie
            const userData = JSON.parse(user_data)

            // Kiểm tra xem user đã có đầy đủ thông tin cá nhân chưa
            const hasFullPersonalInfo = userData.fullname && userData.date && (userData.gender !== undefined)
            //Nếu chưa xác thuc OTP => chuyển hướng đến trang xác thực
            if (!userData.verified_otp && userData.provider !== 'google') {
                return NextResponse.redirect(new URL(`/xac-thuc?from=dang-ky&email=${userData.email}`, request.url))
            }
            // Nếu chưa có đủ thông tin và không đang ở trang thông tin cá nhân, chuyển hướng đến trang thông tin cá nhân
            if (!hasFullPersonalInfo) {
                return NextResponse.redirect(new URL('/thong-tin-ca-nhan', request.url))
            }
        } catch (error) {
            console.error('Error parsing user_data:', error)
        }
    }

    // Kiểm tra nếu user đã có đơn hàng completed
    const hasCompletedOrder = request.cookies.get('hasCompletedOrder')?.value === 'true'

    // LOGIC ĐÚNG: User đã mua khóa học chỉ được tương tác ở /quan-ly
    // Homepage và các trang khác chỉ dành cho user chưa mua khóa học
    if (token && hasCompletedOrder) {
        // User đã mua khóa học chỉ được truy cập:
        // - Các trang quản lý (/quan-ly/*)
        // - Các trang tài khoản (/tai-khoan/*)
        // - Các trang auth (logout, etc.)
        // - Static resources

        if (!isManagementPath && !isAuthPath && !isAccountPath && !isStaticResource) {
            logMiddlewareAction('REDIRECT_TO_MANAGEMENT', pathname, {
                hasCompletedOrder: true,
                reason: 'User đã mua khóa học chỉ được tương tác ở /quan-ly'
            });
            return NextResponse.redirect(new URL('/quan-ly', request.url));
        }
    }

    // Nếu user chưa có đơn hàng completed nhưng cố gắng truy cập trang quản lý
    if (token && !hasCompletedOrder && isManagementPath) {
        logMiddlewareAction('REDIRECT_TO_HOME', pathname, { reason: 'No completed order' });
        return NextResponse.redirect(new URL('/', request.url));
    }

    // Nếu đã có token và đang truy cập các trang đăng nhập/đăng ký
    if (token && (pathname === '/dang-nhap' || pathname === '/dang-ky')) {
        // CẢI THIỆN: Redirect về trang phù hợp dựa trên trạng thái user
        const redirectUrl = hasCompletedOrder ? '/quan-ly' : '/';
        logMiddlewareAction('REDIRECT_AUTHENTICATED_USER', pathname, { redirectTo: redirectUrl });
        return NextResponse.redirect(new URL(redirectUrl, request.url));
    }

    logMiddlewareAction('ALLOW', pathname, { authenticated: !!token, hasCompletedOrder });
    return NextResponse.next();

} catch (error) {
    console.error('🚨 Middleware error:', error);

    // Nếu là lỗi authentication, redirect về custom error page
    if (isAuthenticationError(error)) {
        logMiddlewareAction('ERROR_REDIRECT_TO_AUTH_ERROR', request.nextUrl.pathname, { error: error.message });
        return NextResponse.redirect(new URL('/loi-xac-thuc', request.url));
    }

    // Các lỗi khác, log và cho phép request tiếp tục
    logMiddlewareAction('ERROR_CONTINUE', request.nextUrl.pathname, { error: error.message });
    return NextResponse.next();
}
}

// Chỉ áp dụng middleware với các đường dẫn sau
export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         */
        '/((?!_next/static|_next/image|favicon.ico).*)',
    ],
}