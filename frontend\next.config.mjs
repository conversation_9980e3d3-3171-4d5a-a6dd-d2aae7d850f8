import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const strapiBaseUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337';
const parsedUrl = new URL(strapiBaseUrl);

/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        remotePatterns: [
            {
                protocol: parsedUrl.protocol.replace(':', ''),
                hostname: parsedUrl.hostname,
                port: parsedUrl.port || '',
                pathname: '/uploads/**',
            },
            {
                protocol: 'http',
                hostname: 'localhost',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'localhost',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'ongbadayhoa.com',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'be.ongbadayhoa.com',
                port: '',
                pathname: '/**',
            },
            {
                protocol: 'https',
                hostname: 'img.ongbadayhoa.com',
                port: '',
                pathname: '/**',
            },
        ],
    },
    webpack: (config) => {
        config.resolve.alias['@'] = __dirname;
        return config;
    },
    // serverActions: {
    //     bodySizeLimit: '50mb',
    // },
    reactStrictMode: true,
};

export default nextConfig;
