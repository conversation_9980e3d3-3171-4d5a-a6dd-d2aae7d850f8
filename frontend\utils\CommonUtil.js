/**
 * Common utility functions for the application
 * @class CommonUtil
 */
/**
 * @typedef {Object} DimensionsParam
 * @property {number} [width=20] - Width of the icon in pixels
 * @property {number} [height=20] - Height of the icon in pixels
 */
export class CommonUtil {
    /**
     * Calculate appropriate stroke width for SVG icons based on dimensions
     * Used to maintain consistent visual weight across different icon sizes
     * 
     * @param {DimensionsParam} params - The dimensions object
     * @returns {string} The stroke width value as string, or empty string if no match
     * 
     * @example
     * // Get stroke width for default 20x20 icon
     * CommonUtil.getStrokeWidth({width: 20, height: 20}); // returns "1.67"
     * 
     * @example
     * // Get stroke width for larger 24x24 icon
     * CommonUtil.getStrokeWidth({width: 24, height: 24}); // returns "2"
     */
    static getStrokeWidth({ width = 20, height = 20 }) {
        let strokeWidth = '';
        if (width === 20 && height === 20) {
            strokeWidth = "1.67";
        } else if (width === 24 && height === 24) {
            strokeWidth = "2";
        } else if (width === 16 && height === 16) {
            strokeWidth = "1.33";
        } else if (width === 12 && height === 12) {
            strokeWidth = "1";
        }
        return strokeWidth;
    }
}