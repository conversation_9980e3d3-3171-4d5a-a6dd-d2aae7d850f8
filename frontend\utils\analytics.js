/**
 * Google Analytics tracking utilities
 * Provides functions to track user interactions and events
 */

/**
 * @typedef {Object} EventParams
 * @property {string} action - The action being tracked (e.g., 'click', 'view', 'purchase')
 * @property {string} category - Event category (e.g., 'engagement', 'video', 'form')
 * @property {string} [label] - Optional event label for additional context
 * @property {number} [value] - Optional numeric value associated with the event
 */

/**
 * Track a custom event in Google Analytics
 * Safely handles cases where GA is not yet loaded
 * 
 * @param {EventParams} params - Event tracking parameters
 * 
 * @example
 * // Track a button click
 * event({
 *   action: 'click',
 *   category: 'engagement',
 *   label: 'header_cta_button'
 * });
 * 
 * @example
 * // Track a purchase with value
 * event({
 *   action: 'purchase',
 *   category: 'ecommerce',
 *   label: 'course_chemistry_101',
 *   value: 299000
 * });
 * @returns {void}
 */
export const event = ({ action, category, label, value }) => {
  if (typeof window.gtag !== 'function') {
    console.warn('GA is not loaded yet.');
    return;
  }
  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  });
}; 