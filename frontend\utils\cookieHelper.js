/**
 * <PERSON>ie Helper Utility
 * Chuẩn hóa việc quản lý cookies authentication trong toàn bộ ứng dụng
 */

import Cookies from 'js-cookie';

/**
 * @typedef {Object} AuthData
 * @property {string} [token]
 * @property {Object} [user]
 * @property {boolean} [hasCompletedOrder]
 * @property {Object} [completedOrderInfo]
 */

/**
 * @typedef {Object} AuthState
 * @property {string|null} token
 * @property {Object|null} user
 * @property {boolean} hasCompletedOrder
 * @property {Object|null} completedOrderInfo
 */

// Danh sách tất cả cookies liên quan đến authentication
/**
 * @readonly
 * @type {{ACCESS_TOKEN:string, TOKEN:string, USER_DATA:string, HAS_COMPLETED_ORDER:string, COMPLETED_ORDER_INFO:string}}
 */
export const AUTH_COOKIES = {
  ACCESS_TOKEN: 'access_token',
  TOKEN: 'token', // Backward compatibility
  USER_DATA: 'user_data',
  HAS_COMPLETED_ORDER: 'hasCompletedOrder',
  COMPLETED_ORDER_INFO: 'completedOrderInfo'
};

// Cookie options mặc định
const DEFAULT_COOKIE_OPTIONS = {
  path: '/',
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax'
};

/**
 * Xóa tất cả cookies liên quan đến authentication
 * @param {boolean} useDocumentCookie - Sử dụng document.cookie thay vì js-cookie (cho trường hợp emergency)
 * @returns {void}
 */
export const clearAuthCookies = (useDocumentCookie = false) => {
  const cookiesToClear = Object.values(AUTH_COOKIES);
  
  if (useDocumentCookie && typeof document !== 'undefined') {
    // Phương pháp emergency clear cookies
    cookiesToClear.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`;
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    });
  } else {
    // Phương pháp thông thường với js-cookie
    cookiesToClear.forEach(cookieName => {
      Cookies.remove(cookieName, { path: '/' });
      // Thử xóa với domain hiện tại nếu có
      if (typeof window !== 'undefined') {
        Cookies.remove(cookieName, { path: '/', domain: window.location.hostname });
      }
    });
  }
  
  // Clear localStorage items liên quan
  if (typeof window !== 'undefined') {
    localStorage.removeItem('user');
    localStorage.removeItem('user_data');
    localStorage.removeItem('auth_token');
    localStorage.removeItem('completedOrderInfo');
  }
};

/**
 * Lưu thông tin authentication vào cookies
 * @param {AuthData} authData - Dữ liệu authentication
 * @param {number} expirationDays - Số ngày hết hạn (mặc định 15)
 * @returns {void}
 */
export const setAuthCookies = (authData, expirationDays = 15) => {
  const { token, user, hasCompletedOrder, completedOrderInfo } = authData;
  
  const cookieOptions = {
    ...DEFAULT_COOKIE_OPTIONS,
    expires: expirationDays
  };
  
  // Lưu token
  if (token) {
    Cookies.set(AUTH_COOKIES.ACCESS_TOKEN, token, cookieOptions);
  }
  
  // Lưu thông tin user
  if (user) {
    Cookies.set(AUTH_COOKIES.USER_DATA, JSON.stringify(user), cookieOptions);
  }
  
  // Lưu trạng thái đơn hàng
  if (hasCompletedOrder !== undefined) {
    Cookies.set(AUTH_COOKIES.HAS_COMPLETED_ORDER, hasCompletedOrder.toString(), cookieOptions);
  }
  
  // Lưu thông tin đơn hàng completed
  if (completedOrderInfo) {
    Cookies.set(AUTH_COOKIES.COMPLETED_ORDER_INFO, JSON.stringify(completedOrderInfo), cookieOptions);
  }
};

/**
 * Lấy tất cả thông tin authentication từ cookies
 * @returns {AuthState} Thông tin authentication
 */
export const getAuthCookies = () => {
  try {
    const token = Cookies.get(AUTH_COOKIES.ACCESS_TOKEN) || Cookies.get(AUTH_COOKIES.TOKEN);
    const userDataStr = Cookies.get(AUTH_COOKIES.USER_DATA);
    const hasCompletedOrderStr = Cookies.get(AUTH_COOKIES.HAS_COMPLETED_ORDER);
    const completedOrderInfoStr = Cookies.get(AUTH_COOKIES.COMPLETED_ORDER_INFO);
    
    return {
      token,
      user: userDataStr ? JSON.parse(userDataStr) : null,
      hasCompletedOrder: hasCompletedOrderStr === 'true',
      completedOrderInfo: completedOrderInfoStr ? JSON.parse(completedOrderInfoStr) : null
    };
  } catch (error) {
    console.error('Error parsing auth cookies:', error);
    return {
      token: null,
      user: null,
      hasCompletedOrder: false,
      completedOrderInfo: null
    };
  }
};

/**
 * Kiểm tra xem user có đăng nhập không
 * @returns {boolean}
 */
export const isAuthenticated = () => {
  const { token, user } = getAuthCookies();
  return !!(token && user);
};

/**
 * Lấy token từ cookies
 * @returns {string|null}
 */
export const getToken = () => {
  return Cookies.get(AUTH_COOKIES.ACCESS_TOKEN) || Cookies.get(AUTH_COOKIES.TOKEN) || null;
};

/**
 * Lấy thông tin user từ cookies
 * @returns {Object|null}
 */
export const getUser = () => {
  const { user } = getAuthCookies();
  return user;
};

/**
 * Kiểm tra xem user có đơn hàng completed không
 * @returns {boolean}
 */
export const hasCompletedOrder = () => {
  const { hasCompletedOrder } = getAuthCookies();
  return hasCompletedOrder;
};

/**
 * Lấy thông tin đơn hàng completed
 * @returns {Object|null}
 */
export const getCompletedOrderInfo = () => {
  const { completedOrderInfo } = getAuthCookies();
  return completedOrderInfo;
};

/**
 * Cập nhật thông tin user trong cookie
 * @param {Object} updatedUser - Thông tin user mới
 * @returns {void}
 */
export const updateUserCookie = (updatedUser) => {
  const currentAuth = getAuthCookies();
  if (currentAuth.user) {
    const mergedUser = { ...currentAuth.user, ...updatedUser };
    Cookies.set(AUTH_COOKIES.USER_DATA, JSON.stringify(mergedUser), DEFAULT_COOKIE_OPTIONS);
  }
};

/**
 * Cập nhật trạng thái đơn hàng
 * @param {boolean} hasOrder - Có đơn hàng completed không
 * @param {Object} orderInfo - Thông tin đơn hàng
 * @returns {void}
 */
export const updateOrderStatus = (hasOrder, orderInfo = null) => {
  Cookies.set(AUTH_COOKIES.HAS_COMPLETED_ORDER, hasOrder.toString(), DEFAULT_COOKIE_OPTIONS);
  
  if (orderInfo) {
    Cookies.set(AUTH_COOKIES.COMPLETED_ORDER_INFO, JSON.stringify(orderInfo), DEFAULT_COOKIE_OPTIONS);
  } else if (!hasOrder) {
    Cookies.remove(AUTH_COOKIES.COMPLETED_ORDER_INFO, { path: '/' });
  }
};

/**
 * Debug function - Log tất cả cookies authentication
 * @returns {void}
 */
export const debugAuthCookies = () => {
  if (process.env.NODE_ENV === 'development') {
    console.group('🍪 Auth Cookies Debug');
    const authData = getAuthCookies();
    console.log('Token:', authData.token ? '✅ Present' : '❌ Missing');
    console.log('User:', authData.user ? '✅ Present' : '❌ Missing');
    console.log('Has Completed Order:', authData.hasCompletedOrder ? '✅ Yes' : '❌ No');
    console.log('Completed Order Info:', authData.completedOrderInfo ? '✅ Present' : '❌ Missing');
    console.log('Full Auth Data:', authData);
    console.groupEnd();
  }
};

const cookieHelper = {
  clearAuthCookies,
  setAuthCookies,
  getAuthCookies,
  isAuthenticated,
  getToken,
  getUser,
  hasCompletedOrder,
  getCompletedOrderInfo,
  updateUserCookie,
  updateOrderStatus,
  debugAuthCookies,
  AUTH_COOKIES
};

export default cookieHelper;
