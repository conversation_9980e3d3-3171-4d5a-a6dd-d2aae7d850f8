/**
 * Media URL utilities for Strapi CMS integration
 * Handles URL formatting and resolution for media assets
 */

/**
 * Convert Strapi media URL to full absolute URL
 * Handles both relative paths from Strapi and already absolute URLs
 * 
 * @param {string|null|undefined} url - The media URL from Strapi (can be relative or absolute)
 * @returns {string} Full absolute URL for the media asset, or empty string if no URL provided. Requires the `NEXT_PUBLIC_STRAPI_URL` environment variable to be set for relative paths.
 * 
 * @example
 * // Convert relative Strapi URL to absolute
 * getStrapiMedia('/uploads/image.jpg'); 
 * // returns "http://localhost:1337/uploads/image.jpg"
 * 
 * @example
 * // Return absolute URL as-is
 * getStrapiMedia('https://cdn.example.com/image.jpg'); 
 * // returns "https://cdn.example.com/image.jpg"
 * 
 * @example
 * // Handle empty/null values
 * getStrapiMedia(null); // returns ""
 * getStrapiMedia(""); // returns ""
 */
export const getStrapiMedia = (url) => {
    if (!url) return "";

    // Nếu URL đã là đường dẫn đầy đủ
    if (url.startsWith("http://") || url.startsWith("https://")) {
        return url;
    }

    // Nếu không, thêm base URL
    return process.env.NEXT_PUBLIC_STRAPI_URL + url;
};