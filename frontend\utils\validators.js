/**
 * Validates Gmail addresses with specific constraints
 * - Local part: 6–30 chars, lowercase alphanumeric and dots, no consecutive dots
 * - Domain: gmail.com
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid Gmail address
 */
export function isValidGmail(email) {
  if (!email || typeof email !== "string") return false;
  const trimmed = email.trim().toLowerCase();
  const match = trimmed.match(
    /^([a-z0-9](?:[a-z0-9.]{4,28}[a-z0-9]))@gmail\.com$/
  );
  if (!match) return false;
  const baseUser = match[1];
  if (baseUser.includes("..")) return false; // no two periods in a row
  return true;
}

/**
 * Validates Vietnamese phone numbers
 * Accepts phone numbers starting with 03, 05, 07, 08, 09 followed by 8 digits
 * @param {string} phone - Phone number to validate
 * @returns {boolean} - True if valid Vietnamese phone number
 */
export function isValidPhoneNumber(phone) {
  if (!phone || typeof phone !== "string") return false;
  const trimmed = phone.trim();
  const phoneRegex = /^(0[35789])([0-9]{8})$/;
  return phoneRegex.test(trimmed);
}