/**
 * Utility functions để xử lý timezone Vietnam (UTC+7) một cách nhất quán
 * <PERSON><PERSON><PERSON> bảo tất cả date operations đều sử dụng múi giờ Việt Nam
 */

/**
 * <PERSON><PERSON><PERSON> ngày hiện tại theo múi giờ Việt Nam (UTC+7)
 * Sử dụng Intl.DateTimeFormat để đảm bảo consistency
 * @returns {Date} Date object theo múi giờ Việt Nam
 */
export const getVietnamDate = (date = new Date()) => {
  // Sử dụng Intl.DateTimeFormat để đảm bảo consistent timezone handling
  const vietnamFormatter = new Intl.DateTimeFormat('sv-SE', {
    timeZone: 'Asia/Ho_Chi_Minh',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  const parts = vietnamFormatter.formatToParts(date);
  const year = parts.find(p => p.type === 'year').value;
  const month = parts.find(p => p.type === 'month').value;
  const day = parts.find(p => p.type === 'day').value;
  const hour = parts.find(p => p.type === 'hour').value;
  const minute = parts.find(p => p.type === 'minute').value;
  const second = parts.find(p => p.type === 'second').value;

  // Tạo Date object với timezone Việt Nam
  return new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}+07:00`);
};

/**
 * Format ngày thành chuỗi YYYY-MM-DD theo múi giờ Việt Nam
 * @param {Date} date - Date object cần format (optional, mặc định là hôm nay)
 * @returns {string} Chuỗi ngày định dạng YYYY-MM-DD
 */
export const formatVietnamDate = (date = null) => {
  const targetDate = date || getVietnamDate();

  // Sử dụng Intl.DateTimeFormat để đảm bảo consistency
  const vietnamFormatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: 'Asia/Ho_Chi_Minh',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });

  const parts = vietnamFormatter.formatToParts(targetDate);
  const year = parts.find(p => p.type === 'year').value;
  const month = parts.find(p => p.type === 'month').value;
  const day = parts.find(p => p.type === 'day').value;

  return `${year}-${month}-${day}`;
};

/**
 * Lấy tên thứ trong tuần theo múi giờ Việt Nam
 * @param {Date} date - Date object (optional, mặc định là hôm nay)
 * @returns {string} Tên thứ trong tuần (VD: "Thứ 2", "Thứ 3", ...)
 */
export const getVietnamDayOfWeek = (date = null) => {
  const targetDate = date || getVietnamDate();
  const vietnamDate = new Date(targetDate.toLocaleString("en-US", {timeZone: "Asia/Ho_Chi_Minh"}));
  
  const days = ["Chủ nhật", "Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7"];
  return days[vietnamDate.getDay()];
};

/**
 * Kiểm tra xem có phải giờ đóng streak không (23:45 - 08:30)
 * @returns {boolean} True nếu đang trong giờ đóng streak
 */
export const isStreakClosedTime = () => {
  const vietnamDate = getVietnamDate();
  const hours = vietnamDate.getHours();
  const minutes = vietnamDate.getMinutes();
  
  // Đóng từ 23:45 đến 08:30 hôm sau
  return (hours === 23 && minutes >= 45) || (hours >= 0 && hours < 8) || (hours === 8 && minutes < 30);
};

/**
 * Tạo Date object từ chuỗi date theo múi giờ Việt Nam
 * @param {string} dateString - Chuỗi ngày định dạng YYYY-MM-DD
 * @returns {Date} Date object theo múi giờ Việt Nam
 */
export const parseVietnamDate = (dateString) => {
  // Parse date string and create Date object with Vietnam timezone
  const [year, month, day] = dateString.split('-');

  // Create date string with Vietnam timezone explicitly
  const vietnamDateString = `${year}-${month}-${day}T00:00:00+07:00`;

  return new Date(vietnamDateString);
};

/**
 * Validate timezone consistency giữa client và server
 * @param {string} serverDate - Date string từ server
 * @param {string} clientDate - Date string từ client
 * @returns {boolean} True nếu consistent
 */
export const validateTimezoneConsistency = (serverDate, clientDate) => {
  const server = new Date(serverDate);
  const client = parseVietnamDate(clientDate);

  // So sánh date part only (ignore time)
  const serverDateOnly = server.toISOString().split('T')[0];
  const clientDateOnly = client.toISOString().split('T')[0];

  return serverDateOnly === clientDateOnly;
};

/**
 * Get timezone offset in minutes for Vietnam
 * @returns {number} Offset in minutes
 */
export const getVietnamTimezoneOffset = () => {
  return 7 * 60; // UTC+7 = +420 minutes
};

/**
 * Convert UTC date to Vietnam date consistently
 * @param {Date} utcDate - UTC Date object
 * @returns {Date} Vietnam Date object
 */
export const utcToVietnamDate = (utcDate) => {
  const vietnamTime = new Date(utcDate.getTime() + (7 * 60 * 60 * 1000));
  return vietnamTime;
};

/**
 * Convert Vietnam date to UTC date consistently
 * @param {Date} vietnamDate - Vietnam Date object
 * @returns {Date} UTC Date object
 */
export const vietnamToUtcDate = (vietnamDate) => {
  const utcTime = new Date(vietnamDate.getTime() - (7 * 60 * 60 * 1000));
  return utcTime;
};

/**
 * Log debug thông tin về timezone để troubleshoot
 */
export const debugTimezone = () => {
  const now = new Date();
  const vietnamDate = getVietnamDate();

  console.log('=== TIMEZONE DEBUG ===');
  console.log('Local Time:', now.toString());
  console.log('Local Time (ISO):', now.toISOString());
  console.log('Vietnam Time:', vietnamDate.toString());
  console.log('Vietnam Time (ISO):', vietnamDate.toISOString());
  console.log('Vietnam Date (formatted):', formatVietnamDate());
  console.log('Vietnam Day of Week:', getVietnamDayOfWeek());
  console.log('Is Streak Closed Time:', isStreakClosedTime());
  console.log('Timezone Offset:', getVietnamTimezoneOffset(), 'minutes');
  console.log('======================');
};